@echo off
chcp 65001 >nul
title MySkin.ai Skin Analysis Tool

echo.
echo ========================================
echo    MySkin.ai Skin Analysis Tool
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python 3.7 or higher
    pause
    exit /b 1
)

REM Use virtual environment Python if exists
if exist "venv\Scripts\python.exe" (
    echo Using virtual environment Python...
    set PYTHON_CMD=venv\Scripts\python.exe
) else (
    echo Using system Python...
    set PYTHON_CMD=python
)

REM Check required dependencies
echo Checking dependencies...
%PYTHON_CMD% -c "import cv2, mediapipe, tensorflow, PIL, skimage" >nul 2>&1
if errorlevel 1 (
    echo Missing required dependencies
    echo Installing dependencies...
    %PYTHON_CMD% -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Dependency installation failed
        pause
        exit /b 1
    )
)

REM Check model file
if not exist "more_data(3).h5" (
    echo Error: Model file more_data(3).h5 not found
    echo Please ensure the model file is in the current directory
    pause
    exit /b 1
)

echo Environment check completed
echo.

REM Run skin analysis tool
if "%~1"=="" (
    echo Usage Instructions:
    echo 1. Drag and drop image file onto this batch file
    echo 2. Or run this file directly and enter image path
    echo.
    %PYTHON_CMD% skin_analyzer_cli.py
) else (
    echo Analyzing image: %~1
    %PYTHON_CMD% skin_analyzer_cli.py "%~1"
)

echo.
echo Thank you for using MySkin.ai Skin Analysis Tool!
pause
