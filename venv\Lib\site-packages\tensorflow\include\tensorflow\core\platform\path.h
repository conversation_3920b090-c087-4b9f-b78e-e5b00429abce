/* Copyright 2015 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_PLATFORM_PATH_H_
#define TENSORFLOW_CORE_PLATFORM_PATH_H_

#include "tensorflow/core/platform/stringpiece.h"
#include "tensorflow/core/platform/types.h"
#include "tsl/platform/path.h"

// NOLINTBEGIN(misc-unused-using-decls)
namespace tensorflow {
namespace io {
namespace internal {
using tsl::io::internal::JoinPathImpl;
}
#ifndef SWIG  // variadic templates
using tsl::io::JoinPath;
#endif /* SWIG */
using tsl::io::Basename;
using tsl::io::BasenamePrefix;
using tsl::io::CleanPath;
using tsl::io::CommonPathPrefix;
using tsl::io::CreateURI;
using tsl::io::Dirname;
using tsl::io::Extension;
using tsl::io::GetTempFilename;
using tsl::io::GetTestUndeclaredOutputsDir;
using tsl::io::IsAbsolutePath;
using tsl::io::ParseURI;
}  // namespace io
}  // namespace tensorflow
// NOLINTEND(misc-unused-using-decls)

#endif  // TENSORFLOW_CORE_PLATFORM_PATH_H_
