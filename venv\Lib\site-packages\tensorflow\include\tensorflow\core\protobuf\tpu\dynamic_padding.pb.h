// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/dynamic_padding.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto;
namespace tensorflow {
namespace tpu {
class PaddingMap;
struct PaddingMapDefaultTypeInternal;
extern PaddingMapDefaultTypeInternal _PaddingMap_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::PaddingMap* Arena::CreateMaybeMessage<::tensorflow::tpu::PaddingMap>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

// ===================================================================

class PaddingMap final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.PaddingMap) */ {
 public:
  inline PaddingMap() : PaddingMap(nullptr) {}
  ~PaddingMap() override;
  explicit PROTOBUF_CONSTEXPR PaddingMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PaddingMap(const PaddingMap& from);
  PaddingMap(PaddingMap&& from) noexcept
    : PaddingMap() {
    *this = ::std::move(from);
  }

  inline PaddingMap& operator=(const PaddingMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline PaddingMap& operator=(PaddingMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PaddingMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const PaddingMap* internal_default_instance() {
    return reinterpret_cast<const PaddingMap*>(
               &_PaddingMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PaddingMap& a, PaddingMap& b) {
    a.Swap(&b);
  }
  inline void Swap(PaddingMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PaddingMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PaddingMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PaddingMap>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PaddingMap& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PaddingMap& from) {
    PaddingMap::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PaddingMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.PaddingMap";
  }
  protected:
  explicit PaddingMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kArgIndexFieldNumber = 1,
    kShapeIndexFieldNumber = 2,
    kPaddingArgIndexFieldNumber = 3,
  };
  // int32 arg_index = 1;
  void clear_arg_index();
  int32_t arg_index() const;
  void set_arg_index(int32_t value);
  private:
  int32_t _internal_arg_index() const;
  void _internal_set_arg_index(int32_t value);
  public:

  // int32 shape_index = 2;
  void clear_shape_index();
  int32_t shape_index() const;
  void set_shape_index(int32_t value);
  private:
  int32_t _internal_shape_index() const;
  void _internal_set_shape_index(int32_t value);
  public:

  // int32 padding_arg_index = 3;
  void clear_padding_arg_index();
  int32_t padding_arg_index() const;
  void set_padding_arg_index(int32_t value);
  private:
  int32_t _internal_padding_arg_index() const;
  void _internal_set_padding_arg_index(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.PaddingMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t arg_index_;
    int32_t shape_index_;
    int32_t padding_arg_index_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PaddingMap

// int32 arg_index = 1;
inline void PaddingMap::clear_arg_index() {
  _impl_.arg_index_ = 0;
}
inline int32_t PaddingMap::_internal_arg_index() const {
  return _impl_.arg_index_;
}
inline int32_t PaddingMap::arg_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.PaddingMap.arg_index)
  return _internal_arg_index();
}
inline void PaddingMap::_internal_set_arg_index(int32_t value) {
  
  _impl_.arg_index_ = value;
}
inline void PaddingMap::set_arg_index(int32_t value) {
  _internal_set_arg_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.PaddingMap.arg_index)
}

// int32 shape_index = 2;
inline void PaddingMap::clear_shape_index() {
  _impl_.shape_index_ = 0;
}
inline int32_t PaddingMap::_internal_shape_index() const {
  return _impl_.shape_index_;
}
inline int32_t PaddingMap::shape_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.PaddingMap.shape_index)
  return _internal_shape_index();
}
inline void PaddingMap::_internal_set_shape_index(int32_t value) {
  
  _impl_.shape_index_ = value;
}
inline void PaddingMap::set_shape_index(int32_t value) {
  _internal_set_shape_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.PaddingMap.shape_index)
}

// int32 padding_arg_index = 3;
inline void PaddingMap::clear_padding_arg_index() {
  _impl_.padding_arg_index_ = 0;
}
inline int32_t PaddingMap::_internal_padding_arg_index() const {
  return _impl_.padding_arg_index_;
}
inline int32_t PaddingMap::padding_arg_index() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.PaddingMap.padding_arg_index)
  return _internal_padding_arg_index();
}
inline void PaddingMap::_internal_set_padding_arg_index(int32_t value) {
  
  _impl_.padding_arg_index_ = value;
}
inline void PaddingMap::set_padding_arg_index(int32_t value) {
  _internal_set_padding_arg_index(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.PaddingMap.padding_arg_index)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2fdynamic_5fpadding_2eproto
