// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tensorflow/core/protobuf/eager_service.proto
#ifndef GRPC_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto__INCLUDED
#define GRPC_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto__INCLUDED

#include "tensorflow/core/protobuf/eager_service.pb.h"

#include <functional>
#include <grpc/impl/codegen/port_platform.h>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace tensorflow {
namespace eager {


namespace grpc {

// //////////////////////////////////////////////////////////////////////////////
//
// Eager Service defines a TensorFlow service that executes operations eagerly
// on a set of local devices, on behalf of a remote Eager executor.
//
// The service impl will keep track of the various clients and devices it has
// access to and allows the client to enqueue ops on any devices that it is able
// to access and schedule data transfers from/to any of the peers.
//
// A client can generate multiple contexts to be able to independently execute
// operations, but cannot share data between the two contexts.
//
// NOTE: Even though contexts generated by clients should be independent, the
// lower level tensorflow execution engine is not, so they might share some data
// (e.g. a Device's ResourceMgr).
//
// //////////////////////////////////////////////////////////////////////////////
class EagerService final {
 public:
  static constexpr char const* service_full_name() {
    return "tensorflow.eager.EagerService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // This initializes the worker, informing it about the other workers in the
    // cluster and exchanging authentication tokens which will be used in all
    // other RPCs to detect whether the worker has restarted.
    virtual ::grpc::Status CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::tensorflow::eager::CreateContextResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>> AsyncCreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>>(AsyncCreateContextRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>> PrepareAsyncCreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>>(PrepareAsyncCreateContextRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // This updates the eager context on an existing worker when updating the set
    // of servers in a distributed eager cluster.
    virtual ::grpc::Status UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::tensorflow::eager::UpdateContextResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>> AsyncUpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>>(AsyncUpdateContextRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>> PrepareAsyncUpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>>(PrepareAsyncUpdateContextRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // This takes a list of Execute and DeleteTensorHandle operations and enqueues
    // (in async mode) or executes (in sync mode) them on the remote server.
    // All outputs of ops which were not explicitly deleted with
    // DeleteTensorHandle entries will be assumed to be alive and are usable by
    // future calls to Enqueue.
    virtual ::grpc::Status Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::tensorflow::eager::EnqueueResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>> AsyncEnqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>>(AsyncEnqueueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>> PrepareAsyncEnqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>>(PrepareAsyncEnqueueRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // A streaming version of Enqueue.
    // Current server implementation sends one response per received request.
    // The benefit for using a streaming version is that subsequent requests
    // can be sent without waiting for a response to the previous request. This
    // synchronization is required in the regular Enqueue call because gRPC does
    // not guarantee to preserve request order.
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>> StreamingEnqueue(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>>(StreamingEnqueueRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>> AsyncStreamingEnqueue(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>>(AsyncStreamingEnqueueRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>> PrepareAsyncStreamingEnqueue(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>>(PrepareAsyncStreamingEnqueueRaw(context, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Takes a set of op IDs and waits until those ops are done. Returns any error
    // in the stream so far.
    virtual ::grpc::Status WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::tensorflow::eager::WaitQueueDoneResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>> AsyncWaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>>(AsyncWaitQueueDoneRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>> PrepareAsyncWaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>>(PrepareAsyncWaitQueueDoneRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // This takes an Eager operation and executes it in async mode on the remote
    // server. Different from EnqueueRequest, ops/functions sent through this
    // type of requests are allowed to execute in parallel and no ordering is
    // preserved by RPC stream or executor.
    // This request type should only be used for executing component functions.
    // Ordering of component functions should be enforced by their corresponding
    // main functions. The runtime ensures the following invarients for component
    // functions (CFs) and their main functions (MFs):
    // (1) MF1 -> MF2 ==> CF1 -> CF2 ("->" indicates order of execution);
    // (2) MF1 || MF2 ==> CF1 || CF2 ("||" indicates possible parallel execution);
    // (3) For CF1 and CF2 that come from the same MF, CF1 || CF2
    // For executing ops/main functions, use Enqueue or StreamingEnqueue instead
    // for correct ordering.
    virtual ::grpc::Status RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::tensorflow::eager::RunComponentFunctionResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>> AsyncRunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>>(AsyncRunComponentFunctionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>> PrepareAsyncRunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>>(PrepareAsyncRunComponentFunctionRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Contexts are always created with a deadline and no RPCs within a deadline
    // will trigger a context garbage collection. KeepAlive calls can be used to
    // delay this. It can also be used to validate the existence of a context ID
    // on remote eager worker. If the context is on remote worker, return the same
    // ID and the current context view ID. This is useful for checking if the
    // remote worker (potentially with the same task name and hostname / port) is
    // replaced with a new process.
    virtual ::grpc::Status KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::tensorflow::eager::KeepAliveResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>> AsyncKeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>>(AsyncKeepAliveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>> PrepareAsyncKeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>>(PrepareAsyncKeepAliveRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    // Closes the context. No calls to other methods using the existing context ID
    // are valid after this.
    virtual ::grpc::Status CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::tensorflow::eager::CloseContextResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>> AsyncCloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>>(AsyncCloseContextRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>> PrepareAsyncCloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>>(PrepareAsyncCloseContextRaw(context, request, cq));
    }
    // [AUTOMATION]: Internal rpc option goes here.
    class experimental_async_interface {
     public:
      virtual ~experimental_async_interface() {}
      // This initializes the worker, informing it about the other workers in the
      // cluster and exchanging authentication tokens which will be used in all
      // other RPCs to detect whether the worker has restarted.
      virtual void CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // This updates the eager context on an existing worker when updating the set
      // of servers in a distributed eager cluster.
      virtual void UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // This takes a list of Execute and DeleteTensorHandle operations and enqueues
      // (in async mode) or executes (in sync mode) them on the remote server.
      // All outputs of ops which were not explicitly deleted with
      // DeleteTensorHandle entries will be assumed to be alive and are usable by
      // future calls to Enqueue.
      virtual void Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // A streaming version of Enqueue.
      // Current server implementation sends one response per received request.
      // The benefit for using a streaming version is that subsequent requests
      // can be sent without waiting for a response to the previous request. This
      // synchronization is required in the regular Enqueue call because gRPC does
      // not guarantee to preserve request order.
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void StreamingEnqueue(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>* reactor) = 0;
      #else
      virtual void StreamingEnqueue(::grpc::ClientContext* context, ::grpc::experimental::ClientBidiReactor< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Takes a set of op IDs and waits until those ops are done. Returns any error
      // in the stream so far.
      virtual void WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // This takes an Eager operation and executes it in async mode on the remote
      // server. Different from EnqueueRequest, ops/functions sent through this
      // type of requests are allowed to execute in parallel and no ordering is
      // preserved by RPC stream or executor.
      // This request type should only be used for executing component functions.
      // Ordering of component functions should be enforced by their corresponding
      // main functions. The runtime ensures the following invarients for component
      // functions (CFs) and their main functions (MFs):
      // (1) MF1 -> MF2 ==> CF1 -> CF2 ("->" indicates order of execution);
      // (2) MF1 || MF2 ==> CF1 || CF2 ("||" indicates possible parallel execution);
      // (3) For CF1 and CF2 that come from the same MF, CF1 || CF2
      // For executing ops/main functions, use Enqueue or StreamingEnqueue instead
      // for correct ordering.
      virtual void RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Contexts are always created with a deadline and no RPCs within a deadline
      // will trigger a context garbage collection. KeepAlive calls can be used to
      // delay this. It can also be used to validate the existence of a context ID
      // on remote eager worker. If the context is on remote worker, return the same
      // ID and the current context view ID. This is useful for checking if the
      // remote worker (potentially with the same task name and hostname / port) is
      // replaced with a new process.
      virtual void KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
      // Closes the context. No calls to other methods using the existing context ID
      // are valid after this.
      virtual void CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, std::function<void(::grpc::Status)>) = 0;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      virtual void CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      #else
      virtual void CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) = 0;
      #endif
      // [AUTOMATION]: Internal rpc option goes here.
    };
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    typedef class experimental_async_interface async_interface;
    #endif
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    async_interface* async() { return experimental_async(); }
    #endif
    virtual class experimental_async_interface* experimental_async() { return nullptr; }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>* AsyncCreateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CreateContextResponse>* PrepareAsyncCreateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>* AsyncUpdateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::UpdateContextResponse>* PrepareAsyncUpdateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>* AsyncEnqueueRaw(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::EnqueueResponse>* PrepareAsyncEnqueueRaw(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* StreamingEnqueueRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* AsyncStreamingEnqueueRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* PrepareAsyncStreamingEnqueueRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>* AsyncWaitQueueDoneRaw(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::WaitQueueDoneResponse>* PrepareAsyncWaitQueueDoneRaw(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>* AsyncRunComponentFunctionRaw(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::RunComponentFunctionResponse>* PrepareAsyncRunComponentFunctionRaw(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>* AsyncKeepAliveRaw(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::KeepAliveResponse>* PrepareAsyncKeepAliveRaw(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>* AsyncCloseContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::eager::CloseContextResponse>* PrepareAsyncCloseContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::tensorflow::eager::CreateContextResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>> AsyncCreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>>(AsyncCreateContextRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>> PrepareAsyncCreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>>(PrepareAsyncCreateContextRaw(context, request, cq));
    }
    ::grpc::Status UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::tensorflow::eager::UpdateContextResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>> AsyncUpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>>(AsyncUpdateContextRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>> PrepareAsyncUpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>>(PrepareAsyncUpdateContextRaw(context, request, cq));
    }
    ::grpc::Status Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::tensorflow::eager::EnqueueResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>> AsyncEnqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>>(AsyncEnqueueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>> PrepareAsyncEnqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>>(PrepareAsyncEnqueueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>> StreamingEnqueue(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>>(StreamingEnqueueRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>> AsyncStreamingEnqueue(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>>(AsyncStreamingEnqueueRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>> PrepareAsyncStreamingEnqueue(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>>(PrepareAsyncStreamingEnqueueRaw(context, cq));
    }
    ::grpc::Status WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::tensorflow::eager::WaitQueueDoneResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>> AsyncWaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>>(AsyncWaitQueueDoneRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>> PrepareAsyncWaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>>(PrepareAsyncWaitQueueDoneRaw(context, request, cq));
    }
    ::grpc::Status RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::tensorflow::eager::RunComponentFunctionResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>> AsyncRunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>>(AsyncRunComponentFunctionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>> PrepareAsyncRunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>>(PrepareAsyncRunComponentFunctionRaw(context, request, cq));
    }
    ::grpc::Status KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::tensorflow::eager::KeepAliveResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>> AsyncKeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>>(AsyncKeepAliveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>> PrepareAsyncKeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>>(PrepareAsyncKeepAliveRaw(context, request, cq));
    }
    ::grpc::Status CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::tensorflow::eager::CloseContextResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>> AsyncCloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>>(AsyncCloseContextRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>> PrepareAsyncCloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>>(PrepareAsyncCloseContextRaw(context, request, cq));
    }
    class experimental_async final :
      public StubInterface::experimental_async_interface {
     public:
      void CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, std::function<void(::grpc::Status)>) override;
      void CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void CreateContext(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void CreateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CreateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, std::function<void(::grpc::Status)>) override;
      void UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void UpdateContext(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void UpdateContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::UpdateContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, std::function<void(::grpc::Status)>) override;
      void Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Enqueue(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void Enqueue(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::EnqueueResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void StreamingEnqueue(::grpc::ClientContext* context, ::grpc::ClientBidiReactor< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>* reactor) override;
      #else
      void StreamingEnqueue(::grpc::ClientContext* context, ::grpc::experimental::ClientBidiReactor< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>* reactor) override;
      #endif
      void WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, std::function<void(::grpc::Status)>) override;
      void WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void WaitQueueDone(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void WaitQueueDone(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::WaitQueueDoneResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, std::function<void(::grpc::Status)>) override;
      void RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void RunComponentFunction(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void RunComponentFunction(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::RunComponentFunctionResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, std::function<void(::grpc::Status)>) override;
      void KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void KeepAlive(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void KeepAlive(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::KeepAliveResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      void CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, std::function<void(::grpc::Status)>) override;
      void CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, std::function<void(::grpc::Status)>) override;
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void CloseContext(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
      #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      void CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      #else
      void CloseContext(::grpc::ClientContext* context, const ::grpc::ByteBuffer* request, ::tensorflow::eager::CloseContextResponse* response, ::grpc::experimental::ClientUnaryReactor* reactor) override;
      #endif
     private:
      friend class Stub;
      explicit experimental_async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class experimental_async_interface* experimental_async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class experimental_async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>* AsyncCreateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CreateContextResponse>* PrepareAsyncCreateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CreateContextRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>* AsyncUpdateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::UpdateContextResponse>* PrepareAsyncUpdateContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::UpdateContextRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>* AsyncEnqueueRaw(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::EnqueueResponse>* PrepareAsyncEnqueueRaw(::grpc::ClientContext* context, const ::tensorflow::eager::EnqueueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* StreamingEnqueueRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* AsyncStreamingEnqueueRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* PrepareAsyncStreamingEnqueueRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>* AsyncWaitQueueDoneRaw(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::WaitQueueDoneResponse>* PrepareAsyncWaitQueueDoneRaw(::grpc::ClientContext* context, const ::tensorflow::eager::WaitQueueDoneRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>* AsyncRunComponentFunctionRaw(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::RunComponentFunctionResponse>* PrepareAsyncRunComponentFunctionRaw(::grpc::ClientContext* context, const ::tensorflow::eager::RunComponentFunctionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>* AsyncKeepAliveRaw(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::KeepAliveResponse>* PrepareAsyncKeepAliveRaw(::grpc::ClientContext* context, const ::tensorflow::eager::KeepAliveRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>* AsyncCloseContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::tensorflow::eager::CloseContextResponse>* PrepareAsyncCloseContextRaw(::grpc::ClientContext* context, const ::tensorflow::eager::CloseContextRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_CreateContext_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateContext_;
    const ::grpc::internal::RpcMethod rpcmethod_Enqueue_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingEnqueue_;
    const ::grpc::internal::RpcMethod rpcmethod_WaitQueueDone_;
    const ::grpc::internal::RpcMethod rpcmethod_RunComponentFunction_;
    const ::grpc::internal::RpcMethod rpcmethod_KeepAlive_;
    const ::grpc::internal::RpcMethod rpcmethod_CloseContext_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // This initializes the worker, informing it about the other workers in the
    // cluster and exchanging authentication tokens which will be used in all
    // other RPCs to detect whether the worker has restarted.
    virtual ::grpc::Status CreateContext(::grpc::ServerContext* context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // This updates the eager context on an existing worker when updating the set
    // of servers in a distributed eager cluster.
    virtual ::grpc::Status UpdateContext(::grpc::ServerContext* context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // This takes a list of Execute and DeleteTensorHandle operations and enqueues
    // (in async mode) or executes (in sync mode) them on the remote server.
    // All outputs of ops which were not explicitly deleted with
    // DeleteTensorHandle entries will be assumed to be alive and are usable by
    // future calls to Enqueue.
    virtual ::grpc::Status Enqueue(::grpc::ServerContext* context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // A streaming version of Enqueue.
    // Current server implementation sends one response per received request.
    // The benefit for using a streaming version is that subsequent requests
    // can be sent without waiting for a response to the previous request. This
    // synchronization is required in the regular Enqueue call because gRPC does
    // not guarantee to preserve request order.
    virtual ::grpc::Status StreamingEnqueue(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* stream);
    // [AUTOMATION]: Internal rpc option goes here.
    // Takes a set of op IDs and waits until those ops are done. Returns any error
    // in the stream so far.
    virtual ::grpc::Status WaitQueueDone(::grpc::ServerContext* context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // This takes an Eager operation and executes it in async mode on the remote
    // server. Different from EnqueueRequest, ops/functions sent through this
    // type of requests are allowed to execute in parallel and no ordering is
    // preserved by RPC stream or executor.
    // This request type should only be used for executing component functions.
    // Ordering of component functions should be enforced by their corresponding
    // main functions. The runtime ensures the following invarients for component
    // functions (CFs) and their main functions (MFs):
    // (1) MF1 -> MF2 ==> CF1 -> CF2 ("->" indicates order of execution);
    // (2) MF1 || MF2 ==> CF1 || CF2 ("||" indicates possible parallel execution);
    // (3) For CF1 and CF2 that come from the same MF, CF1 || CF2
    // For executing ops/main functions, use Enqueue or StreamingEnqueue instead
    // for correct ordering.
    virtual ::grpc::Status RunComponentFunction(::grpc::ServerContext* context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Contexts are always created with a deadline and no RPCs within a deadline
    // will trigger a context garbage collection. KeepAlive calls can be used to
    // delay this. It can also be used to validate the existence of a context ID
    // on remote eager worker. If the context is on remote worker, return the same
    // ID and the current context view ID. This is useful for checking if the
    // remote worker (potentially with the same task name and hostname / port) is
    // replaced with a new process.
    virtual ::grpc::Status KeepAlive(::grpc::ServerContext* context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
    // Closes the context. No calls to other methods using the existing context ID
    // are valid after this.
    virtual ::grpc::Status CloseContext(::grpc::ServerContext* context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response);
    // [AUTOMATION]: Internal rpc option goes here.
  };
  template <class BaseClass>
  class WithAsyncMethod_CreateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CreateContext() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_CreateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateContext(::grpc::ServerContext* context, ::tensorflow::eager::CreateContextRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::CreateContextResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateContext() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_UpdateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateContext(::grpc::ServerContext* context, ::tensorflow::eager::UpdateContextRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::UpdateContextResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Enqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Enqueue() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_Enqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Enqueue(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEnqueue(::grpc::ServerContext* context, ::tensorflow::eager::EnqueueRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::EnqueueResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingEnqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StreamingEnqueue() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_StreamingEnqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingEnqueue(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingEnqueue(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(3, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_WaitQueueDone : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_WaitQueueDone() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_WaitQueueDone() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitQueueDone(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestWaitQueueDone(::grpc::ServerContext* context, ::tensorflow::eager::WaitQueueDoneRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::WaitQueueDoneResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RunComponentFunction : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RunComponentFunction() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_RunComponentFunction() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunComponentFunction(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRunComponentFunction(::grpc::ServerContext* context, ::tensorflow::eager::RunComponentFunctionRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::RunComponentFunctionResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_KeepAlive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_KeepAlive() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_KeepAlive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status KeepAlive(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestKeepAlive(::grpc::ServerContext* context, ::tensorflow::eager::KeepAliveRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::KeepAliveResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CloseContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CloseContext() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_CloseContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CloseContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCloseContext(::grpc::ServerContext* context, ::tensorflow::eager::CloseContextRequest* request, ::grpc::ServerAsyncResponseWriter< ::tensorflow::eager::CloseContextResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_CreateContext<WithAsyncMethod_UpdateContext<WithAsyncMethod_Enqueue<WithAsyncMethod_StreamingEnqueue<WithAsyncMethod_WaitQueueDone<WithAsyncMethod_RunComponentFunction<WithAsyncMethod_KeepAlive<WithAsyncMethod_CloseContext<Service > > > > > > > > AsyncService;
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_CreateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_CreateContext() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::CreateContextRequest, ::tensorflow::eager::CreateContextResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::CreateContextRequest* request, ::tensorflow::eager::CreateContextResponse* response) { return this->CreateContext(context, request, response); }));}
    void SetMessageAllocatorFor_CreateContext(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::CreateContextRequest, ::tensorflow::eager::CreateContextResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(0);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::CreateContextRequest, ::tensorflow::eager::CreateContextResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_CreateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* CreateContext(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* CreateContext(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_UpdateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_UpdateContext() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::UpdateContextRequest, ::tensorflow::eager::UpdateContextResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::UpdateContextRequest* request, ::tensorflow::eager::UpdateContextResponse* response) { return this->UpdateContext(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateContext(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::UpdateContextRequest, ::tensorflow::eager::UpdateContextResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(1);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::UpdateContextRequest, ::tensorflow::eager::UpdateContextResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_UpdateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* UpdateContext(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* UpdateContext(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_Enqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_Enqueue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::EnqueueRequest* request, ::tensorflow::eager::EnqueueResponse* response) { return this->Enqueue(context, request, response); }));}
    void SetMessageAllocatorFor_Enqueue(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(2);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_Enqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Enqueue(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Enqueue(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Enqueue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_StreamingEnqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_StreamingEnqueue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(3,
          new ::grpc_impl::internal::CallbackBidiHandler< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context) { return this->StreamingEnqueue(context); }));
    }
    ~ExperimentalWithCallbackMethod_StreamingEnqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingEnqueue(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerBidiReactor< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* StreamingEnqueue(
      ::grpc::CallbackServerContext* /*context*/)
    #else
    virtual ::grpc::experimental::ServerBidiReactor< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>* StreamingEnqueue(
      ::grpc::experimental::CallbackServerContext* /*context*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_WaitQueueDone : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_WaitQueueDone() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(4,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::WaitQueueDoneRequest, ::tensorflow::eager::WaitQueueDoneResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::WaitQueueDoneRequest* request, ::tensorflow::eager::WaitQueueDoneResponse* response) { return this->WaitQueueDone(context, request, response); }));}
    void SetMessageAllocatorFor_WaitQueueDone(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::WaitQueueDoneRequest, ::tensorflow::eager::WaitQueueDoneResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(4);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::WaitQueueDoneRequest, ::tensorflow::eager::WaitQueueDoneResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_WaitQueueDone() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitQueueDone(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* WaitQueueDone(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* WaitQueueDone(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_RunComponentFunction : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_RunComponentFunction() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(5,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::RunComponentFunctionRequest, ::tensorflow::eager::RunComponentFunctionResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::RunComponentFunctionRequest* request, ::tensorflow::eager::RunComponentFunctionResponse* response) { return this->RunComponentFunction(context, request, response); }));}
    void SetMessageAllocatorFor_RunComponentFunction(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::RunComponentFunctionRequest, ::tensorflow::eager::RunComponentFunctionResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(5);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::RunComponentFunctionRequest, ::tensorflow::eager::RunComponentFunctionResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_RunComponentFunction() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunComponentFunction(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* RunComponentFunction(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* RunComponentFunction(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_KeepAlive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_KeepAlive() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(6,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::KeepAliveRequest, ::tensorflow::eager::KeepAliveResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::KeepAliveRequest* request, ::tensorflow::eager::KeepAliveResponse* response) { return this->KeepAlive(context, request, response); }));}
    void SetMessageAllocatorFor_KeepAlive(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::KeepAliveRequest, ::tensorflow::eager::KeepAliveResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(6);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::KeepAliveRequest, ::tensorflow::eager::KeepAliveResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_KeepAlive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status KeepAlive(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* KeepAlive(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* KeepAlive(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithCallbackMethod_CloseContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithCallbackMethod_CloseContext() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodCallback(7,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::CloseContextRequest, ::tensorflow::eager::CloseContextResponse>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::tensorflow::eager::CloseContextRequest* request, ::tensorflow::eager::CloseContextResponse* response) { return this->CloseContext(context, request, response); }));}
    void SetMessageAllocatorFor_CloseContext(
        ::grpc::experimental::MessageAllocator< ::tensorflow::eager::CloseContextRequest, ::tensorflow::eager::CloseContextResponse>* allocator) {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
    #else
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::experimental().GetHandler(7);
    #endif
      static_cast<::grpc_impl::internal::CallbackUnaryHandler< ::tensorflow::eager::CloseContextRequest, ::tensorflow::eager::CloseContextResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~ExperimentalWithCallbackMethod_CloseContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CloseContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* CloseContext(
      ::grpc::CallbackServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* CloseContext(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/)
    #endif
      { return nullptr; }
  };
  #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
  typedef ExperimentalWithCallbackMethod_CreateContext<ExperimentalWithCallbackMethod_UpdateContext<ExperimentalWithCallbackMethod_Enqueue<ExperimentalWithCallbackMethod_StreamingEnqueue<ExperimentalWithCallbackMethod_WaitQueueDone<ExperimentalWithCallbackMethod_RunComponentFunction<ExperimentalWithCallbackMethod_KeepAlive<ExperimentalWithCallbackMethod_CloseContext<Service > > > > > > > > CallbackService;
  #endif

  typedef ExperimentalWithCallbackMethod_CreateContext<ExperimentalWithCallbackMethod_UpdateContext<ExperimentalWithCallbackMethod_Enqueue<ExperimentalWithCallbackMethod_StreamingEnqueue<ExperimentalWithCallbackMethod_WaitQueueDone<ExperimentalWithCallbackMethod_RunComponentFunction<ExperimentalWithCallbackMethod_KeepAlive<ExperimentalWithCallbackMethod_CloseContext<Service > > > > > > > > ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_CreateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CreateContext() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_CreateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateContext() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_UpdateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Enqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Enqueue() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_Enqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Enqueue(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingEnqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StreamingEnqueue() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_StreamingEnqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingEnqueue(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_WaitQueueDone : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_WaitQueueDone() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_WaitQueueDone() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitQueueDone(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RunComponentFunction : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RunComponentFunction() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_RunComponentFunction() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunComponentFunction(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_KeepAlive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_KeepAlive() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_KeepAlive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status KeepAlive(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CloseContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CloseContext() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_CloseContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CloseContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_CreateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CreateContext() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_CreateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateContext(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateContext() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_UpdateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateContext(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Enqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Enqueue() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_Enqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Enqueue(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEnqueue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingEnqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StreamingEnqueue() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_StreamingEnqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingEnqueue(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingEnqueue(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(3, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_WaitQueueDone : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_WaitQueueDone() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_WaitQueueDone() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitQueueDone(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestWaitQueueDone(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RunComponentFunction : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RunComponentFunction() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_RunComponentFunction() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunComponentFunction(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRunComponentFunction(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_KeepAlive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_KeepAlive() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_KeepAlive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status KeepAlive(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestKeepAlive(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CloseContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CloseContext() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_CloseContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CloseContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCloseContext(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_CreateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_CreateContext() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(0,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CreateContext(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_CreateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* CreateContext(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* CreateContext(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_UpdateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_UpdateContext() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(1,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateContext(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_UpdateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* UpdateContext(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* UpdateContext(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_Enqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_Enqueue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(2,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Enqueue(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_Enqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Enqueue(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* Enqueue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* Enqueue(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_StreamingEnqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_StreamingEnqueue() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(3,
          new ::grpc_impl::internal::CallbackBidiHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context) { return this->StreamingEnqueue(context); }));
    }
    ~ExperimentalWithRawCallbackMethod_StreamingEnqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingEnqueue(::grpc::ServerContext* /*context*/, ::grpc::ServerReaderWriter< ::tensorflow::eager::EnqueueResponse, ::tensorflow::eager::EnqueueRequest>* /*stream*/)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerBidiReactor< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* StreamingEnqueue(
      ::grpc::CallbackServerContext* /*context*/)
    #else
    virtual ::grpc::experimental::ServerBidiReactor< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* StreamingEnqueue(
      ::grpc::experimental::CallbackServerContext* /*context*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_WaitQueueDone : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_WaitQueueDone() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(4,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->WaitQueueDone(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_WaitQueueDone() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status WaitQueueDone(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* WaitQueueDone(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* WaitQueueDone(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_RunComponentFunction : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_RunComponentFunction() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(5,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RunComponentFunction(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_RunComponentFunction() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunComponentFunction(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* RunComponentFunction(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* RunComponentFunction(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_KeepAlive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_KeepAlive() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(6,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->KeepAlive(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_KeepAlive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status KeepAlive(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* KeepAlive(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* KeepAlive(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class ExperimentalWithRawCallbackMethod_CloseContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    ExperimentalWithRawCallbackMethod_CloseContext() {
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
      ::grpc::Service::
    #else
      ::grpc::Service::experimental().
    #endif
        MarkMethodRawCallback(7,
          new ::grpc_impl::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
                   ::grpc::CallbackServerContext*
    #else
                   ::grpc::experimental::CallbackServerContext*
    #endif
                     context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CloseContext(context, request, response); }));
    }
    ~ExperimentalWithRawCallbackMethod_CloseContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CloseContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    #ifdef GRPC_CALLBACK_API_NONEXPERIMENTAL
    virtual ::grpc::ServerUnaryReactor* CloseContext(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #else
    virtual ::grpc::experimental::ServerUnaryReactor* CloseContext(
      ::grpc::experimental::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)
    #endif
      { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CreateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CreateContext() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::CreateContextRequest, ::tensorflow::eager::CreateContextResponse>(std::bind(&WithStreamedUnaryMethod_CreateContext<BaseClass>::StreamedCreateContext, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_CreateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CreateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CreateContextRequest* /*request*/, ::tensorflow::eager::CreateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCreateContext(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::CreateContextRequest,::tensorflow::eager::CreateContextResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateContext() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::UpdateContextRequest, ::tensorflow::eager::UpdateContextResponse>(std::bind(&WithStreamedUnaryMethod_UpdateContext<BaseClass>::StreamedUpdateContext, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_UpdateContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::UpdateContextRequest* /*request*/, ::tensorflow::eager::UpdateContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateContext(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::UpdateContextRequest,::tensorflow::eager::UpdateContextResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Enqueue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Enqueue() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::EnqueueRequest, ::tensorflow::eager::EnqueueResponse>(std::bind(&WithStreamedUnaryMethod_Enqueue<BaseClass>::StreamedEnqueue, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Enqueue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Enqueue(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::EnqueueRequest* /*request*/, ::tensorflow::eager::EnqueueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedEnqueue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::EnqueueRequest,::tensorflow::eager::EnqueueResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_WaitQueueDone : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_WaitQueueDone() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::WaitQueueDoneRequest, ::tensorflow::eager::WaitQueueDoneResponse>(std::bind(&WithStreamedUnaryMethod_WaitQueueDone<BaseClass>::StreamedWaitQueueDone, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_WaitQueueDone() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status WaitQueueDone(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::WaitQueueDoneRequest* /*request*/, ::tensorflow::eager::WaitQueueDoneResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedWaitQueueDone(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::WaitQueueDoneRequest,::tensorflow::eager::WaitQueueDoneResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RunComponentFunction : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RunComponentFunction() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::RunComponentFunctionRequest, ::tensorflow::eager::RunComponentFunctionResponse>(std::bind(&WithStreamedUnaryMethod_RunComponentFunction<BaseClass>::StreamedRunComponentFunction, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_RunComponentFunction() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RunComponentFunction(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::RunComponentFunctionRequest* /*request*/, ::tensorflow::eager::RunComponentFunctionResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRunComponentFunction(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::RunComponentFunctionRequest,::tensorflow::eager::RunComponentFunctionResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_KeepAlive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_KeepAlive() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::KeepAliveRequest, ::tensorflow::eager::KeepAliveResponse>(std::bind(&WithStreamedUnaryMethod_KeepAlive<BaseClass>::StreamedKeepAlive, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_KeepAlive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status KeepAlive(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::KeepAliveRequest* /*request*/, ::tensorflow::eager::KeepAliveResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedKeepAlive(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::KeepAliveRequest,::tensorflow::eager::KeepAliveResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CloseContext : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CloseContext() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler< ::tensorflow::eager::CloseContextRequest, ::tensorflow::eager::CloseContextResponse>(std::bind(&WithStreamedUnaryMethod_CloseContext<BaseClass>::StreamedCloseContext, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_CloseContext() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CloseContext(::grpc::ServerContext* /*context*/, const ::tensorflow::eager::CloseContextRequest* /*request*/, ::tensorflow::eager::CloseContextResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCloseContext(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::tensorflow::eager::CloseContextRequest,::tensorflow::eager::CloseContextResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_CreateContext<WithStreamedUnaryMethod_UpdateContext<WithStreamedUnaryMethod_Enqueue<WithStreamedUnaryMethod_WaitQueueDone<WithStreamedUnaryMethod_RunComponentFunction<WithStreamedUnaryMethod_KeepAlive<WithStreamedUnaryMethod_CloseContext<Service > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_CreateContext<WithStreamedUnaryMethod_UpdateContext<WithStreamedUnaryMethod_Enqueue<WithStreamedUnaryMethod_WaitQueueDone<WithStreamedUnaryMethod_RunComponentFunction<WithStreamedUnaryMethod_KeepAlive<WithStreamedUnaryMethod_CloseContext<Service > > > > > > > StreamedService;
};

}  // namespace grpc

}  // namespace eager
}  // namespace tensorflow


#endif  // GRPC_tensorflow_2fcore_2fprotobuf_2feager_5fservice_2eproto__INCLUDED
