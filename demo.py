#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySkin.ai 皮膚分析工具演示腳本
展示如何使用皮膚分析器進行批量分析
"""

import os
import glob
from skin_analyzer_cli import SkinAnalyzer

def demo_analysis():
    """演示皮膚分析功能"""
    print("🎯 MySkin.ai 皮膚分析工具 - 演示模式")
    print("=" * 50)
    
    # 初始化分析器
    analyzer = SkinAnalyzer()
    
    # 查找可用的圖片文件
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
    image_files = []
    
    for ext in image_extensions:
        image_files.extend(glob.glob(ext))
    
    if not image_files:
        print("❌ 當前目錄中沒有找到圖片文件")
        print("請將圖片文件放在當前目錄中，支持的格式：.jpg, .jpeg, .png, .bmp")
        return
    
    print(f"📁 找到 {len(image_files)} 個圖片文件:")
    for i, img in enumerate(image_files, 1):
        print(f"  {i}. {img}")
    
    print("\n🔍 開始分析...")
    
    results = []
    for img_path in image_files:
        print(f"\n📸 分析圖片: {img_path}")
        
        # 執行分析
        analysis_result, landmarks_frame = analyzer.analyze_image(img_path)
        
        if analysis_result:
            # 生成輸出文件名
            base_filename = os.path.splitext(os.path.basename(img_path))[0]
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{base_filename}_{timestamp}"
            
            # 保存結果
            processed_image_path = analyzer.save_processed_image(landmarks_frame, output_filename)
            json_report_path, text_report_path = analyzer.generate_report(analysis_result, output_filename)
            
            results.append({
                'image': img_path,
                'age': analysis_result['predicted_age'],
                'wrinkles': analysis_result['scaled_scores']['wrinkles_score'],
                'spots': analysis_result['scaled_scores']['spots_score'],
                'texture': analysis_result['scaled_scores']['texture_score'],
                'processed_image': processed_image_path,
                'report': text_report_path
            })
            
            print(f"  ✅ 完成 - 年齡: {analysis_result['predicted_age']}")
        else:
            print(f"  ❌ 分析失敗")
    
    # 顯示總結
    print("\n" + "=" * 50)
    print("📊 分析總結")
    print("=" * 50)
    
    if results:
        print(f"成功分析 {len(results)} 張圖片:\n")
        
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['image']}")
            print(f"   年齡: {result['age']}")
            print(f"   皺紋: {result['wrinkles']:.2f}/100")
            print(f"   斑點: {result['spots']:.2f}/100")
            print(f"   紋理: {result['texture']:.2f}/100")
            print(f"   報告: {result['report']}")
            print()
        
        # 計算平均值
        avg_wrinkles = sum(r['wrinkles'] for r in results) / len(results)
        avg_spots = sum(r['spots'] for r in results) / len(results)
        avg_texture = sum(r['texture'] for r in results) / len(results)
        
        print("📈 平均評分:")
        print(f"   皺紋: {avg_wrinkles:.2f}/100")
        print(f"   斑點: {avg_spots:.2f}/100")
        print(f"   紋理: {avg_texture:.2f}/100")
        
    else:
        print("❌ 沒有成功分析的圖片")
    
    print(f"\n📁 所有結果已保存到 '{analyzer.output_dir}' 資料夾")

def interactive_demo():
    """互動式演示"""
    print("🎯 MySkin.ai 皮膚分析工具 - 互動演示")
    print("=" * 50)
    
    while True:
        print("\n請選擇操作:")
        print("1. 分析單張圖片")
        print("2. 批量分析所有圖片")
        print("3. 查看使用說明")
        print("4. 退出")
        
        choice = input("\n請輸入選項 (1-4): ").strip()
        
        if choice == '1':
            img_path = input("請輸入圖片路徑: ").strip('"')
            if os.path.exists(img_path):
                analyzer = SkinAnalyzer()
                analysis_result, landmarks_frame = analyzer.analyze_image(img_path)
                
                if analysis_result:
                    print(f"\n✅ 分析完成!")
                    print(f"年齡: {analysis_result['predicted_age']}")
                    print(f"皺紋: {analysis_result['scaled_scores']['wrinkles_score']:.2f}/100")
                    print(f"斑點: {analysis_result['scaled_scores']['spots_score']:.2f}/100")
                    print(f"紋理: {analysis_result['scaled_scores']['texture_score']:.2f}/100")
                else:
                    print("❌ 分析失敗")
            else:
                print("❌ 文件不存在")
        
        elif choice == '2':
            demo_analysis()
        
        elif choice == '3':
            print("\n📝 使用說明:")
            print("1. 將圖片文件拖拽到 run_skin_analysis.bat")
            print("2. 或運行: python skin_analyzer_cli.py 'image_path'")
            print("3. 或運行此演示腳本: python demo.py")
            print("\n支持的圖片格式: .jpg, .jpeg, .png, .bmp")
            print("輸出文件會保存在 'output' 資料夾中")
        
        elif choice == '4':
            print("👋 感謝使用 MySkin.ai 皮膚分析工具!")
            break
        
        else:
            print("❌ 無效選項，請重新選擇")

if __name__ == "__main__":
    try:
        interactive_demo()
    except KeyboardInterrupt:
        print("\n\n👋 程序已退出")
    except Exception as e:
        print(f"\n❌ 發生錯誤: {e}")
        input("按 Enter 鍵退出...")
