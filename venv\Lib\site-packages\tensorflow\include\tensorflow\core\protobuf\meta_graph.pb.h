// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/meta_graph.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/op_def.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/protobuf/saved_object_graph.pb.h"
#include "tensorflow/core/protobuf/saver.pb.h"
#include "tensorflow/core/protobuf/struct.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
namespace tensorflow {
class AssetFileDef;
struct AssetFileDefDefaultTypeInternal;
extern AssetFileDefDefaultTypeInternal _AssetFileDef_default_instance_;
class CollectionDef;
struct CollectionDefDefaultTypeInternal;
extern CollectionDefDefaultTypeInternal _CollectionDef_default_instance_;
class CollectionDef_AnyList;
struct CollectionDef_AnyListDefaultTypeInternal;
extern CollectionDef_AnyListDefaultTypeInternal _CollectionDef_AnyList_default_instance_;
class CollectionDef_BytesList;
struct CollectionDef_BytesListDefaultTypeInternal;
extern CollectionDef_BytesListDefaultTypeInternal _CollectionDef_BytesList_default_instance_;
class CollectionDef_FloatList;
struct CollectionDef_FloatListDefaultTypeInternal;
extern CollectionDef_FloatListDefaultTypeInternal _CollectionDef_FloatList_default_instance_;
class CollectionDef_Int64List;
struct CollectionDef_Int64ListDefaultTypeInternal;
extern CollectionDef_Int64ListDefaultTypeInternal _CollectionDef_Int64List_default_instance_;
class CollectionDef_NodeList;
struct CollectionDef_NodeListDefaultTypeInternal;
extern CollectionDef_NodeListDefaultTypeInternal _CollectionDef_NodeList_default_instance_;
class MetaGraphDef;
struct MetaGraphDefDefaultTypeInternal;
extern MetaGraphDefDefaultTypeInternal _MetaGraphDef_default_instance_;
class MetaGraphDef_CollectionDefEntry_DoNotUse;
struct MetaGraphDef_CollectionDefEntry_DoNotUseDefaultTypeInternal;
extern MetaGraphDef_CollectionDefEntry_DoNotUseDefaultTypeInternal _MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_;
class MetaGraphDef_MetaInfoDef;
struct MetaGraphDef_MetaInfoDefDefaultTypeInternal;
extern MetaGraphDef_MetaInfoDefDefaultTypeInternal _MetaGraphDef_MetaInfoDef_default_instance_;
class MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse;
struct MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUseDefaultTypeInternal;
extern MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUseDefaultTypeInternal _MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse_default_instance_;
class MetaGraphDef_SignatureDefEntry_DoNotUse;
struct MetaGraphDef_SignatureDefEntry_DoNotUseDefaultTypeInternal;
extern MetaGraphDef_SignatureDefEntry_DoNotUseDefaultTypeInternal _MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_;
class SignatureDef;
struct SignatureDefDefaultTypeInternal;
extern SignatureDefDefaultTypeInternal _SignatureDef_default_instance_;
class SignatureDef_DefaultsEntry_DoNotUse;
struct SignatureDef_DefaultsEntry_DoNotUseDefaultTypeInternal;
extern SignatureDef_DefaultsEntry_DoNotUseDefaultTypeInternal _SignatureDef_DefaultsEntry_DoNotUse_default_instance_;
class SignatureDef_InputsEntry_DoNotUse;
struct SignatureDef_InputsEntry_DoNotUseDefaultTypeInternal;
extern SignatureDef_InputsEntry_DoNotUseDefaultTypeInternal _SignatureDef_InputsEntry_DoNotUse_default_instance_;
class SignatureDef_OutputsEntry_DoNotUse;
struct SignatureDef_OutputsEntry_DoNotUseDefaultTypeInternal;
extern SignatureDef_OutputsEntry_DoNotUseDefaultTypeInternal _SignatureDef_OutputsEntry_DoNotUse_default_instance_;
class TensorInfo;
struct TensorInfoDefaultTypeInternal;
extern TensorInfoDefaultTypeInternal _TensorInfo_default_instance_;
class TensorInfo_CompositeTensor;
struct TensorInfo_CompositeTensorDefaultTypeInternal;
extern TensorInfo_CompositeTensorDefaultTypeInternal _TensorInfo_CompositeTensor_default_instance_;
class TensorInfo_CooSparse;
struct TensorInfo_CooSparseDefaultTypeInternal;
extern TensorInfo_CooSparseDefaultTypeInternal _TensorInfo_CooSparse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::AssetFileDef* Arena::CreateMaybeMessage<::tensorflow::AssetFileDef>(Arena*);
template<> ::tensorflow::CollectionDef* Arena::CreateMaybeMessage<::tensorflow::CollectionDef>(Arena*);
template<> ::tensorflow::CollectionDef_AnyList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_AnyList>(Arena*);
template<> ::tensorflow::CollectionDef_BytesList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_BytesList>(Arena*);
template<> ::tensorflow::CollectionDef_FloatList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_FloatList>(Arena*);
template<> ::tensorflow::CollectionDef_Int64List* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_Int64List>(Arena*);
template<> ::tensorflow::CollectionDef_NodeList* Arena::CreateMaybeMessage<::tensorflow::CollectionDef_NodeList>(Arena*);
template<> ::tensorflow::MetaGraphDef* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef>(Arena*);
template<> ::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_CollectionDefEntry_DoNotUse>(Arena*);
template<> ::tensorflow::MetaGraphDef_MetaInfoDef* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_MetaInfoDef>(Arena*);
template<> ::tensorflow::MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse>(Arena*);
template<> ::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::MetaGraphDef_SignatureDefEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SignatureDef* Arena::CreateMaybeMessage<::tensorflow::SignatureDef>(Arena*);
template<> ::tensorflow::SignatureDef_DefaultsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SignatureDef_DefaultsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SignatureDef_InputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SignatureDef_InputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::SignatureDef_OutputsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::SignatureDef_OutputsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::TensorInfo* Arena::CreateMaybeMessage<::tensorflow::TensorInfo>(Arena*);
template<> ::tensorflow::TensorInfo_CompositeTensor* Arena::CreateMaybeMessage<::tensorflow::TensorInfo_CompositeTensor>(Arena*);
template<> ::tensorflow::TensorInfo_CooSparse* Arena::CreateMaybeMessage<::tensorflow::TensorInfo_CooSparse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse& other);
  static const MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse*>(&_MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.MetaGraphDef.MetaInfoDef.FunctionAliasesEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.MetaGraphDef.MetaInfoDef.FunctionAliasesEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class MetaGraphDef_MetaInfoDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MetaGraphDef.MetaInfoDef) */ {
 public:
  inline MetaGraphDef_MetaInfoDef() : MetaGraphDef_MetaInfoDef(nullptr) {}
  ~MetaGraphDef_MetaInfoDef() override;
  explicit PROTOBUF_CONSTEXPR MetaGraphDef_MetaInfoDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MetaGraphDef_MetaInfoDef(const MetaGraphDef_MetaInfoDef& from);
  MetaGraphDef_MetaInfoDef(MetaGraphDef_MetaInfoDef&& from) noexcept
    : MetaGraphDef_MetaInfoDef() {
    *this = ::std::move(from);
  }

  inline MetaGraphDef_MetaInfoDef& operator=(const MetaGraphDef_MetaInfoDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline MetaGraphDef_MetaInfoDef& operator=(MetaGraphDef_MetaInfoDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MetaGraphDef_MetaInfoDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const MetaGraphDef_MetaInfoDef* internal_default_instance() {
    return reinterpret_cast<const MetaGraphDef_MetaInfoDef*>(
               &_MetaGraphDef_MetaInfoDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MetaGraphDef_MetaInfoDef& a, MetaGraphDef_MetaInfoDef& b) {
    a.Swap(&b);
  }
  inline void Swap(MetaGraphDef_MetaInfoDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MetaGraphDef_MetaInfoDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MetaGraphDef_MetaInfoDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MetaGraphDef_MetaInfoDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MetaGraphDef_MetaInfoDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MetaGraphDef_MetaInfoDef& from) {
    MetaGraphDef_MetaInfoDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MetaGraphDef_MetaInfoDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MetaGraphDef.MetaInfoDef";
  }
  protected:
  explicit MetaGraphDef_MetaInfoDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kTagsFieldNumber = 4,
    kFunctionAliasesFieldNumber = 8,
    kMetaGraphVersionFieldNumber = 1,
    kTensorflowVersionFieldNumber = 5,
    kTensorflowGitVersionFieldNumber = 6,
    kStrippedOpListFieldNumber = 2,
    kAnyInfoFieldNumber = 3,
    kStrippedDefaultAttrsFieldNumber = 7,
  };
  // repeated string tags = 4;
  int tags_size() const;
  private:
  int _internal_tags_size() const;
  public:
  void clear_tags();
  const std::string& tags(int index) const;
  std::string* mutable_tags(int index);
  void set_tags(int index, const std::string& value);
  void set_tags(int index, std::string&& value);
  void set_tags(int index, const char* value);
  void set_tags(int index, const char* value, size_t size);
  std::string* add_tags();
  void add_tags(const std::string& value);
  void add_tags(std::string&& value);
  void add_tags(const char* value);
  void add_tags(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& tags() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_tags();
  private:
  const std::string& _internal_tags(int index) const;
  std::string* _internal_add_tags();
  public:

  // map<string, string> function_aliases = 8;
  int function_aliases_size() const;
  private:
  int _internal_function_aliases_size() const;
  public:
  void clear_function_aliases();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_function_aliases() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_function_aliases();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      function_aliases() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_function_aliases();

  // string meta_graph_version = 1;
  void clear_meta_graph_version();
  const std::string& meta_graph_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_meta_graph_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_meta_graph_version();
  PROTOBUF_NODISCARD std::string* release_meta_graph_version();
  void set_allocated_meta_graph_version(std::string* meta_graph_version);
  private:
  const std::string& _internal_meta_graph_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_meta_graph_version(const std::string& value);
  std::string* _internal_mutable_meta_graph_version();
  public:

  // string tensorflow_version = 5;
  void clear_tensorflow_version();
  const std::string& tensorflow_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tensorflow_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tensorflow_version();
  PROTOBUF_NODISCARD std::string* release_tensorflow_version();
  void set_allocated_tensorflow_version(std::string* tensorflow_version);
  private:
  const std::string& _internal_tensorflow_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tensorflow_version(const std::string& value);
  std::string* _internal_mutable_tensorflow_version();
  public:

  // string tensorflow_git_version = 6;
  void clear_tensorflow_git_version();
  const std::string& tensorflow_git_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tensorflow_git_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tensorflow_git_version();
  PROTOBUF_NODISCARD std::string* release_tensorflow_git_version();
  void set_allocated_tensorflow_git_version(std::string* tensorflow_git_version);
  private:
  const std::string& _internal_tensorflow_git_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tensorflow_git_version(const std::string& value);
  std::string* _internal_mutable_tensorflow_git_version();
  public:

  // .tensorflow.OpList stripped_op_list = 2;
  bool has_stripped_op_list() const;
  private:
  bool _internal_has_stripped_op_list() const;
  public:
  void clear_stripped_op_list();
  const ::tensorflow::OpList& stripped_op_list() const;
  PROTOBUF_NODISCARD ::tensorflow::OpList* release_stripped_op_list();
  ::tensorflow::OpList* mutable_stripped_op_list();
  void set_allocated_stripped_op_list(::tensorflow::OpList* stripped_op_list);
  private:
  const ::tensorflow::OpList& _internal_stripped_op_list() const;
  ::tensorflow::OpList* _internal_mutable_stripped_op_list();
  public:
  void unsafe_arena_set_allocated_stripped_op_list(
      ::tensorflow::OpList* stripped_op_list);
  ::tensorflow::OpList* unsafe_arena_release_stripped_op_list();

  // .google.protobuf.Any any_info = 3;
  bool has_any_info() const;
  private:
  bool _internal_has_any_info() const;
  public:
  void clear_any_info();
  const ::PROTOBUF_NAMESPACE_ID::Any& any_info() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_any_info();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_any_info();
  void set_allocated_any_info(::PROTOBUF_NAMESPACE_ID::Any* any_info);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_any_info() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_any_info();
  public:
  void unsafe_arena_set_allocated_any_info(
      ::PROTOBUF_NAMESPACE_ID::Any* any_info);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_any_info();

  // bool stripped_default_attrs = 7;
  void clear_stripped_default_attrs();
  bool stripped_default_attrs() const;
  void set_stripped_default_attrs(bool value);
  private:
  bool _internal_stripped_default_attrs() const;
  void _internal_set_stripped_default_attrs(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MetaGraphDef.MetaInfoDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> tags_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        MetaGraphDef_MetaInfoDef_FunctionAliasesEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> function_aliases_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr meta_graph_version_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensorflow_version_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensorflow_git_version_;
    ::tensorflow::OpList* stripped_op_list_;
    ::PROTOBUF_NAMESPACE_ID::Any* any_info_;
    bool stripped_default_attrs_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class MetaGraphDef_CollectionDefEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<MetaGraphDef_CollectionDefEntry_DoNotUse, 
    std::string, ::tensorflow::CollectionDef,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<MetaGraphDef_CollectionDefEntry_DoNotUse, 
    std::string, ::tensorflow::CollectionDef,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  MetaGraphDef_CollectionDefEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR MetaGraphDef_CollectionDefEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit MetaGraphDef_CollectionDefEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const MetaGraphDef_CollectionDefEntry_DoNotUse& other);
  static const MetaGraphDef_CollectionDefEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const MetaGraphDef_CollectionDefEntry_DoNotUse*>(&_MetaGraphDef_CollectionDefEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.MetaGraphDef.CollectionDefEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class MetaGraphDef_SignatureDefEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<MetaGraphDef_SignatureDefEntry_DoNotUse, 
    std::string, ::tensorflow::SignatureDef,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<MetaGraphDef_SignatureDefEntry_DoNotUse, 
    std::string, ::tensorflow::SignatureDef,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  MetaGraphDef_SignatureDefEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR MetaGraphDef_SignatureDefEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit MetaGraphDef_SignatureDefEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const MetaGraphDef_SignatureDefEntry_DoNotUse& other);
  static const MetaGraphDef_SignatureDefEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const MetaGraphDef_SignatureDefEntry_DoNotUse*>(&_MetaGraphDef_SignatureDefEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.MetaGraphDef.SignatureDefEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class MetaGraphDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MetaGraphDef) */ {
 public:
  inline MetaGraphDef() : MetaGraphDef(nullptr) {}
  ~MetaGraphDef() override;
  explicit PROTOBUF_CONSTEXPR MetaGraphDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MetaGraphDef(const MetaGraphDef& from);
  MetaGraphDef(MetaGraphDef&& from) noexcept
    : MetaGraphDef() {
    *this = ::std::move(from);
  }

  inline MetaGraphDef& operator=(const MetaGraphDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline MetaGraphDef& operator=(MetaGraphDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MetaGraphDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const MetaGraphDef* internal_default_instance() {
    return reinterpret_cast<const MetaGraphDef*>(
               &_MetaGraphDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MetaGraphDef& a, MetaGraphDef& b) {
    a.Swap(&b);
  }
  inline void Swap(MetaGraphDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MetaGraphDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MetaGraphDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MetaGraphDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MetaGraphDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MetaGraphDef& from) {
    MetaGraphDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MetaGraphDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MetaGraphDef";
  }
  protected:
  explicit MetaGraphDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef MetaGraphDef_MetaInfoDef MetaInfoDef;

  // accessors -------------------------------------------------------

  enum : int {
    kCollectionDefFieldNumber = 4,
    kSignatureDefFieldNumber = 5,
    kAssetFileDefFieldNumber = 6,
    kMetaInfoDefFieldNumber = 1,
    kGraphDefFieldNumber = 2,
    kSaverDefFieldNumber = 3,
    kObjectGraphDefFieldNumber = 7,
  };
  // map<string, .tensorflow.CollectionDef> collection_def = 4;
  int collection_def_size() const;
  private:
  int _internal_collection_def_size() const;
  public:
  void clear_collection_def();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >&
      _internal_collection_def() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >*
      _internal_mutable_collection_def();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >&
      collection_def() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >*
      mutable_collection_def();

  // map<string, .tensorflow.SignatureDef> signature_def = 5;
  int signature_def_size() const;
  private:
  int _internal_signature_def_size() const;
  public:
  void clear_signature_def();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >&
      _internal_signature_def() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >*
      _internal_mutable_signature_def();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >&
      signature_def() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >*
      mutable_signature_def();

  // repeated .tensorflow.AssetFileDef asset_file_def = 6;
  int asset_file_def_size() const;
  private:
  int _internal_asset_file_def_size() const;
  public:
  void clear_asset_file_def();
  ::tensorflow::AssetFileDef* mutable_asset_file_def(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >*
      mutable_asset_file_def();
  private:
  const ::tensorflow::AssetFileDef& _internal_asset_file_def(int index) const;
  ::tensorflow::AssetFileDef* _internal_add_asset_file_def();
  public:
  const ::tensorflow::AssetFileDef& asset_file_def(int index) const;
  ::tensorflow::AssetFileDef* add_asset_file_def();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >&
      asset_file_def() const;

  // .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
  bool has_meta_info_def() const;
  private:
  bool _internal_has_meta_info_def() const;
  public:
  void clear_meta_info_def();
  const ::tensorflow::MetaGraphDef_MetaInfoDef& meta_info_def() const;
  PROTOBUF_NODISCARD ::tensorflow::MetaGraphDef_MetaInfoDef* release_meta_info_def();
  ::tensorflow::MetaGraphDef_MetaInfoDef* mutable_meta_info_def();
  void set_allocated_meta_info_def(::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def);
  private:
  const ::tensorflow::MetaGraphDef_MetaInfoDef& _internal_meta_info_def() const;
  ::tensorflow::MetaGraphDef_MetaInfoDef* _internal_mutable_meta_info_def();
  public:
  void unsafe_arena_set_allocated_meta_info_def(
      ::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def);
  ::tensorflow::MetaGraphDef_MetaInfoDef* unsafe_arena_release_meta_info_def();

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  private:
  bool _internal_has_graph_def() const;
  public:
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  ::tensorflow::GraphDef* _internal_mutable_graph_def();
  public:
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.SaverDef saver_def = 3;
  bool has_saver_def() const;
  private:
  bool _internal_has_saver_def() const;
  public:
  void clear_saver_def();
  const ::tensorflow::SaverDef& saver_def() const;
  PROTOBUF_NODISCARD ::tensorflow::SaverDef* release_saver_def();
  ::tensorflow::SaverDef* mutable_saver_def();
  void set_allocated_saver_def(::tensorflow::SaverDef* saver_def);
  private:
  const ::tensorflow::SaverDef& _internal_saver_def() const;
  ::tensorflow::SaverDef* _internal_mutable_saver_def();
  public:
  void unsafe_arena_set_allocated_saver_def(
      ::tensorflow::SaverDef* saver_def);
  ::tensorflow::SaverDef* unsafe_arena_release_saver_def();

  // .tensorflow.SavedObjectGraph object_graph_def = 7;
  bool has_object_graph_def() const;
  private:
  bool _internal_has_object_graph_def() const;
  public:
  void clear_object_graph_def();
  const ::tensorflow::SavedObjectGraph& object_graph_def() const;
  PROTOBUF_NODISCARD ::tensorflow::SavedObjectGraph* release_object_graph_def();
  ::tensorflow::SavedObjectGraph* mutable_object_graph_def();
  void set_allocated_object_graph_def(::tensorflow::SavedObjectGraph* object_graph_def);
  private:
  const ::tensorflow::SavedObjectGraph& _internal_object_graph_def() const;
  ::tensorflow::SavedObjectGraph* _internal_mutable_object_graph_def();
  public:
  void unsafe_arena_set_allocated_object_graph_def(
      ::tensorflow::SavedObjectGraph* object_graph_def);
  ::tensorflow::SavedObjectGraph* unsafe_arena_release_object_graph_def();

  // @@protoc_insertion_point(class_scope:tensorflow.MetaGraphDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        MetaGraphDef_CollectionDefEntry_DoNotUse,
        std::string, ::tensorflow::CollectionDef,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> collection_def_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        MetaGraphDef_SignatureDefEntry_DoNotUse,
        std::string, ::tensorflow::SignatureDef,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> signature_def_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef > asset_file_def_;
    ::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def_;
    ::tensorflow::GraphDef* graph_def_;
    ::tensorflow::SaverDef* saver_def_;
    ::tensorflow::SavedObjectGraph* object_graph_def_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CollectionDef_NodeList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.NodeList) */ {
 public:
  inline CollectionDef_NodeList() : CollectionDef_NodeList(nullptr) {}
  ~CollectionDef_NodeList() override;
  explicit PROTOBUF_CONSTEXPR CollectionDef_NodeList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CollectionDef_NodeList(const CollectionDef_NodeList& from);
  CollectionDef_NodeList(CollectionDef_NodeList&& from) noexcept
    : CollectionDef_NodeList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_NodeList& operator=(const CollectionDef_NodeList& from) {
    CopyFrom(from);
    return *this;
  }
  inline CollectionDef_NodeList& operator=(CollectionDef_NodeList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CollectionDef_NodeList& default_instance() {
    return *internal_default_instance();
  }
  static inline const CollectionDef_NodeList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_NodeList*>(
               &_CollectionDef_NodeList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(CollectionDef_NodeList& a, CollectionDef_NodeList& b) {
    a.Swap(&b);
  }
  inline void Swap(CollectionDef_NodeList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CollectionDef_NodeList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CollectionDef_NodeList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CollectionDef_NodeList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CollectionDef_NodeList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CollectionDef_NodeList& from) {
    CollectionDef_NodeList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_NodeList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CollectionDef.NodeList";
  }
  protected:
  explicit CollectionDef_NodeList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated string value = 1;
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  const std::string& value(int index) const;
  std::string* mutable_value(int index);
  void set_value(int index, const std::string& value);
  void set_value(int index, std::string&& value);
  void set_value(int index, const char* value);
  void set_value(int index, const char* value, size_t size);
  std::string* add_value();
  void add_value(const std::string& value);
  void add_value(std::string&& value);
  void add_value(const char* value);
  void add_value(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_value();
  private:
  const std::string& _internal_value(int index) const;
  std::string* _internal_add_value();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.NodeList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CollectionDef_BytesList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.BytesList) */ {
 public:
  inline CollectionDef_BytesList() : CollectionDef_BytesList(nullptr) {}
  ~CollectionDef_BytesList() override;
  explicit PROTOBUF_CONSTEXPR CollectionDef_BytesList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CollectionDef_BytesList(const CollectionDef_BytesList& from);
  CollectionDef_BytesList(CollectionDef_BytesList&& from) noexcept
    : CollectionDef_BytesList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_BytesList& operator=(const CollectionDef_BytesList& from) {
    CopyFrom(from);
    return *this;
  }
  inline CollectionDef_BytesList& operator=(CollectionDef_BytesList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CollectionDef_BytesList& default_instance() {
    return *internal_default_instance();
  }
  static inline const CollectionDef_BytesList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_BytesList*>(
               &_CollectionDef_BytesList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(CollectionDef_BytesList& a, CollectionDef_BytesList& b) {
    a.Swap(&b);
  }
  inline void Swap(CollectionDef_BytesList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CollectionDef_BytesList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CollectionDef_BytesList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CollectionDef_BytesList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CollectionDef_BytesList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CollectionDef_BytesList& from) {
    CollectionDef_BytesList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_BytesList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CollectionDef.BytesList";
  }
  protected:
  explicit CollectionDef_BytesList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated bytes value = 1;
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  const std::string& value(int index) const;
  std::string* mutable_value(int index);
  void set_value(int index, const std::string& value);
  void set_value(int index, std::string&& value);
  void set_value(int index, const char* value);
  void set_value(int index, const void* value, size_t size);
  std::string* add_value();
  void add_value(const std::string& value);
  void add_value(std::string&& value);
  void add_value(const char* value);
  void add_value(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_value();
  private:
  const std::string& _internal_value(int index) const;
  std::string* _internal_add_value();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.BytesList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CollectionDef_Int64List final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.Int64List) */ {
 public:
  inline CollectionDef_Int64List() : CollectionDef_Int64List(nullptr) {}
  ~CollectionDef_Int64List() override;
  explicit PROTOBUF_CONSTEXPR CollectionDef_Int64List(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CollectionDef_Int64List(const CollectionDef_Int64List& from);
  CollectionDef_Int64List(CollectionDef_Int64List&& from) noexcept
    : CollectionDef_Int64List() {
    *this = ::std::move(from);
  }

  inline CollectionDef_Int64List& operator=(const CollectionDef_Int64List& from) {
    CopyFrom(from);
    return *this;
  }
  inline CollectionDef_Int64List& operator=(CollectionDef_Int64List&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CollectionDef_Int64List& default_instance() {
    return *internal_default_instance();
  }
  static inline const CollectionDef_Int64List* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_Int64List*>(
               &_CollectionDef_Int64List_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(CollectionDef_Int64List& a, CollectionDef_Int64List& b) {
    a.Swap(&b);
  }
  inline void Swap(CollectionDef_Int64List* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CollectionDef_Int64List* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CollectionDef_Int64List* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CollectionDef_Int64List>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CollectionDef_Int64List& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CollectionDef_Int64List& from) {
    CollectionDef_Int64List::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_Int64List* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CollectionDef.Int64List";
  }
  protected:
  explicit CollectionDef_Int64List(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated int64 value = 1 [packed = true];
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  private:
  int64_t _internal_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_value() const;
  void _internal_add_value(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_value();
  public:
  int64_t value(int index) const;
  void set_value(int index, int64_t value);
  void add_value(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.Int64List)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > value_;
    mutable std::atomic<int> _value_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CollectionDef_FloatList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.FloatList) */ {
 public:
  inline CollectionDef_FloatList() : CollectionDef_FloatList(nullptr) {}
  ~CollectionDef_FloatList() override;
  explicit PROTOBUF_CONSTEXPR CollectionDef_FloatList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CollectionDef_FloatList(const CollectionDef_FloatList& from);
  CollectionDef_FloatList(CollectionDef_FloatList&& from) noexcept
    : CollectionDef_FloatList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_FloatList& operator=(const CollectionDef_FloatList& from) {
    CopyFrom(from);
    return *this;
  }
  inline CollectionDef_FloatList& operator=(CollectionDef_FloatList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CollectionDef_FloatList& default_instance() {
    return *internal_default_instance();
  }
  static inline const CollectionDef_FloatList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_FloatList*>(
               &_CollectionDef_FloatList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(CollectionDef_FloatList& a, CollectionDef_FloatList& b) {
    a.Swap(&b);
  }
  inline void Swap(CollectionDef_FloatList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CollectionDef_FloatList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CollectionDef_FloatList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CollectionDef_FloatList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CollectionDef_FloatList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CollectionDef_FloatList& from) {
    CollectionDef_FloatList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_FloatList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CollectionDef.FloatList";
  }
  protected:
  explicit CollectionDef_FloatList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated float value = 1 [packed = true];
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  private:
  float _internal_value(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_value() const;
  void _internal_add_value(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_value();
  public:
  float value(int index) const;
  void set_value(int index, float value);
  void add_value(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      value() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_value();

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.FloatList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CollectionDef_AnyList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef.AnyList) */ {
 public:
  inline CollectionDef_AnyList() : CollectionDef_AnyList(nullptr) {}
  ~CollectionDef_AnyList() override;
  explicit PROTOBUF_CONSTEXPR CollectionDef_AnyList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CollectionDef_AnyList(const CollectionDef_AnyList& from);
  CollectionDef_AnyList(CollectionDef_AnyList&& from) noexcept
    : CollectionDef_AnyList() {
    *this = ::std::move(from);
  }

  inline CollectionDef_AnyList& operator=(const CollectionDef_AnyList& from) {
    CopyFrom(from);
    return *this;
  }
  inline CollectionDef_AnyList& operator=(CollectionDef_AnyList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CollectionDef_AnyList& default_instance() {
    return *internal_default_instance();
  }
  static inline const CollectionDef_AnyList* internal_default_instance() {
    return reinterpret_cast<const CollectionDef_AnyList*>(
               &_CollectionDef_AnyList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(CollectionDef_AnyList& a, CollectionDef_AnyList& b) {
    a.Swap(&b);
  }
  inline void Swap(CollectionDef_AnyList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CollectionDef_AnyList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CollectionDef_AnyList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CollectionDef_AnyList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CollectionDef_AnyList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CollectionDef_AnyList& from) {
    CollectionDef_AnyList::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef_AnyList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CollectionDef.AnyList";
  }
  protected:
  explicit CollectionDef_AnyList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValueFieldNumber = 1,
  };
  // repeated .google.protobuf.Any value = 1;
  int value_size() const;
  private:
  int _internal_value_size() const;
  public:
  void clear_value();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_value(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
      mutable_value();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_add_value();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Any& value(int index) const;
  ::PROTOBUF_NAMESPACE_ID::Any* add_value();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
      value() const;

  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef.AnyList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any > value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class CollectionDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CollectionDef) */ {
 public:
  inline CollectionDef() : CollectionDef(nullptr) {}
  ~CollectionDef() override;
  explicit PROTOBUF_CONSTEXPR CollectionDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CollectionDef(const CollectionDef& from);
  CollectionDef(CollectionDef&& from) noexcept
    : CollectionDef() {
    *this = ::std::move(from);
  }

  inline CollectionDef& operator=(const CollectionDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline CollectionDef& operator=(CollectionDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CollectionDef& default_instance() {
    return *internal_default_instance();
  }
  enum KindCase {
    kNodeList = 1,
    kBytesList = 2,
    kInt64List = 3,
    kFloatList = 4,
    kAnyList = 5,
    KIND_NOT_SET = 0,
  };

  static inline const CollectionDef* internal_default_instance() {
    return reinterpret_cast<const CollectionDef*>(
               &_CollectionDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(CollectionDef& a, CollectionDef& b) {
    a.Swap(&b);
  }
  inline void Swap(CollectionDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CollectionDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CollectionDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CollectionDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CollectionDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CollectionDef& from) {
    CollectionDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CollectionDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CollectionDef";
  }
  protected:
  explicit CollectionDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef CollectionDef_NodeList NodeList;
  typedef CollectionDef_BytesList BytesList;
  typedef CollectionDef_Int64List Int64List;
  typedef CollectionDef_FloatList FloatList;
  typedef CollectionDef_AnyList AnyList;

  // accessors -------------------------------------------------------

  enum : int {
    kNodeListFieldNumber = 1,
    kBytesListFieldNumber = 2,
    kInt64ListFieldNumber = 3,
    kFloatListFieldNumber = 4,
    kAnyListFieldNumber = 5,
  };
  // .tensorflow.CollectionDef.NodeList node_list = 1;
  bool has_node_list() const;
  private:
  bool _internal_has_node_list() const;
  public:
  void clear_node_list();
  const ::tensorflow::CollectionDef_NodeList& node_list() const;
  PROTOBUF_NODISCARD ::tensorflow::CollectionDef_NodeList* release_node_list();
  ::tensorflow::CollectionDef_NodeList* mutable_node_list();
  void set_allocated_node_list(::tensorflow::CollectionDef_NodeList* node_list);
  private:
  const ::tensorflow::CollectionDef_NodeList& _internal_node_list() const;
  ::tensorflow::CollectionDef_NodeList* _internal_mutable_node_list();
  public:
  void unsafe_arena_set_allocated_node_list(
      ::tensorflow::CollectionDef_NodeList* node_list);
  ::tensorflow::CollectionDef_NodeList* unsafe_arena_release_node_list();

  // .tensorflow.CollectionDef.BytesList bytes_list = 2;
  bool has_bytes_list() const;
  private:
  bool _internal_has_bytes_list() const;
  public:
  void clear_bytes_list();
  const ::tensorflow::CollectionDef_BytesList& bytes_list() const;
  PROTOBUF_NODISCARD ::tensorflow::CollectionDef_BytesList* release_bytes_list();
  ::tensorflow::CollectionDef_BytesList* mutable_bytes_list();
  void set_allocated_bytes_list(::tensorflow::CollectionDef_BytesList* bytes_list);
  private:
  const ::tensorflow::CollectionDef_BytesList& _internal_bytes_list() const;
  ::tensorflow::CollectionDef_BytesList* _internal_mutable_bytes_list();
  public:
  void unsafe_arena_set_allocated_bytes_list(
      ::tensorflow::CollectionDef_BytesList* bytes_list);
  ::tensorflow::CollectionDef_BytesList* unsafe_arena_release_bytes_list();

  // .tensorflow.CollectionDef.Int64List int64_list = 3;
  bool has_int64_list() const;
  private:
  bool _internal_has_int64_list() const;
  public:
  void clear_int64_list();
  const ::tensorflow::CollectionDef_Int64List& int64_list() const;
  PROTOBUF_NODISCARD ::tensorflow::CollectionDef_Int64List* release_int64_list();
  ::tensorflow::CollectionDef_Int64List* mutable_int64_list();
  void set_allocated_int64_list(::tensorflow::CollectionDef_Int64List* int64_list);
  private:
  const ::tensorflow::CollectionDef_Int64List& _internal_int64_list() const;
  ::tensorflow::CollectionDef_Int64List* _internal_mutable_int64_list();
  public:
  void unsafe_arena_set_allocated_int64_list(
      ::tensorflow::CollectionDef_Int64List* int64_list);
  ::tensorflow::CollectionDef_Int64List* unsafe_arena_release_int64_list();

  // .tensorflow.CollectionDef.FloatList float_list = 4;
  bool has_float_list() const;
  private:
  bool _internal_has_float_list() const;
  public:
  void clear_float_list();
  const ::tensorflow::CollectionDef_FloatList& float_list() const;
  PROTOBUF_NODISCARD ::tensorflow::CollectionDef_FloatList* release_float_list();
  ::tensorflow::CollectionDef_FloatList* mutable_float_list();
  void set_allocated_float_list(::tensorflow::CollectionDef_FloatList* float_list);
  private:
  const ::tensorflow::CollectionDef_FloatList& _internal_float_list() const;
  ::tensorflow::CollectionDef_FloatList* _internal_mutable_float_list();
  public:
  void unsafe_arena_set_allocated_float_list(
      ::tensorflow::CollectionDef_FloatList* float_list);
  ::tensorflow::CollectionDef_FloatList* unsafe_arena_release_float_list();

  // .tensorflow.CollectionDef.AnyList any_list = 5;
  bool has_any_list() const;
  private:
  bool _internal_has_any_list() const;
  public:
  void clear_any_list();
  const ::tensorflow::CollectionDef_AnyList& any_list() const;
  PROTOBUF_NODISCARD ::tensorflow::CollectionDef_AnyList* release_any_list();
  ::tensorflow::CollectionDef_AnyList* mutable_any_list();
  void set_allocated_any_list(::tensorflow::CollectionDef_AnyList* any_list);
  private:
  const ::tensorflow::CollectionDef_AnyList& _internal_any_list() const;
  ::tensorflow::CollectionDef_AnyList* _internal_mutable_any_list();
  public:
  void unsafe_arena_set_allocated_any_list(
      ::tensorflow::CollectionDef_AnyList* any_list);
  ::tensorflow::CollectionDef_AnyList* unsafe_arena_release_any_list();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.CollectionDef)
 private:
  class _Internal;
  void set_has_node_list();
  void set_has_bytes_list();
  void set_has_int64_list();
  void set_has_float_list();
  void set_has_any_list();

  inline bool has_kind() const;
  inline void clear_has_kind();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union KindUnion {
      constexpr KindUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::CollectionDef_NodeList* node_list_;
      ::tensorflow::CollectionDef_BytesList* bytes_list_;
      ::tensorflow::CollectionDef_Int64List* int64_list_;
      ::tensorflow::CollectionDef_FloatList* float_list_;
      ::tensorflow::CollectionDef_AnyList* any_list_;
    } kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TensorInfo_CooSparse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorInfo.CooSparse) */ {
 public:
  inline TensorInfo_CooSparse() : TensorInfo_CooSparse(nullptr) {}
  ~TensorInfo_CooSparse() override;
  explicit PROTOBUF_CONSTEXPR TensorInfo_CooSparse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorInfo_CooSparse(const TensorInfo_CooSparse& from);
  TensorInfo_CooSparse(TensorInfo_CooSparse&& from) noexcept
    : TensorInfo_CooSparse() {
    *this = ::std::move(from);
  }

  inline TensorInfo_CooSparse& operator=(const TensorInfo_CooSparse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorInfo_CooSparse& operator=(TensorInfo_CooSparse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorInfo_CooSparse& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorInfo_CooSparse* internal_default_instance() {
    return reinterpret_cast<const TensorInfo_CooSparse*>(
               &_TensorInfo_CooSparse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(TensorInfo_CooSparse& a, TensorInfo_CooSparse& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorInfo_CooSparse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorInfo_CooSparse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorInfo_CooSparse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorInfo_CooSparse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorInfo_CooSparse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorInfo_CooSparse& from) {
    TensorInfo_CooSparse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorInfo_CooSparse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorInfo.CooSparse";
  }
  protected:
  explicit TensorInfo_CooSparse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesTensorNameFieldNumber = 1,
    kIndicesTensorNameFieldNumber = 2,
    kDenseShapeTensorNameFieldNumber = 3,
  };
  // string values_tensor_name = 1;
  void clear_values_tensor_name();
  const std::string& values_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_values_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_values_tensor_name();
  PROTOBUF_NODISCARD std::string* release_values_tensor_name();
  void set_allocated_values_tensor_name(std::string* values_tensor_name);
  private:
  const std::string& _internal_values_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_values_tensor_name(const std::string& value);
  std::string* _internal_mutable_values_tensor_name();
  public:

  // string indices_tensor_name = 2;
  void clear_indices_tensor_name();
  const std::string& indices_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_indices_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_indices_tensor_name();
  PROTOBUF_NODISCARD std::string* release_indices_tensor_name();
  void set_allocated_indices_tensor_name(std::string* indices_tensor_name);
  private:
  const std::string& _internal_indices_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_indices_tensor_name(const std::string& value);
  std::string* _internal_mutable_indices_tensor_name();
  public:

  // string dense_shape_tensor_name = 3;
  void clear_dense_shape_tensor_name();
  const std::string& dense_shape_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dense_shape_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dense_shape_tensor_name();
  PROTOBUF_NODISCARD std::string* release_dense_shape_tensor_name();
  void set_allocated_dense_shape_tensor_name(std::string* dense_shape_tensor_name);
  private:
  const std::string& _internal_dense_shape_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dense_shape_tensor_name(const std::string& value);
  std::string* _internal_mutable_dense_shape_tensor_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TensorInfo.CooSparse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr values_tensor_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr indices_tensor_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dense_shape_tensor_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TensorInfo_CompositeTensor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorInfo.CompositeTensor) */ {
 public:
  inline TensorInfo_CompositeTensor() : TensorInfo_CompositeTensor(nullptr) {}
  ~TensorInfo_CompositeTensor() override;
  explicit PROTOBUF_CONSTEXPR TensorInfo_CompositeTensor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorInfo_CompositeTensor(const TensorInfo_CompositeTensor& from);
  TensorInfo_CompositeTensor(TensorInfo_CompositeTensor&& from) noexcept
    : TensorInfo_CompositeTensor() {
    *this = ::std::move(from);
  }

  inline TensorInfo_CompositeTensor& operator=(const TensorInfo_CompositeTensor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorInfo_CompositeTensor& operator=(TensorInfo_CompositeTensor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorInfo_CompositeTensor& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorInfo_CompositeTensor* internal_default_instance() {
    return reinterpret_cast<const TensorInfo_CompositeTensor*>(
               &_TensorInfo_CompositeTensor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(TensorInfo_CompositeTensor& a, TensorInfo_CompositeTensor& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorInfo_CompositeTensor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorInfo_CompositeTensor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorInfo_CompositeTensor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorInfo_CompositeTensor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorInfo_CompositeTensor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorInfo_CompositeTensor& from) {
    TensorInfo_CompositeTensor::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorInfo_CompositeTensor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorInfo.CompositeTensor";
  }
  protected:
  explicit TensorInfo_CompositeTensor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentsFieldNumber = 2,
    kTypeSpecFieldNumber = 1,
  };
  // repeated .tensorflow.TensorInfo components = 2;
  int components_size() const;
  private:
  int _internal_components_size() const;
  public:
  void clear_components();
  ::tensorflow::TensorInfo* mutable_components(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorInfo >*
      mutable_components();
  private:
  const ::tensorflow::TensorInfo& _internal_components(int index) const;
  ::tensorflow::TensorInfo* _internal_add_components();
  public:
  const ::tensorflow::TensorInfo& components(int index) const;
  ::tensorflow::TensorInfo* add_components();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorInfo >&
      components() const;

  // .tensorflow.TypeSpecProto type_spec = 1;
  bool has_type_spec() const;
  private:
  bool _internal_has_type_spec() const;
  public:
  void clear_type_spec();
  const ::tensorflow::TypeSpecProto& type_spec() const;
  PROTOBUF_NODISCARD ::tensorflow::TypeSpecProto* release_type_spec();
  ::tensorflow::TypeSpecProto* mutable_type_spec();
  void set_allocated_type_spec(::tensorflow::TypeSpecProto* type_spec);
  private:
  const ::tensorflow::TypeSpecProto& _internal_type_spec() const;
  ::tensorflow::TypeSpecProto* _internal_mutable_type_spec();
  public:
  void unsafe_arena_set_allocated_type_spec(
      ::tensorflow::TypeSpecProto* type_spec);
  ::tensorflow::TypeSpecProto* unsafe_arena_release_type_spec();

  // @@protoc_insertion_point(class_scope:tensorflow.TensorInfo.CompositeTensor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorInfo > components_;
    ::tensorflow::TypeSpecProto* type_spec_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class TensorInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorInfo) */ {
 public:
  inline TensorInfo() : TensorInfo(nullptr) {}
  ~TensorInfo() override;
  explicit PROTOBUF_CONSTEXPR TensorInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorInfo(const TensorInfo& from);
  TensorInfo(TensorInfo&& from) noexcept
    : TensorInfo() {
    *this = ::std::move(from);
  }

  inline TensorInfo& operator=(const TensorInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorInfo& operator=(TensorInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorInfo& default_instance() {
    return *internal_default_instance();
  }
  enum EncodingCase {
    kName = 1,
    kCooSparse = 4,
    kCompositeTensor = 5,
    ENCODING_NOT_SET = 0,
  };

  static inline const TensorInfo* internal_default_instance() {
    return reinterpret_cast<const TensorInfo*>(
               &_TensorInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(TensorInfo& a, TensorInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorInfo& from) {
    TensorInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorInfo";
  }
  protected:
  explicit TensorInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TensorInfo_CooSparse CooSparse;
  typedef TensorInfo_CompositeTensor CompositeTensor;

  // accessors -------------------------------------------------------

  enum : int {
    kTensorShapeFieldNumber = 3,
    kDtypeFieldNumber = 2,
    kNameFieldNumber = 1,
    kCooSparseFieldNumber = 4,
    kCompositeTensorFieldNumber = 5,
  };
  // .tensorflow.TensorShapeProto tensor_shape = 3;
  bool has_tensor_shape() const;
  private:
  bool _internal_has_tensor_shape() const;
  public:
  void clear_tensor_shape();
  const ::tensorflow::TensorShapeProto& tensor_shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_tensor_shape();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape();
  void set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_tensor_shape();
  public:
  void unsafe_arena_set_allocated_tensor_shape(
      ::tensorflow::TensorShapeProto* tensor_shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape();

  // .tensorflow.DataType dtype = 2;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
  bool has_coo_sparse() const;
  private:
  bool _internal_has_coo_sparse() const;
  public:
  void clear_coo_sparse();
  const ::tensorflow::TensorInfo_CooSparse& coo_sparse() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorInfo_CooSparse* release_coo_sparse();
  ::tensorflow::TensorInfo_CooSparse* mutable_coo_sparse();
  void set_allocated_coo_sparse(::tensorflow::TensorInfo_CooSparse* coo_sparse);
  private:
  const ::tensorflow::TensorInfo_CooSparse& _internal_coo_sparse() const;
  ::tensorflow::TensorInfo_CooSparse* _internal_mutable_coo_sparse();
  public:
  void unsafe_arena_set_allocated_coo_sparse(
      ::tensorflow::TensorInfo_CooSparse* coo_sparse);
  ::tensorflow::TensorInfo_CooSparse* unsafe_arena_release_coo_sparse();

  // .tensorflow.TensorInfo.CompositeTensor composite_tensor = 5;
  bool has_composite_tensor() const;
  private:
  bool _internal_has_composite_tensor() const;
  public:
  void clear_composite_tensor();
  const ::tensorflow::TensorInfo_CompositeTensor& composite_tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorInfo_CompositeTensor* release_composite_tensor();
  ::tensorflow::TensorInfo_CompositeTensor* mutable_composite_tensor();
  void set_allocated_composite_tensor(::tensorflow::TensorInfo_CompositeTensor* composite_tensor);
  private:
  const ::tensorflow::TensorInfo_CompositeTensor& _internal_composite_tensor() const;
  ::tensorflow::TensorInfo_CompositeTensor* _internal_mutable_composite_tensor();
  public:
  void unsafe_arena_set_allocated_composite_tensor(
      ::tensorflow::TensorInfo_CompositeTensor* composite_tensor);
  ::tensorflow::TensorInfo_CompositeTensor* unsafe_arena_release_composite_tensor();

  void clear_encoding();
  EncodingCase encoding_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.TensorInfo)
 private:
  class _Internal;
  void set_has_name();
  void set_has_coo_sparse();
  void set_has_composite_tensor();

  inline bool has_encoding() const;
  inline void clear_has_encoding();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorShapeProto* tensor_shape_;
    int dtype_;
    union EncodingUnion {
      constexpr EncodingUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
      ::tensorflow::TensorInfo_CooSparse* coo_sparse_;
      ::tensorflow::TensorInfo_CompositeTensor* composite_tensor_;
    } encoding_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class SignatureDef_InputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SignatureDef_InputsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorInfo,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SignatureDef_InputsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorInfo,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  SignatureDef_InputsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR SignatureDef_InputsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit SignatureDef_InputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SignatureDef_InputsEntry_DoNotUse& other);
  static const SignatureDef_InputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SignatureDef_InputsEntry_DoNotUse*>(&_SignatureDef_InputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SignatureDef.InputsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class SignatureDef_OutputsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SignatureDef_OutputsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorInfo,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SignatureDef_OutputsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorInfo,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  SignatureDef_OutputsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR SignatureDef_OutputsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit SignatureDef_OutputsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SignatureDef_OutputsEntry_DoNotUse& other);
  static const SignatureDef_OutputsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SignatureDef_OutputsEntry_DoNotUse*>(&_SignatureDef_OutputsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SignatureDef.OutputsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class SignatureDef_DefaultsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SignatureDef_DefaultsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<SignatureDef_DefaultsEntry_DoNotUse, 
    std::string, ::tensorflow::TensorProto,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  SignatureDef_DefaultsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR SignatureDef_DefaultsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit SignatureDef_DefaultsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const SignatureDef_DefaultsEntry_DoNotUse& other);
  static const SignatureDef_DefaultsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const SignatureDef_DefaultsEntry_DoNotUse*>(&_SignatureDef_DefaultsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.SignatureDef.DefaultsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};

// -------------------------------------------------------------------

class SignatureDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SignatureDef) */ {
 public:
  inline SignatureDef() : SignatureDef(nullptr) {}
  ~SignatureDef() override;
  explicit PROTOBUF_CONSTEXPR SignatureDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SignatureDef(const SignatureDef& from);
  SignatureDef(SignatureDef&& from) noexcept
    : SignatureDef() {
    *this = ::std::move(from);
  }

  inline SignatureDef& operator=(const SignatureDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline SignatureDef& operator=(SignatureDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SignatureDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const SignatureDef* internal_default_instance() {
    return reinterpret_cast<const SignatureDef*>(
               &_SignatureDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(SignatureDef& a, SignatureDef& b) {
    a.Swap(&b);
  }
  inline void Swap(SignatureDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SignatureDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SignatureDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SignatureDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SignatureDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SignatureDef& from) {
    SignatureDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SignatureDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SignatureDef";
  }
  protected:
  explicit SignatureDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kInputsFieldNumber = 1,
    kOutputsFieldNumber = 2,
    kDefaultsFieldNumber = 4,
    kMethodNameFieldNumber = 3,
  };
  // map<string, .tensorflow.TensorInfo> inputs = 1;
  int inputs_size() const;
  private:
  int _internal_inputs_size() const;
  public:
  void clear_inputs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
      _internal_inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
      _internal_mutable_inputs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
      inputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
      mutable_inputs();

  // map<string, .tensorflow.TensorInfo> outputs = 2;
  int outputs_size() const;
  private:
  int _internal_outputs_size() const;
  public:
  void clear_outputs();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
      _internal_outputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
      _internal_mutable_outputs();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
      outputs() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
      mutable_outputs();

  // map<string, .tensorflow.TensorProto> defaults = 4;
  int defaults_size() const;
  private:
  int _internal_defaults_size() const;
  public:
  void clear_defaults();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
      _internal_defaults() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
      _internal_mutable_defaults();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
      defaults() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
      mutable_defaults();

  // string method_name = 3;
  void clear_method_name();
  const std::string& method_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_method_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_method_name();
  PROTOBUF_NODISCARD std::string* release_method_name();
  void set_allocated_method_name(std::string* method_name);
  private:
  const std::string& _internal_method_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_method_name(const std::string& value);
  std::string* _internal_mutable_method_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SignatureDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        SignatureDef_InputsEntry_DoNotUse,
        std::string, ::tensorflow::TensorInfo,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> inputs_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        SignatureDef_OutputsEntry_DoNotUse,
        std::string, ::tensorflow::TensorInfo,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> outputs_;
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        SignatureDef_DefaultsEntry_DoNotUse,
        std::string, ::tensorflow::TensorProto,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> defaults_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr method_name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// -------------------------------------------------------------------

class AssetFileDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.AssetFileDef) */ {
 public:
  inline AssetFileDef() : AssetFileDef(nullptr) {}
  ~AssetFileDef() override;
  explicit PROTOBUF_CONSTEXPR AssetFileDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AssetFileDef(const AssetFileDef& from);
  AssetFileDef(AssetFileDef&& from) noexcept
    : AssetFileDef() {
    *this = ::std::move(from);
  }

  inline AssetFileDef& operator=(const AssetFileDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline AssetFileDef& operator=(AssetFileDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AssetFileDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const AssetFileDef* internal_default_instance() {
    return reinterpret_cast<const AssetFileDef*>(
               &_AssetFileDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(AssetFileDef& a, AssetFileDef& b) {
    a.Swap(&b);
  }
  inline void Swap(AssetFileDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AssetFileDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AssetFileDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AssetFileDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AssetFileDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AssetFileDef& from) {
    AssetFileDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AssetFileDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.AssetFileDef";
  }
  protected:
  explicit AssetFileDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFilenameFieldNumber = 2,
    kTensorInfoFieldNumber = 1,
  };
  // string filename = 2;
  void clear_filename();
  const std::string& filename() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filename(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filename();
  PROTOBUF_NODISCARD std::string* release_filename();
  void set_allocated_filename(std::string* filename);
  private:
  const std::string& _internal_filename() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filename(const std::string& value);
  std::string* _internal_mutable_filename();
  public:

  // .tensorflow.TensorInfo tensor_info = 1;
  bool has_tensor_info() const;
  private:
  bool _internal_has_tensor_info() const;
  public:
  void clear_tensor_info();
  const ::tensorflow::TensorInfo& tensor_info() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorInfo* release_tensor_info();
  ::tensorflow::TensorInfo* mutable_tensor_info();
  void set_allocated_tensor_info(::tensorflow::TensorInfo* tensor_info);
  private:
  const ::tensorflow::TensorInfo& _internal_tensor_info() const;
  ::tensorflow::TensorInfo* _internal_mutable_tensor_info();
  public:
  void unsafe_arena_set_allocated_tensor_info(
      ::tensorflow::TensorInfo* tensor_info);
  ::tensorflow::TensorInfo* unsafe_arena_release_tensor_info();

  // @@protoc_insertion_point(class_scope:tensorflow.AssetFileDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filename_;
    ::tensorflow::TensorInfo* tensor_info_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// MetaGraphDef_MetaInfoDef

// string meta_graph_version = 1;
inline void MetaGraphDef_MetaInfoDef::clear_meta_graph_version() {
  _impl_.meta_graph_version_.ClearToEmpty();
}
inline const std::string& MetaGraphDef_MetaInfoDef::meta_graph_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  return _internal_meta_graph_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MetaGraphDef_MetaInfoDef::set_meta_graph_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.meta_graph_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}
inline std::string* MetaGraphDef_MetaInfoDef::mutable_meta_graph_version() {
  std::string* _s = _internal_mutable_meta_graph_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  return _s;
}
inline const std::string& MetaGraphDef_MetaInfoDef::_internal_meta_graph_version() const {
  return _impl_.meta_graph_version_.Get();
}
inline void MetaGraphDef_MetaInfoDef::_internal_set_meta_graph_version(const std::string& value) {
  
  _impl_.meta_graph_version_.Set(value, GetArenaForAllocation());
}
inline std::string* MetaGraphDef_MetaInfoDef::_internal_mutable_meta_graph_version() {
  
  return _impl_.meta_graph_version_.Mutable(GetArenaForAllocation());
}
inline std::string* MetaGraphDef_MetaInfoDef::release_meta_graph_version() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
  return _impl_.meta_graph_version_.Release();
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_meta_graph_version(std::string* meta_graph_version) {
  if (meta_graph_version != nullptr) {
    
  } else {
    
  }
  _impl_.meta_graph_version_.SetAllocated(meta_graph_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.meta_graph_version_.IsDefault()) {
    _impl_.meta_graph_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.meta_graph_version)
}

// .tensorflow.OpList stripped_op_list = 2;
inline bool MetaGraphDef_MetaInfoDef::_internal_has_stripped_op_list() const {
  return this != internal_default_instance() && _impl_.stripped_op_list_ != nullptr;
}
inline bool MetaGraphDef_MetaInfoDef::has_stripped_op_list() const {
  return _internal_has_stripped_op_list();
}
inline const ::tensorflow::OpList& MetaGraphDef_MetaInfoDef::_internal_stripped_op_list() const {
  const ::tensorflow::OpList* p = _impl_.stripped_op_list_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::OpList&>(
      ::tensorflow::_OpList_default_instance_);
}
inline const ::tensorflow::OpList& MetaGraphDef_MetaInfoDef::stripped_op_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  return _internal_stripped_op_list();
}
inline void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_stripped_op_list(
    ::tensorflow::OpList* stripped_op_list) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.stripped_op_list_);
  }
  _impl_.stripped_op_list_ = stripped_op_list;
  if (stripped_op_list) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::release_stripped_op_list() {
  
  ::tensorflow::OpList* temp = _impl_.stripped_op_list_;
  _impl_.stripped_op_list_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::unsafe_arena_release_stripped_op_list() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  
  ::tensorflow::OpList* temp = _impl_.stripped_op_list_;
  _impl_.stripped_op_list_ = nullptr;
  return temp;
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::_internal_mutable_stripped_op_list() {
  
  if (_impl_.stripped_op_list_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::OpList>(GetArenaForAllocation());
    _impl_.stripped_op_list_ = p;
  }
  return _impl_.stripped_op_list_;
}
inline ::tensorflow::OpList* MetaGraphDef_MetaInfoDef::mutable_stripped_op_list() {
  ::tensorflow::OpList* _msg = _internal_mutable_stripped_op_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
  return _msg;
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_stripped_op_list(::tensorflow::OpList* stripped_op_list) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.stripped_op_list_);
  }
  if (stripped_op_list) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(stripped_op_list));
    if (message_arena != submessage_arena) {
      stripped_op_list = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stripped_op_list, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.stripped_op_list_ = stripped_op_list;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.stripped_op_list)
}

// .google.protobuf.Any any_info = 3;
inline bool MetaGraphDef_MetaInfoDef::_internal_has_any_info() const {
  return this != internal_default_instance() && _impl_.any_info_ != nullptr;
}
inline bool MetaGraphDef_MetaInfoDef::has_any_info() const {
  return _internal_has_any_info();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& MetaGraphDef_MetaInfoDef::_internal_any_info() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.any_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& MetaGraphDef_MetaInfoDef::any_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  return _internal_any_info();
}
inline void MetaGraphDef_MetaInfoDef::unsafe_arena_set_allocated_any_info(
    ::PROTOBUF_NAMESPACE_ID::Any* any_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.any_info_);
  }
  _impl_.any_info_ = any_info;
  if (any_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MetaGraphDef_MetaInfoDef::release_any_info() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.any_info_;
  _impl_.any_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MetaGraphDef_MetaInfoDef::unsafe_arena_release_any_info() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.any_info_;
  _impl_.any_info_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MetaGraphDef_MetaInfoDef::_internal_mutable_any_info() {
  
  if (_impl_.any_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.any_info_ = p;
  }
  return _impl_.any_info_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* MetaGraphDef_MetaInfoDef::mutable_any_info() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_any_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
  return _msg;
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_any_info(::PROTOBUF_NAMESPACE_ID::Any* any_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.any_info_);
  }
  if (any_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(any_info));
    if (message_arena != submessage_arena) {
      any_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, any_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.any_info_ = any_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.any_info)
}

// repeated string tags = 4;
inline int MetaGraphDef_MetaInfoDef::_internal_tags_size() const {
  return _impl_.tags_.size();
}
inline int MetaGraphDef_MetaInfoDef::tags_size() const {
  return _internal_tags_size();
}
inline void MetaGraphDef_MetaInfoDef::clear_tags() {
  _impl_.tags_.Clear();
}
inline std::string* MetaGraphDef_MetaInfoDef::add_tags() {
  std::string* _s = _internal_add_tags();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return _s;
}
inline const std::string& MetaGraphDef_MetaInfoDef::_internal_tags(int index) const {
  return _impl_.tags_.Get(index);
}
inline const std::string& MetaGraphDef_MetaInfoDef::tags(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return _internal_tags(index);
}
inline std::string* MetaGraphDef_MetaInfoDef::mutable_tags(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return _impl_.tags_.Mutable(index);
}
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, const std::string& value) {
  _impl_.tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, std::string&& value) {
  _impl_.tags_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tags_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::set_tags(int index, const char* value, size_t size) {
  _impl_.tags_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline std::string* MetaGraphDef_MetaInfoDef::_internal_add_tags() {
  return _impl_.tags_.Add();
}
inline void MetaGraphDef_MetaInfoDef::add_tags(const std::string& value) {
  _impl_.tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::add_tags(std::string&& value) {
  _impl_.tags_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::add_tags(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tags_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline void MetaGraphDef_MetaInfoDef::add_tags(const char* value, size_t size) {
  _impl_.tags_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.MetaGraphDef.MetaInfoDef.tags)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
MetaGraphDef_MetaInfoDef::tags() const {
  // @@protoc_insertion_point(field_list:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return _impl_.tags_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
MetaGraphDef_MetaInfoDef::mutable_tags() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MetaGraphDef.MetaInfoDef.tags)
  return &_impl_.tags_;
}

// string tensorflow_version = 5;
inline void MetaGraphDef_MetaInfoDef::clear_tensorflow_version() {
  _impl_.tensorflow_version_.ClearToEmpty();
}
inline const std::string& MetaGraphDef_MetaInfoDef::tensorflow_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  return _internal_tensorflow_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MetaGraphDef_MetaInfoDef::set_tensorflow_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tensorflow_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}
inline std::string* MetaGraphDef_MetaInfoDef::mutable_tensorflow_version() {
  std::string* _s = _internal_mutable_tensorflow_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  return _s;
}
inline const std::string& MetaGraphDef_MetaInfoDef::_internal_tensorflow_version() const {
  return _impl_.tensorflow_version_.Get();
}
inline void MetaGraphDef_MetaInfoDef::_internal_set_tensorflow_version(const std::string& value) {
  
  _impl_.tensorflow_version_.Set(value, GetArenaForAllocation());
}
inline std::string* MetaGraphDef_MetaInfoDef::_internal_mutable_tensorflow_version() {
  
  return _impl_.tensorflow_version_.Mutable(GetArenaForAllocation());
}
inline std::string* MetaGraphDef_MetaInfoDef::release_tensorflow_version() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
  return _impl_.tensorflow_version_.Release();
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_tensorflow_version(std::string* tensorflow_version) {
  if (tensorflow_version != nullptr) {
    
  } else {
    
  }
  _impl_.tensorflow_version_.SetAllocated(tensorflow_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tensorflow_version_.IsDefault()) {
    _impl_.tensorflow_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_version)
}

// string tensorflow_git_version = 6;
inline void MetaGraphDef_MetaInfoDef::clear_tensorflow_git_version() {
  _impl_.tensorflow_git_version_.ClearToEmpty();
}
inline const std::string& MetaGraphDef_MetaInfoDef::tensorflow_git_version() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  return _internal_tensorflow_git_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MetaGraphDef_MetaInfoDef::set_tensorflow_git_version(ArgT0&& arg0, ArgT... args) {
 
 _impl_.tensorflow_git_version_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}
inline std::string* MetaGraphDef_MetaInfoDef::mutable_tensorflow_git_version() {
  std::string* _s = _internal_mutable_tensorflow_git_version();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  return _s;
}
inline const std::string& MetaGraphDef_MetaInfoDef::_internal_tensorflow_git_version() const {
  return _impl_.tensorflow_git_version_.Get();
}
inline void MetaGraphDef_MetaInfoDef::_internal_set_tensorflow_git_version(const std::string& value) {
  
  _impl_.tensorflow_git_version_.Set(value, GetArenaForAllocation());
}
inline std::string* MetaGraphDef_MetaInfoDef::_internal_mutable_tensorflow_git_version() {
  
  return _impl_.tensorflow_git_version_.Mutable(GetArenaForAllocation());
}
inline std::string* MetaGraphDef_MetaInfoDef::release_tensorflow_git_version() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
  return _impl_.tensorflow_git_version_.Release();
}
inline void MetaGraphDef_MetaInfoDef::set_allocated_tensorflow_git_version(std::string* tensorflow_git_version) {
  if (tensorflow_git_version != nullptr) {
    
  } else {
    
  }
  _impl_.tensorflow_git_version_.SetAllocated(tensorflow_git_version, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.tensorflow_git_version_.IsDefault()) {
    _impl_.tensorflow_git_version_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.MetaInfoDef.tensorflow_git_version)
}

// bool stripped_default_attrs = 7;
inline void MetaGraphDef_MetaInfoDef::clear_stripped_default_attrs() {
  _impl_.stripped_default_attrs_ = false;
}
inline bool MetaGraphDef_MetaInfoDef::_internal_stripped_default_attrs() const {
  return _impl_.stripped_default_attrs_;
}
inline bool MetaGraphDef_MetaInfoDef::stripped_default_attrs() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.MetaInfoDef.stripped_default_attrs)
  return _internal_stripped_default_attrs();
}
inline void MetaGraphDef_MetaInfoDef::_internal_set_stripped_default_attrs(bool value) {
  
  _impl_.stripped_default_attrs_ = value;
}
inline void MetaGraphDef_MetaInfoDef::set_stripped_default_attrs(bool value) {
  _internal_set_stripped_default_attrs(value);
  // @@protoc_insertion_point(field_set:tensorflow.MetaGraphDef.MetaInfoDef.stripped_default_attrs)
}

// map<string, string> function_aliases = 8;
inline int MetaGraphDef_MetaInfoDef::_internal_function_aliases_size() const {
  return _impl_.function_aliases_.size();
}
inline int MetaGraphDef_MetaInfoDef::function_aliases_size() const {
  return _internal_function_aliases_size();
}
inline void MetaGraphDef_MetaInfoDef::clear_function_aliases() {
  _impl_.function_aliases_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
MetaGraphDef_MetaInfoDef::_internal_function_aliases() const {
  return _impl_.function_aliases_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
MetaGraphDef_MetaInfoDef::function_aliases() const {
  // @@protoc_insertion_point(field_map:tensorflow.MetaGraphDef.MetaInfoDef.function_aliases)
  return _internal_function_aliases();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
MetaGraphDef_MetaInfoDef::_internal_mutable_function_aliases() {
  return _impl_.function_aliases_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
MetaGraphDef_MetaInfoDef::mutable_function_aliases() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.MetaGraphDef.MetaInfoDef.function_aliases)
  return _internal_mutable_function_aliases();
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// MetaGraphDef

// .tensorflow.MetaGraphDef.MetaInfoDef meta_info_def = 1;
inline bool MetaGraphDef::_internal_has_meta_info_def() const {
  return this != internal_default_instance() && _impl_.meta_info_def_ != nullptr;
}
inline bool MetaGraphDef::has_meta_info_def() const {
  return _internal_has_meta_info_def();
}
inline void MetaGraphDef::clear_meta_info_def() {
  if (GetArenaForAllocation() == nullptr && _impl_.meta_info_def_ != nullptr) {
    delete _impl_.meta_info_def_;
  }
  _impl_.meta_info_def_ = nullptr;
}
inline const ::tensorflow::MetaGraphDef_MetaInfoDef& MetaGraphDef::_internal_meta_info_def() const {
  const ::tensorflow::MetaGraphDef_MetaInfoDef* p = _impl_.meta_info_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::MetaGraphDef_MetaInfoDef&>(
      ::tensorflow::_MetaGraphDef_MetaInfoDef_default_instance_);
}
inline const ::tensorflow::MetaGraphDef_MetaInfoDef& MetaGraphDef::meta_info_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.meta_info_def)
  return _internal_meta_info_def();
}
inline void MetaGraphDef::unsafe_arena_set_allocated_meta_info_def(
    ::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.meta_info_def_);
  }
  _impl_.meta_info_def_ = meta_info_def;
  if (meta_info_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.meta_info_def)
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::release_meta_info_def() {
  
  ::tensorflow::MetaGraphDef_MetaInfoDef* temp = _impl_.meta_info_def_;
  _impl_.meta_info_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::unsafe_arena_release_meta_info_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.meta_info_def)
  
  ::tensorflow::MetaGraphDef_MetaInfoDef* temp = _impl_.meta_info_def_;
  _impl_.meta_info_def_ = nullptr;
  return temp;
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::_internal_mutable_meta_info_def() {
  
  if (_impl_.meta_info_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MetaGraphDef_MetaInfoDef>(GetArenaForAllocation());
    _impl_.meta_info_def_ = p;
  }
  return _impl_.meta_info_def_;
}
inline ::tensorflow::MetaGraphDef_MetaInfoDef* MetaGraphDef::mutable_meta_info_def() {
  ::tensorflow::MetaGraphDef_MetaInfoDef* _msg = _internal_mutable_meta_info_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.meta_info_def)
  return _msg;
}
inline void MetaGraphDef::set_allocated_meta_info_def(::tensorflow::MetaGraphDef_MetaInfoDef* meta_info_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.meta_info_def_;
  }
  if (meta_info_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(meta_info_def);
    if (message_arena != submessage_arena) {
      meta_info_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, meta_info_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.meta_info_def_ = meta_info_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.meta_info_def)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool MetaGraphDef::_internal_has_graph_def() const {
  return this != internal_default_instance() && _impl_.graph_def_ != nullptr;
}
inline bool MetaGraphDef::has_graph_def() const {
  return _internal_has_graph_def();
}
inline const ::tensorflow::GraphDef& MetaGraphDef::_internal_graph_def() const {
  const ::tensorflow::GraphDef* p = _impl_.graph_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& MetaGraphDef::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.graph_def)
  return _internal_graph_def();
}
inline void MetaGraphDef::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  _impl_.graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.graph_def)
}
inline ::tensorflow::GraphDef* MetaGraphDef::release_graph_def() {
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* MetaGraphDef::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.graph_def)
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* MetaGraphDef::_internal_mutable_graph_def() {
  
  if (_impl_.graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.graph_def_ = p;
  }
  return _impl_.graph_def_;
}
inline ::tensorflow::GraphDef* MetaGraphDef::mutable_graph_def() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.graph_def)
  return _msg;
}
inline void MetaGraphDef::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def));
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.graph_def)
}

// .tensorflow.SaverDef saver_def = 3;
inline bool MetaGraphDef::_internal_has_saver_def() const {
  return this != internal_default_instance() && _impl_.saver_def_ != nullptr;
}
inline bool MetaGraphDef::has_saver_def() const {
  return _internal_has_saver_def();
}
inline const ::tensorflow::SaverDef& MetaGraphDef::_internal_saver_def() const {
  const ::tensorflow::SaverDef* p = _impl_.saver_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SaverDef&>(
      ::tensorflow::_SaverDef_default_instance_);
}
inline const ::tensorflow::SaverDef& MetaGraphDef::saver_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.saver_def)
  return _internal_saver_def();
}
inline void MetaGraphDef::unsafe_arena_set_allocated_saver_def(
    ::tensorflow::SaverDef* saver_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.saver_def_);
  }
  _impl_.saver_def_ = saver_def;
  if (saver_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.saver_def)
}
inline ::tensorflow::SaverDef* MetaGraphDef::release_saver_def() {
  
  ::tensorflow::SaverDef* temp = _impl_.saver_def_;
  _impl_.saver_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SaverDef* MetaGraphDef::unsafe_arena_release_saver_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.saver_def)
  
  ::tensorflow::SaverDef* temp = _impl_.saver_def_;
  _impl_.saver_def_ = nullptr;
  return temp;
}
inline ::tensorflow::SaverDef* MetaGraphDef::_internal_mutable_saver_def() {
  
  if (_impl_.saver_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SaverDef>(GetArenaForAllocation());
    _impl_.saver_def_ = p;
  }
  return _impl_.saver_def_;
}
inline ::tensorflow::SaverDef* MetaGraphDef::mutable_saver_def() {
  ::tensorflow::SaverDef* _msg = _internal_mutable_saver_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.saver_def)
  return _msg;
}
inline void MetaGraphDef::set_allocated_saver_def(::tensorflow::SaverDef* saver_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.saver_def_);
  }
  if (saver_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(saver_def));
    if (message_arena != submessage_arena) {
      saver_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, saver_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.saver_def_ = saver_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.saver_def)
}

// map<string, .tensorflow.CollectionDef> collection_def = 4;
inline int MetaGraphDef::_internal_collection_def_size() const {
  return _impl_.collection_def_.size();
}
inline int MetaGraphDef::collection_def_size() const {
  return _internal_collection_def_size();
}
inline void MetaGraphDef::clear_collection_def() {
  _impl_.collection_def_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >&
MetaGraphDef::_internal_collection_def() const {
  return _impl_.collection_def_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >&
MetaGraphDef::collection_def() const {
  // @@protoc_insertion_point(field_map:tensorflow.MetaGraphDef.collection_def)
  return _internal_collection_def();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >*
MetaGraphDef::_internal_mutable_collection_def() {
  return _impl_.collection_def_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::CollectionDef >*
MetaGraphDef::mutable_collection_def() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.MetaGraphDef.collection_def)
  return _internal_mutable_collection_def();
}

// map<string, .tensorflow.SignatureDef> signature_def = 5;
inline int MetaGraphDef::_internal_signature_def_size() const {
  return _impl_.signature_def_.size();
}
inline int MetaGraphDef::signature_def_size() const {
  return _internal_signature_def_size();
}
inline void MetaGraphDef::clear_signature_def() {
  _impl_.signature_def_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >&
MetaGraphDef::_internal_signature_def() const {
  return _impl_.signature_def_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >&
MetaGraphDef::signature_def() const {
  // @@protoc_insertion_point(field_map:tensorflow.MetaGraphDef.signature_def)
  return _internal_signature_def();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >*
MetaGraphDef::_internal_mutable_signature_def() {
  return _impl_.signature_def_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::SignatureDef >*
MetaGraphDef::mutable_signature_def() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.MetaGraphDef.signature_def)
  return _internal_mutable_signature_def();
}

// repeated .tensorflow.AssetFileDef asset_file_def = 6;
inline int MetaGraphDef::_internal_asset_file_def_size() const {
  return _impl_.asset_file_def_.size();
}
inline int MetaGraphDef::asset_file_def_size() const {
  return _internal_asset_file_def_size();
}
inline void MetaGraphDef::clear_asset_file_def() {
  _impl_.asset_file_def_.Clear();
}
inline ::tensorflow::AssetFileDef* MetaGraphDef::mutable_asset_file_def(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.asset_file_def)
  return _impl_.asset_file_def_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >*
MetaGraphDef::mutable_asset_file_def() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MetaGraphDef.asset_file_def)
  return &_impl_.asset_file_def_;
}
inline const ::tensorflow::AssetFileDef& MetaGraphDef::_internal_asset_file_def(int index) const {
  return _impl_.asset_file_def_.Get(index);
}
inline const ::tensorflow::AssetFileDef& MetaGraphDef::asset_file_def(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.asset_file_def)
  return _internal_asset_file_def(index);
}
inline ::tensorflow::AssetFileDef* MetaGraphDef::_internal_add_asset_file_def() {
  return _impl_.asset_file_def_.Add();
}
inline ::tensorflow::AssetFileDef* MetaGraphDef::add_asset_file_def() {
  ::tensorflow::AssetFileDef* _add = _internal_add_asset_file_def();
  // @@protoc_insertion_point(field_add:tensorflow.MetaGraphDef.asset_file_def)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::AssetFileDef >&
MetaGraphDef::asset_file_def() const {
  // @@protoc_insertion_point(field_list:tensorflow.MetaGraphDef.asset_file_def)
  return _impl_.asset_file_def_;
}

// .tensorflow.SavedObjectGraph object_graph_def = 7;
inline bool MetaGraphDef::_internal_has_object_graph_def() const {
  return this != internal_default_instance() && _impl_.object_graph_def_ != nullptr;
}
inline bool MetaGraphDef::has_object_graph_def() const {
  return _internal_has_object_graph_def();
}
inline const ::tensorflow::SavedObjectGraph& MetaGraphDef::_internal_object_graph_def() const {
  const ::tensorflow::SavedObjectGraph* p = _impl_.object_graph_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::SavedObjectGraph&>(
      ::tensorflow::_SavedObjectGraph_default_instance_);
}
inline const ::tensorflow::SavedObjectGraph& MetaGraphDef::object_graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.MetaGraphDef.object_graph_def)
  return _internal_object_graph_def();
}
inline void MetaGraphDef::unsafe_arena_set_allocated_object_graph_def(
    ::tensorflow::SavedObjectGraph* object_graph_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.object_graph_def_);
  }
  _impl_.object_graph_def_ = object_graph_def;
  if (object_graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MetaGraphDef.object_graph_def)
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::release_object_graph_def() {
  
  ::tensorflow::SavedObjectGraph* temp = _impl_.object_graph_def_;
  _impl_.object_graph_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::unsafe_arena_release_object_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.MetaGraphDef.object_graph_def)
  
  ::tensorflow::SavedObjectGraph* temp = _impl_.object_graph_def_;
  _impl_.object_graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::_internal_mutable_object_graph_def() {
  
  if (_impl_.object_graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::SavedObjectGraph>(GetArenaForAllocation());
    _impl_.object_graph_def_ = p;
  }
  return _impl_.object_graph_def_;
}
inline ::tensorflow::SavedObjectGraph* MetaGraphDef::mutable_object_graph_def() {
  ::tensorflow::SavedObjectGraph* _msg = _internal_mutable_object_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.MetaGraphDef.object_graph_def)
  return _msg;
}
inline void MetaGraphDef::set_allocated_object_graph_def(::tensorflow::SavedObjectGraph* object_graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.object_graph_def_);
  }
  if (object_graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(object_graph_def));
    if (message_arena != submessage_arena) {
      object_graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, object_graph_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.object_graph_def_ = object_graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MetaGraphDef.object_graph_def)
}

// -------------------------------------------------------------------

// CollectionDef_NodeList

// repeated string value = 1;
inline int CollectionDef_NodeList::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int CollectionDef_NodeList::value_size() const {
  return _internal_value_size();
}
inline void CollectionDef_NodeList::clear_value() {
  _impl_.value_.Clear();
}
inline std::string* CollectionDef_NodeList::add_value() {
  std::string* _s = _internal_add_value();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CollectionDef.NodeList.value)
  return _s;
}
inline const std::string& CollectionDef_NodeList::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline const std::string& CollectionDef_NodeList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.NodeList.value)
  return _internal_value(index);
}
inline std::string* CollectionDef_NodeList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.NodeList.value)
  return _impl_.value_.Mutable(index);
}
inline void CollectionDef_NodeList::set_value(int index, const std::string& value) {
  _impl_.value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::set_value(int index, std::string&& value) {
  _impl_.value_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::set_value(int index, const char* value, size_t size) {
  _impl_.value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CollectionDef.NodeList.value)
}
inline std::string* CollectionDef_NodeList::_internal_add_value() {
  return _impl_.value_.Add();
}
inline void CollectionDef_NodeList::add_value(const std::string& value) {
  _impl_.value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::add_value(std::string&& value) {
  _impl_.value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::add_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CollectionDef.NodeList.value)
}
inline void CollectionDef_NodeList::add_value(const char* value, size_t size) {
  _impl_.value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CollectionDef.NodeList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CollectionDef_NodeList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.NodeList.value)
  return _impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CollectionDef_NodeList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.NodeList.value)
  return &_impl_.value_;
}

// -------------------------------------------------------------------

// CollectionDef_BytesList

// repeated bytes value = 1;
inline int CollectionDef_BytesList::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int CollectionDef_BytesList::value_size() const {
  return _internal_value_size();
}
inline void CollectionDef_BytesList::clear_value() {
  _impl_.value_.Clear();
}
inline std::string* CollectionDef_BytesList::add_value() {
  std::string* _s = _internal_add_value();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CollectionDef.BytesList.value)
  return _s;
}
inline const std::string& CollectionDef_BytesList::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline const std::string& CollectionDef_BytesList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.BytesList.value)
  return _internal_value(index);
}
inline std::string* CollectionDef_BytesList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.BytesList.value)
  return _impl_.value_.Mutable(index);
}
inline void CollectionDef_BytesList::set_value(int index, const std::string& value) {
  _impl_.value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::set_value(int index, std::string&& value) {
  _impl_.value_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::set_value(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::set_value(int index, const void* value, size_t size) {
  _impl_.value_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CollectionDef.BytesList.value)
}
inline std::string* CollectionDef_BytesList::_internal_add_value() {
  return _impl_.value_.Add();
}
inline void CollectionDef_BytesList::add_value(const std::string& value) {
  _impl_.value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::add_value(std::string&& value) {
  _impl_.value_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::add_value(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.value_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CollectionDef.BytesList.value)
}
inline void CollectionDef_BytesList::add_value(const void* value, size_t size) {
  _impl_.value_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CollectionDef.BytesList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CollectionDef_BytesList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.BytesList.value)
  return _impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CollectionDef_BytesList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.BytesList.value)
  return &_impl_.value_;
}

// -------------------------------------------------------------------

// CollectionDef_Int64List

// repeated int64 value = 1 [packed = true];
inline int CollectionDef_Int64List::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int CollectionDef_Int64List::value_size() const {
  return _internal_value_size();
}
inline void CollectionDef_Int64List::clear_value() {
  _impl_.value_.Clear();
}
inline int64_t CollectionDef_Int64List::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline int64_t CollectionDef_Int64List::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.Int64List.value)
  return _internal_value(index);
}
inline void CollectionDef_Int64List::set_value(int index, int64_t value) {
  _impl_.value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.Int64List.value)
}
inline void CollectionDef_Int64List::_internal_add_value(int64_t value) {
  _impl_.value_.Add(value);
}
inline void CollectionDef_Int64List::add_value(int64_t value) {
  _internal_add_value(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.Int64List.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
CollectionDef_Int64List::_internal_value() const {
  return _impl_.value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
CollectionDef_Int64List::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.Int64List.value)
  return _internal_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
CollectionDef_Int64List::_internal_mutable_value() {
  return &_impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
CollectionDef_Int64List::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.Int64List.value)
  return _internal_mutable_value();
}

// -------------------------------------------------------------------

// CollectionDef_FloatList

// repeated float value = 1 [packed = true];
inline int CollectionDef_FloatList::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int CollectionDef_FloatList::value_size() const {
  return _internal_value_size();
}
inline void CollectionDef_FloatList::clear_value() {
  _impl_.value_.Clear();
}
inline float CollectionDef_FloatList::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline float CollectionDef_FloatList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.FloatList.value)
  return _internal_value(index);
}
inline void CollectionDef_FloatList::set_value(int index, float value) {
  _impl_.value_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CollectionDef.FloatList.value)
}
inline void CollectionDef_FloatList::_internal_add_value(float value) {
  _impl_.value_.Add(value);
}
inline void CollectionDef_FloatList::add_value(float value) {
  _internal_add_value(value);
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.FloatList.value)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
CollectionDef_FloatList::_internal_value() const {
  return _impl_.value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
CollectionDef_FloatList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.FloatList.value)
  return _internal_value();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
CollectionDef_FloatList::_internal_mutable_value() {
  return &_impl_.value_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
CollectionDef_FloatList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.FloatList.value)
  return _internal_mutable_value();
}

// -------------------------------------------------------------------

// CollectionDef_AnyList

// repeated .google.protobuf.Any value = 1;
inline int CollectionDef_AnyList::_internal_value_size() const {
  return _impl_.value_.size();
}
inline int CollectionDef_AnyList::value_size() const {
  return _internal_value_size();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* CollectionDef_AnyList::mutable_value(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.AnyList.value)
  return _impl_.value_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >*
CollectionDef_AnyList::mutable_value() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CollectionDef.AnyList.value)
  return &_impl_.value_;
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& CollectionDef_AnyList::_internal_value(int index) const {
  return _impl_.value_.Get(index);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& CollectionDef_AnyList::value(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.AnyList.value)
  return _internal_value(index);
}
inline ::PROTOBUF_NAMESPACE_ID::Any* CollectionDef_AnyList::_internal_add_value() {
  return _impl_.value_.Add();
}
inline ::PROTOBUF_NAMESPACE_ID::Any* CollectionDef_AnyList::add_value() {
  ::PROTOBUF_NAMESPACE_ID::Any* _add = _internal_add_value();
  // @@protoc_insertion_point(field_add:tensorflow.CollectionDef.AnyList.value)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::PROTOBUF_NAMESPACE_ID::Any >&
CollectionDef_AnyList::value() const {
  // @@protoc_insertion_point(field_list:tensorflow.CollectionDef.AnyList.value)
  return _impl_.value_;
}

// -------------------------------------------------------------------

// CollectionDef

// .tensorflow.CollectionDef.NodeList node_list = 1;
inline bool CollectionDef::_internal_has_node_list() const {
  return kind_case() == kNodeList;
}
inline bool CollectionDef::has_node_list() const {
  return _internal_has_node_list();
}
inline void CollectionDef::set_has_node_list() {
  _impl_._oneof_case_[0] = kNodeList;
}
inline void CollectionDef::clear_node_list() {
  if (_internal_has_node_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.node_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::release_node_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.node_list)
  if (_internal_has_node_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_NodeList* temp = _impl_.kind_.node_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.node_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CollectionDef_NodeList& CollectionDef::_internal_node_list() const {
  return _internal_has_node_list()
      ? *_impl_.kind_.node_list_
      : reinterpret_cast< ::tensorflow::CollectionDef_NodeList&>(::tensorflow::_CollectionDef_NodeList_default_instance_);
}
inline const ::tensorflow::CollectionDef_NodeList& CollectionDef::node_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.node_list)
  return _internal_node_list();
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::unsafe_arena_release_node_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.node_list)
  if (_internal_has_node_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_NodeList* temp = _impl_.kind_.node_list_;
    _impl_.kind_.node_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_node_list(::tensorflow::CollectionDef_NodeList* node_list) {
  clear_kind();
  if (node_list) {
    set_has_node_list();
    _impl_.kind_.node_list_ = node_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.node_list)
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::_internal_mutable_node_list() {
  if (!_internal_has_node_list()) {
    clear_kind();
    set_has_node_list();
    _impl_.kind_.node_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_NodeList >(GetArenaForAllocation());
  }
  return _impl_.kind_.node_list_;
}
inline ::tensorflow::CollectionDef_NodeList* CollectionDef::mutable_node_list() {
  ::tensorflow::CollectionDef_NodeList* _msg = _internal_mutable_node_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.node_list)
  return _msg;
}

// .tensorflow.CollectionDef.BytesList bytes_list = 2;
inline bool CollectionDef::_internal_has_bytes_list() const {
  return kind_case() == kBytesList;
}
inline bool CollectionDef::has_bytes_list() const {
  return _internal_has_bytes_list();
}
inline void CollectionDef::set_has_bytes_list() {
  _impl_._oneof_case_[0] = kBytesList;
}
inline void CollectionDef::clear_bytes_list() {
  if (_internal_has_bytes_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.bytes_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::release_bytes_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.bytes_list)
  if (_internal_has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_BytesList* temp = _impl_.kind_.bytes_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.bytes_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CollectionDef_BytesList& CollectionDef::_internal_bytes_list() const {
  return _internal_has_bytes_list()
      ? *_impl_.kind_.bytes_list_
      : reinterpret_cast< ::tensorflow::CollectionDef_BytesList&>(::tensorflow::_CollectionDef_BytesList_default_instance_);
}
inline const ::tensorflow::CollectionDef_BytesList& CollectionDef::bytes_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.bytes_list)
  return _internal_bytes_list();
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::unsafe_arena_release_bytes_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.bytes_list)
  if (_internal_has_bytes_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_BytesList* temp = _impl_.kind_.bytes_list_;
    _impl_.kind_.bytes_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_bytes_list(::tensorflow::CollectionDef_BytesList* bytes_list) {
  clear_kind();
  if (bytes_list) {
    set_has_bytes_list();
    _impl_.kind_.bytes_list_ = bytes_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.bytes_list)
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::_internal_mutable_bytes_list() {
  if (!_internal_has_bytes_list()) {
    clear_kind();
    set_has_bytes_list();
    _impl_.kind_.bytes_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_BytesList >(GetArenaForAllocation());
  }
  return _impl_.kind_.bytes_list_;
}
inline ::tensorflow::CollectionDef_BytesList* CollectionDef::mutable_bytes_list() {
  ::tensorflow::CollectionDef_BytesList* _msg = _internal_mutable_bytes_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.bytes_list)
  return _msg;
}

// .tensorflow.CollectionDef.Int64List int64_list = 3;
inline bool CollectionDef::_internal_has_int64_list() const {
  return kind_case() == kInt64List;
}
inline bool CollectionDef::has_int64_list() const {
  return _internal_has_int64_list();
}
inline void CollectionDef::set_has_int64_list() {
  _impl_._oneof_case_[0] = kInt64List;
}
inline void CollectionDef::clear_int64_list() {
  if (_internal_has_int64_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.int64_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::release_int64_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.int64_list)
  if (_internal_has_int64_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_Int64List* temp = _impl_.kind_.int64_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.int64_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CollectionDef_Int64List& CollectionDef::_internal_int64_list() const {
  return _internal_has_int64_list()
      ? *_impl_.kind_.int64_list_
      : reinterpret_cast< ::tensorflow::CollectionDef_Int64List&>(::tensorflow::_CollectionDef_Int64List_default_instance_);
}
inline const ::tensorflow::CollectionDef_Int64List& CollectionDef::int64_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.int64_list)
  return _internal_int64_list();
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::unsafe_arena_release_int64_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.int64_list)
  if (_internal_has_int64_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_Int64List* temp = _impl_.kind_.int64_list_;
    _impl_.kind_.int64_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_int64_list(::tensorflow::CollectionDef_Int64List* int64_list) {
  clear_kind();
  if (int64_list) {
    set_has_int64_list();
    _impl_.kind_.int64_list_ = int64_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.int64_list)
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::_internal_mutable_int64_list() {
  if (!_internal_has_int64_list()) {
    clear_kind();
    set_has_int64_list();
    _impl_.kind_.int64_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_Int64List >(GetArenaForAllocation());
  }
  return _impl_.kind_.int64_list_;
}
inline ::tensorflow::CollectionDef_Int64List* CollectionDef::mutable_int64_list() {
  ::tensorflow::CollectionDef_Int64List* _msg = _internal_mutable_int64_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.int64_list)
  return _msg;
}

// .tensorflow.CollectionDef.FloatList float_list = 4;
inline bool CollectionDef::_internal_has_float_list() const {
  return kind_case() == kFloatList;
}
inline bool CollectionDef::has_float_list() const {
  return _internal_has_float_list();
}
inline void CollectionDef::set_has_float_list() {
  _impl_._oneof_case_[0] = kFloatList;
}
inline void CollectionDef::clear_float_list() {
  if (_internal_has_float_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.float_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::release_float_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.float_list)
  if (_internal_has_float_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_FloatList* temp = _impl_.kind_.float_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.float_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CollectionDef_FloatList& CollectionDef::_internal_float_list() const {
  return _internal_has_float_list()
      ? *_impl_.kind_.float_list_
      : reinterpret_cast< ::tensorflow::CollectionDef_FloatList&>(::tensorflow::_CollectionDef_FloatList_default_instance_);
}
inline const ::tensorflow::CollectionDef_FloatList& CollectionDef::float_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.float_list)
  return _internal_float_list();
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::unsafe_arena_release_float_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.float_list)
  if (_internal_has_float_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_FloatList* temp = _impl_.kind_.float_list_;
    _impl_.kind_.float_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_float_list(::tensorflow::CollectionDef_FloatList* float_list) {
  clear_kind();
  if (float_list) {
    set_has_float_list();
    _impl_.kind_.float_list_ = float_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.float_list)
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::_internal_mutable_float_list() {
  if (!_internal_has_float_list()) {
    clear_kind();
    set_has_float_list();
    _impl_.kind_.float_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_FloatList >(GetArenaForAllocation());
  }
  return _impl_.kind_.float_list_;
}
inline ::tensorflow::CollectionDef_FloatList* CollectionDef::mutable_float_list() {
  ::tensorflow::CollectionDef_FloatList* _msg = _internal_mutable_float_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.float_list)
  return _msg;
}

// .tensorflow.CollectionDef.AnyList any_list = 5;
inline bool CollectionDef::_internal_has_any_list() const {
  return kind_case() == kAnyList;
}
inline bool CollectionDef::has_any_list() const {
  return _internal_has_any_list();
}
inline void CollectionDef::set_has_any_list() {
  _impl_._oneof_case_[0] = kAnyList;
}
inline void CollectionDef::clear_any_list() {
  if (_internal_has_any_list()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.any_list_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::release_any_list() {
  // @@protoc_insertion_point(field_release:tensorflow.CollectionDef.any_list)
  if (_internal_has_any_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_AnyList* temp = _impl_.kind_.any_list_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.any_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::CollectionDef_AnyList& CollectionDef::_internal_any_list() const {
  return _internal_has_any_list()
      ? *_impl_.kind_.any_list_
      : reinterpret_cast< ::tensorflow::CollectionDef_AnyList&>(::tensorflow::_CollectionDef_AnyList_default_instance_);
}
inline const ::tensorflow::CollectionDef_AnyList& CollectionDef::any_list() const {
  // @@protoc_insertion_point(field_get:tensorflow.CollectionDef.any_list)
  return _internal_any_list();
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::unsafe_arena_release_any_list() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.CollectionDef.any_list)
  if (_internal_has_any_list()) {
    clear_has_kind();
    ::tensorflow::CollectionDef_AnyList* temp = _impl_.kind_.any_list_;
    _impl_.kind_.any_list_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void CollectionDef::unsafe_arena_set_allocated_any_list(::tensorflow::CollectionDef_AnyList* any_list) {
  clear_kind();
  if (any_list) {
    set_has_any_list();
    _impl_.kind_.any_list_ = any_list;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CollectionDef.any_list)
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::_internal_mutable_any_list() {
  if (!_internal_has_any_list()) {
    clear_kind();
    set_has_any_list();
    _impl_.kind_.any_list_ = CreateMaybeMessage< ::tensorflow::CollectionDef_AnyList >(GetArenaForAllocation());
  }
  return _impl_.kind_.any_list_;
}
inline ::tensorflow::CollectionDef_AnyList* CollectionDef::mutable_any_list() {
  ::tensorflow::CollectionDef_AnyList* _msg = _internal_mutable_any_list();
  // @@protoc_insertion_point(field_mutable:tensorflow.CollectionDef.any_list)
  return _msg;
}

inline bool CollectionDef::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void CollectionDef::clear_has_kind() {
  _impl_._oneof_case_[0] = KIND_NOT_SET;
}
inline CollectionDef::KindCase CollectionDef::kind_case() const {
  return CollectionDef::KindCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// TensorInfo_CooSparse

// string values_tensor_name = 1;
inline void TensorInfo_CooSparse::clear_values_tensor_name() {
  _impl_.values_tensor_name_.ClearToEmpty();
}
inline const std::string& TensorInfo_CooSparse::values_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  return _internal_values_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorInfo_CooSparse::set_values_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.values_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}
inline std::string* TensorInfo_CooSparse::mutable_values_tensor_name() {
  std::string* _s = _internal_mutable_values_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  return _s;
}
inline const std::string& TensorInfo_CooSparse::_internal_values_tensor_name() const {
  return _impl_.values_tensor_name_.Get();
}
inline void TensorInfo_CooSparse::_internal_set_values_tensor_name(const std::string& value) {
  
  _impl_.values_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorInfo_CooSparse::_internal_mutable_values_tensor_name() {
  
  return _impl_.values_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorInfo_CooSparse::release_values_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CooSparse.values_tensor_name)
  return _impl_.values_tensor_name_.Release();
}
inline void TensorInfo_CooSparse::set_allocated_values_tensor_name(std::string* values_tensor_name) {
  if (values_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.values_tensor_name_.SetAllocated(values_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.values_tensor_name_.IsDefault()) {
    _impl_.values_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CooSparse.values_tensor_name)
}

// string indices_tensor_name = 2;
inline void TensorInfo_CooSparse::clear_indices_tensor_name() {
  _impl_.indices_tensor_name_.ClearToEmpty();
}
inline const std::string& TensorInfo_CooSparse::indices_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  return _internal_indices_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorInfo_CooSparse::set_indices_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.indices_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}
inline std::string* TensorInfo_CooSparse::mutable_indices_tensor_name() {
  std::string* _s = _internal_mutable_indices_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  return _s;
}
inline const std::string& TensorInfo_CooSparse::_internal_indices_tensor_name() const {
  return _impl_.indices_tensor_name_.Get();
}
inline void TensorInfo_CooSparse::_internal_set_indices_tensor_name(const std::string& value) {
  
  _impl_.indices_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorInfo_CooSparse::_internal_mutable_indices_tensor_name() {
  
  return _impl_.indices_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorInfo_CooSparse::release_indices_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
  return _impl_.indices_tensor_name_.Release();
}
inline void TensorInfo_CooSparse::set_allocated_indices_tensor_name(std::string* indices_tensor_name) {
  if (indices_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.indices_tensor_name_.SetAllocated(indices_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.indices_tensor_name_.IsDefault()) {
    _impl_.indices_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CooSparse.indices_tensor_name)
}

// string dense_shape_tensor_name = 3;
inline void TensorInfo_CooSparse::clear_dense_shape_tensor_name() {
  _impl_.dense_shape_tensor_name_.ClearToEmpty();
}
inline const std::string& TensorInfo_CooSparse::dense_shape_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  return _internal_dense_shape_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorInfo_CooSparse::set_dense_shape_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dense_shape_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}
inline std::string* TensorInfo_CooSparse::mutable_dense_shape_tensor_name() {
  std::string* _s = _internal_mutable_dense_shape_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  return _s;
}
inline const std::string& TensorInfo_CooSparse::_internal_dense_shape_tensor_name() const {
  return _impl_.dense_shape_tensor_name_.Get();
}
inline void TensorInfo_CooSparse::_internal_set_dense_shape_tensor_name(const std::string& value) {
  
  _impl_.dense_shape_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorInfo_CooSparse::_internal_mutable_dense_shape_tensor_name() {
  
  return _impl_.dense_shape_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorInfo_CooSparse::release_dense_shape_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
  return _impl_.dense_shape_tensor_name_.Release();
}
inline void TensorInfo_CooSparse::set_allocated_dense_shape_tensor_name(std::string* dense_shape_tensor_name) {
  if (dense_shape_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.dense_shape_tensor_name_.SetAllocated(dense_shape_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dense_shape_tensor_name_.IsDefault()) {
    _impl_.dense_shape_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CooSparse.dense_shape_tensor_name)
}

// -------------------------------------------------------------------

// TensorInfo_CompositeTensor

// .tensorflow.TypeSpecProto type_spec = 1;
inline bool TensorInfo_CompositeTensor::_internal_has_type_spec() const {
  return this != internal_default_instance() && _impl_.type_spec_ != nullptr;
}
inline bool TensorInfo_CompositeTensor::has_type_spec() const {
  return _internal_has_type_spec();
}
inline const ::tensorflow::TypeSpecProto& TensorInfo_CompositeTensor::_internal_type_spec() const {
  const ::tensorflow::TypeSpecProto* p = _impl_.type_spec_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TypeSpecProto&>(
      ::tensorflow::_TypeSpecProto_default_instance_);
}
inline const ::tensorflow::TypeSpecProto& TensorInfo_CompositeTensor::type_spec() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CompositeTensor.type_spec)
  return _internal_type_spec();
}
inline void TensorInfo_CompositeTensor::unsafe_arena_set_allocated_type_spec(
    ::tensorflow::TypeSpecProto* type_spec) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_spec_);
  }
  _impl_.type_spec_ = type_spec;
  if (type_spec) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.CompositeTensor.type_spec)
}
inline ::tensorflow::TypeSpecProto* TensorInfo_CompositeTensor::release_type_spec() {
  
  ::tensorflow::TypeSpecProto* temp = _impl_.type_spec_;
  _impl_.type_spec_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TypeSpecProto* TensorInfo_CompositeTensor::unsafe_arena_release_type_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.CompositeTensor.type_spec)
  
  ::tensorflow::TypeSpecProto* temp = _impl_.type_spec_;
  _impl_.type_spec_ = nullptr;
  return temp;
}
inline ::tensorflow::TypeSpecProto* TensorInfo_CompositeTensor::_internal_mutable_type_spec() {
  
  if (_impl_.type_spec_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TypeSpecProto>(GetArenaForAllocation());
    _impl_.type_spec_ = p;
  }
  return _impl_.type_spec_;
}
inline ::tensorflow::TypeSpecProto* TensorInfo_CompositeTensor::mutable_type_spec() {
  ::tensorflow::TypeSpecProto* _msg = _internal_mutable_type_spec();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CompositeTensor.type_spec)
  return _msg;
}
inline void TensorInfo_CompositeTensor::set_allocated_type_spec(::tensorflow::TypeSpecProto* type_spec) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_spec_);
  }
  if (type_spec) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(type_spec));
    if (message_arena != submessage_arena) {
      type_spec = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type_spec, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.type_spec_ = type_spec;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.CompositeTensor.type_spec)
}

// repeated .tensorflow.TensorInfo components = 2;
inline int TensorInfo_CompositeTensor::_internal_components_size() const {
  return _impl_.components_.size();
}
inline int TensorInfo_CompositeTensor::components_size() const {
  return _internal_components_size();
}
inline void TensorInfo_CompositeTensor::clear_components() {
  _impl_.components_.Clear();
}
inline ::tensorflow::TensorInfo* TensorInfo_CompositeTensor::mutable_components(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.CompositeTensor.components)
  return _impl_.components_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorInfo >*
TensorInfo_CompositeTensor::mutable_components() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TensorInfo.CompositeTensor.components)
  return &_impl_.components_;
}
inline const ::tensorflow::TensorInfo& TensorInfo_CompositeTensor::_internal_components(int index) const {
  return _impl_.components_.Get(index);
}
inline const ::tensorflow::TensorInfo& TensorInfo_CompositeTensor::components(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.CompositeTensor.components)
  return _internal_components(index);
}
inline ::tensorflow::TensorInfo* TensorInfo_CompositeTensor::_internal_add_components() {
  return _impl_.components_.Add();
}
inline ::tensorflow::TensorInfo* TensorInfo_CompositeTensor::add_components() {
  ::tensorflow::TensorInfo* _add = _internal_add_components();
  // @@protoc_insertion_point(field_add:tensorflow.TensorInfo.CompositeTensor.components)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::TensorInfo >&
TensorInfo_CompositeTensor::components() const {
  // @@protoc_insertion_point(field_list:tensorflow.TensorInfo.CompositeTensor.components)
  return _impl_.components_;
}

// -------------------------------------------------------------------

// TensorInfo

// string name = 1;
inline bool TensorInfo::_internal_has_name() const {
  return encoding_case() == kName;
}
inline bool TensorInfo::has_name() const {
  return _internal_has_name();
}
inline void TensorInfo::set_has_name() {
  _impl_._oneof_case_[0] = kName;
}
inline void TensorInfo::clear_name() {
  if (_internal_has_name()) {
    _impl_.encoding_.name_.Destroy();
    clear_has_encoding();
  }
}
inline const std::string& TensorInfo::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline void TensorInfo::set_name(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_name()) {
    clear_encoding();
    set_has_name();
    _impl_.encoding_.name_.InitDefault();
  }
  _impl_.encoding_.name_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.name)
}
inline std::string* TensorInfo::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.name)
  return _s;
}
inline const std::string& TensorInfo::_internal_name() const {
  if (_internal_has_name()) {
    return _impl_.encoding_.name_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void TensorInfo::_internal_set_name(const std::string& value) {
  if (!_internal_has_name()) {
    clear_encoding();
    set_has_name();
    _impl_.encoding_.name_.InitDefault();
  }
  _impl_.encoding_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorInfo::_internal_mutable_name() {
  if (!_internal_has_name()) {
    clear_encoding();
    set_has_name();
    _impl_.encoding_.name_.InitDefault();
  }
  return _impl_.encoding_.name_.Mutable(      GetArenaForAllocation());
}
inline std::string* TensorInfo::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.name)
  if (_internal_has_name()) {
    clear_has_encoding();
    return _impl_.encoding_.name_.Release();
  } else {
    return nullptr;
  }
}
inline void TensorInfo::set_allocated_name(std::string* name) {
  if (has_encoding()) {
    clear_encoding();
  }
  if (name != nullptr) {
    set_has_name();
    _impl_.encoding_.name_.InitAllocated(name, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.name)
}

// .tensorflow.TensorInfo.CooSparse coo_sparse = 4;
inline bool TensorInfo::_internal_has_coo_sparse() const {
  return encoding_case() == kCooSparse;
}
inline bool TensorInfo::has_coo_sparse() const {
  return _internal_has_coo_sparse();
}
inline void TensorInfo::set_has_coo_sparse() {
  _impl_._oneof_case_[0] = kCooSparse;
}
inline void TensorInfo::clear_coo_sparse() {
  if (_internal_has_coo_sparse()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.encoding_.coo_sparse_;
    }
    clear_has_encoding();
  }
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::release_coo_sparse() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.coo_sparse)
  if (_internal_has_coo_sparse()) {
    clear_has_encoding();
    ::tensorflow::TensorInfo_CooSparse* temp = _impl_.encoding_.coo_sparse_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.encoding_.coo_sparse_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorInfo_CooSparse& TensorInfo::_internal_coo_sparse() const {
  return _internal_has_coo_sparse()
      ? *_impl_.encoding_.coo_sparse_
      : reinterpret_cast< ::tensorflow::TensorInfo_CooSparse&>(::tensorflow::_TensorInfo_CooSparse_default_instance_);
}
inline const ::tensorflow::TensorInfo_CooSparse& TensorInfo::coo_sparse() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.coo_sparse)
  return _internal_coo_sparse();
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::unsafe_arena_release_coo_sparse() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.coo_sparse)
  if (_internal_has_coo_sparse()) {
    clear_has_encoding();
    ::tensorflow::TensorInfo_CooSparse* temp = _impl_.encoding_.coo_sparse_;
    _impl_.encoding_.coo_sparse_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TensorInfo::unsafe_arena_set_allocated_coo_sparse(::tensorflow::TensorInfo_CooSparse* coo_sparse) {
  clear_encoding();
  if (coo_sparse) {
    set_has_coo_sparse();
    _impl_.encoding_.coo_sparse_ = coo_sparse;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.coo_sparse)
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::_internal_mutable_coo_sparse() {
  if (!_internal_has_coo_sparse()) {
    clear_encoding();
    set_has_coo_sparse();
    _impl_.encoding_.coo_sparse_ = CreateMaybeMessage< ::tensorflow::TensorInfo_CooSparse >(GetArenaForAllocation());
  }
  return _impl_.encoding_.coo_sparse_;
}
inline ::tensorflow::TensorInfo_CooSparse* TensorInfo::mutable_coo_sparse() {
  ::tensorflow::TensorInfo_CooSparse* _msg = _internal_mutable_coo_sparse();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.coo_sparse)
  return _msg;
}

// .tensorflow.TensorInfo.CompositeTensor composite_tensor = 5;
inline bool TensorInfo::_internal_has_composite_tensor() const {
  return encoding_case() == kCompositeTensor;
}
inline bool TensorInfo::has_composite_tensor() const {
  return _internal_has_composite_tensor();
}
inline void TensorInfo::set_has_composite_tensor() {
  _impl_._oneof_case_[0] = kCompositeTensor;
}
inline void TensorInfo::clear_composite_tensor() {
  if (_internal_has_composite_tensor()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.encoding_.composite_tensor_;
    }
    clear_has_encoding();
  }
}
inline ::tensorflow::TensorInfo_CompositeTensor* TensorInfo::release_composite_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.composite_tensor)
  if (_internal_has_composite_tensor()) {
    clear_has_encoding();
    ::tensorflow::TensorInfo_CompositeTensor* temp = _impl_.encoding_.composite_tensor_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.encoding_.composite_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorInfo_CompositeTensor& TensorInfo::_internal_composite_tensor() const {
  return _internal_has_composite_tensor()
      ? *_impl_.encoding_.composite_tensor_
      : reinterpret_cast< ::tensorflow::TensorInfo_CompositeTensor&>(::tensorflow::_TensorInfo_CompositeTensor_default_instance_);
}
inline const ::tensorflow::TensorInfo_CompositeTensor& TensorInfo::composite_tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.composite_tensor)
  return _internal_composite_tensor();
}
inline ::tensorflow::TensorInfo_CompositeTensor* TensorInfo::unsafe_arena_release_composite_tensor() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.TensorInfo.composite_tensor)
  if (_internal_has_composite_tensor()) {
    clear_has_encoding();
    ::tensorflow::TensorInfo_CompositeTensor* temp = _impl_.encoding_.composite_tensor_;
    _impl_.encoding_.composite_tensor_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TensorInfo::unsafe_arena_set_allocated_composite_tensor(::tensorflow::TensorInfo_CompositeTensor* composite_tensor) {
  clear_encoding();
  if (composite_tensor) {
    set_has_composite_tensor();
    _impl_.encoding_.composite_tensor_ = composite_tensor;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.composite_tensor)
}
inline ::tensorflow::TensorInfo_CompositeTensor* TensorInfo::_internal_mutable_composite_tensor() {
  if (!_internal_has_composite_tensor()) {
    clear_encoding();
    set_has_composite_tensor();
    _impl_.encoding_.composite_tensor_ = CreateMaybeMessage< ::tensorflow::TensorInfo_CompositeTensor >(GetArenaForAllocation());
  }
  return _impl_.encoding_.composite_tensor_;
}
inline ::tensorflow::TensorInfo_CompositeTensor* TensorInfo::mutable_composite_tensor() {
  ::tensorflow::TensorInfo_CompositeTensor* _msg = _internal_mutable_composite_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.composite_tensor)
  return _msg;
}

// .tensorflow.DataType dtype = 2;
inline void TensorInfo::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType TensorInfo::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType TensorInfo::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.dtype)
  return _internal_dtype();
}
inline void TensorInfo::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void TensorInfo::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorInfo.dtype)
}

// .tensorflow.TensorShapeProto tensor_shape = 3;
inline bool TensorInfo::_internal_has_tensor_shape() const {
  return this != internal_default_instance() && _impl_.tensor_shape_ != nullptr;
}
inline bool TensorInfo::has_tensor_shape() const {
  return _internal_has_tensor_shape();
}
inline const ::tensorflow::TensorShapeProto& TensorInfo::_internal_tensor_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.tensor_shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& TensorInfo::tensor_shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorInfo.tensor_shape)
  return _internal_tensor_shape();
}
inline void TensorInfo::unsafe_arena_set_allocated_tensor_shape(
    ::tensorflow::TensorShapeProto* tensor_shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  _impl_.tensor_shape_ = tensor_shape;
  if (tensor_shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorInfo.tensor_shape)
}
inline ::tensorflow::TensorShapeProto* TensorInfo::release_tensor_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorInfo::unsafe_arena_release_tensor_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorInfo.tensor_shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.tensor_shape_;
  _impl_.tensor_shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorInfo::_internal_mutable_tensor_shape() {
  
  if (_impl_.tensor_shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.tensor_shape_ = p;
  }
  return _impl_.tensor_shape_;
}
inline ::tensorflow::TensorShapeProto* TensorInfo::mutable_tensor_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_tensor_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorInfo.tensor_shape)
  return _msg;
}
inline void TensorInfo::set_allocated_tensor_shape(::tensorflow::TensorShapeProto* tensor_shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_shape_);
  }
  if (tensor_shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor_shape));
    if (message_arena != submessage_arena) {
      tensor_shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_shape_ = tensor_shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorInfo.tensor_shape)
}

inline bool TensorInfo::has_encoding() const {
  return encoding_case() != ENCODING_NOT_SET;
}
inline void TensorInfo::clear_has_encoding() {
  _impl_._oneof_case_[0] = ENCODING_NOT_SET;
}
inline TensorInfo::EncodingCase TensorInfo::encoding_case() const {
  return TensorInfo::EncodingCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// SignatureDef

// map<string, .tensorflow.TensorInfo> inputs = 1;
inline int SignatureDef::_internal_inputs_size() const {
  return _impl_.inputs_.size();
}
inline int SignatureDef::inputs_size() const {
  return _internal_inputs_size();
}
inline void SignatureDef::clear_inputs() {
  _impl_.inputs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
SignatureDef::_internal_inputs() const {
  return _impl_.inputs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
SignatureDef::inputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.SignatureDef.inputs)
  return _internal_inputs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
SignatureDef::_internal_mutable_inputs() {
  return _impl_.inputs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
SignatureDef::mutable_inputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SignatureDef.inputs)
  return _internal_mutable_inputs();
}

// map<string, .tensorflow.TensorInfo> outputs = 2;
inline int SignatureDef::_internal_outputs_size() const {
  return _impl_.outputs_.size();
}
inline int SignatureDef::outputs_size() const {
  return _internal_outputs_size();
}
inline void SignatureDef::clear_outputs() {
  _impl_.outputs_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
SignatureDef::_internal_outputs() const {
  return _impl_.outputs_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >&
SignatureDef::outputs() const {
  // @@protoc_insertion_point(field_map:tensorflow.SignatureDef.outputs)
  return _internal_outputs();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
SignatureDef::_internal_mutable_outputs() {
  return _impl_.outputs_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorInfo >*
SignatureDef::mutable_outputs() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SignatureDef.outputs)
  return _internal_mutable_outputs();
}

// string method_name = 3;
inline void SignatureDef::clear_method_name() {
  _impl_.method_name_.ClearToEmpty();
}
inline const std::string& SignatureDef::method_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SignatureDef.method_name)
  return _internal_method_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SignatureDef::set_method_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.method_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SignatureDef.method_name)
}
inline std::string* SignatureDef::mutable_method_name() {
  std::string* _s = _internal_mutable_method_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SignatureDef.method_name)
  return _s;
}
inline const std::string& SignatureDef::_internal_method_name() const {
  return _impl_.method_name_.Get();
}
inline void SignatureDef::_internal_set_method_name(const std::string& value) {
  
  _impl_.method_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SignatureDef::_internal_mutable_method_name() {
  
  return _impl_.method_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SignatureDef::release_method_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SignatureDef.method_name)
  return _impl_.method_name_.Release();
}
inline void SignatureDef::set_allocated_method_name(std::string* method_name) {
  if (method_name != nullptr) {
    
  } else {
    
  }
  _impl_.method_name_.SetAllocated(method_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.method_name_.IsDefault()) {
    _impl_.method_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SignatureDef.method_name)
}

// map<string, .tensorflow.TensorProto> defaults = 4;
inline int SignatureDef::_internal_defaults_size() const {
  return _impl_.defaults_.size();
}
inline int SignatureDef::defaults_size() const {
  return _internal_defaults_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
SignatureDef::_internal_defaults() const {
  return _impl_.defaults_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >&
SignatureDef::defaults() const {
  // @@protoc_insertion_point(field_map:tensorflow.SignatureDef.defaults)
  return _internal_defaults();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
SignatureDef::_internal_mutable_defaults() {
  return _impl_.defaults_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::TensorProto >*
SignatureDef::mutable_defaults() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.SignatureDef.defaults)
  return _internal_mutable_defaults();
}

// -------------------------------------------------------------------

// AssetFileDef

// .tensorflow.TensorInfo tensor_info = 1;
inline bool AssetFileDef::_internal_has_tensor_info() const {
  return this != internal_default_instance() && _impl_.tensor_info_ != nullptr;
}
inline bool AssetFileDef::has_tensor_info() const {
  return _internal_has_tensor_info();
}
inline void AssetFileDef::clear_tensor_info() {
  if (GetArenaForAllocation() == nullptr && _impl_.tensor_info_ != nullptr) {
    delete _impl_.tensor_info_;
  }
  _impl_.tensor_info_ = nullptr;
}
inline const ::tensorflow::TensorInfo& AssetFileDef::_internal_tensor_info() const {
  const ::tensorflow::TensorInfo* p = _impl_.tensor_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorInfo&>(
      ::tensorflow::_TensorInfo_default_instance_);
}
inline const ::tensorflow::TensorInfo& AssetFileDef::tensor_info() const {
  // @@protoc_insertion_point(field_get:tensorflow.AssetFileDef.tensor_info)
  return _internal_tensor_info();
}
inline void AssetFileDef::unsafe_arena_set_allocated_tensor_info(
    ::tensorflow::TensorInfo* tensor_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_info_);
  }
  _impl_.tensor_info_ = tensor_info;
  if (tensor_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.AssetFileDef.tensor_info)
}
inline ::tensorflow::TensorInfo* AssetFileDef::release_tensor_info() {
  
  ::tensorflow::TensorInfo* temp = _impl_.tensor_info_;
  _impl_.tensor_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorInfo* AssetFileDef::unsafe_arena_release_tensor_info() {
  // @@protoc_insertion_point(field_release:tensorflow.AssetFileDef.tensor_info)
  
  ::tensorflow::TensorInfo* temp = _impl_.tensor_info_;
  _impl_.tensor_info_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorInfo* AssetFileDef::_internal_mutable_tensor_info() {
  
  if (_impl_.tensor_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorInfo>(GetArenaForAllocation());
    _impl_.tensor_info_ = p;
  }
  return _impl_.tensor_info_;
}
inline ::tensorflow::TensorInfo* AssetFileDef::mutable_tensor_info() {
  ::tensorflow::TensorInfo* _msg = _internal_mutable_tensor_info();
  // @@protoc_insertion_point(field_mutable:tensorflow.AssetFileDef.tensor_info)
  return _msg;
}
inline void AssetFileDef::set_allocated_tensor_info(::tensorflow::TensorInfo* tensor_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.tensor_info_;
  }
  if (tensor_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(tensor_info);
    if (message_arena != submessage_arena) {
      tensor_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor_info, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_info_ = tensor_info;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AssetFileDef.tensor_info)
}

// string filename = 2;
inline void AssetFileDef::clear_filename() {
  _impl_.filename_.ClearToEmpty();
}
inline const std::string& AssetFileDef::filename() const {
  // @@protoc_insertion_point(field_get:tensorflow.AssetFileDef.filename)
  return _internal_filename();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AssetFileDef::set_filename(ArgT0&& arg0, ArgT... args) {
 
 _impl_.filename_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.AssetFileDef.filename)
}
inline std::string* AssetFileDef::mutable_filename() {
  std::string* _s = _internal_mutable_filename();
  // @@protoc_insertion_point(field_mutable:tensorflow.AssetFileDef.filename)
  return _s;
}
inline const std::string& AssetFileDef::_internal_filename() const {
  return _impl_.filename_.Get();
}
inline void AssetFileDef::_internal_set_filename(const std::string& value) {
  
  _impl_.filename_.Set(value, GetArenaForAllocation());
}
inline std::string* AssetFileDef::_internal_mutable_filename() {
  
  return _impl_.filename_.Mutable(GetArenaForAllocation());
}
inline std::string* AssetFileDef::release_filename() {
  // @@protoc_insertion_point(field_release:tensorflow.AssetFileDef.filename)
  return _impl_.filename_.Release();
}
inline void AssetFileDef::set_allocated_filename(std::string* filename) {
  if (filename != nullptr) {
    
  } else {
    
  }
  _impl_.filename_.SetAllocated(filename, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.filename_.IsDefault()) {
    _impl_.filename_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.AssetFileDef.filename)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fmeta_5fgraph_2eproto
