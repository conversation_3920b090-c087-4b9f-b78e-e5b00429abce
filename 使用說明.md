# MySkin.ai 皮膚分析工具 - 命令行版本

## 📋 功能介紹

這是一個基於 AI 的皮膚分析工具，可以分析面部圖像並提供詳細的皮膚健康報告。

### 🎯 主要功能
- **皺紋檢測** - 分析皮膚皺紋程度
- **斑點檢測** - 識別皮膚斑點密度  
- **紋理分析** - 評估皮膚紋理複雜度
- **年齡預測** - 使用深度學習預測皮膚年齡
- **面部關鍵點標記** - 繪製面部特徵點
- **自動報告生成** - 生成詳細的分析報告

## 🚀 使用方法

### 方法一：拖拽操作（推薦）
1. 將圖片文件直接拖拽到 `run_skin_analysis.bat` 文件上
2. 系統會自動開始分析
3. 分析完成後，結果會保存在 `output` 資料夾中

### 方法二：雙擊運行
1. 雙擊 `run_skin_analysis.bat` 文件
2. 在提示下輸入圖片路徑
3. 按 Enter 開始分析

### 方法三：命令行運行
```bash
# 激活虛擬環境（如果使用）
venv\Scripts\activate

# 運行分析工具
python skin_analyzer_cli.py "圖片路徑"
```

## 📁 輸出文件

分析完成後，會在 `output` 資料夾中生成以下文件：

### 1. 分析圖像
- 文件名：`原圖名_時間戳_analyzed.jpg`
- 內容：標記了面部關鍵點的圖像

### 2. 詳細報告
- 文件名：`原圖名_時間戳_report.txt`
- 內容：人類可讀的分析報告

### 3. JSON 數據
- 文件名：`原圖名_時間戳_report.json`
- 內容：結構化的分析數據

## 📊 分析指標說明

### 評分系統（0-100分）
- **皺紋評分**：數值越高表示皺紋越明顯
- **斑點評分**：數值越高表示斑點越多
- **紋理評分**：數值越高表示皮膚紋理越複雜

### 年齡預測
- 使用深度學習模型預測皮膚年齡
- 結果格式：「約 X 歲」

## 🔧 系統要求

### 必要軟件
- Python 3.7 或更高版本
- Windows 操作系統

### 必要文件
- `more_data(3).h5` - 深度學習模型文件
- `requirements.txt` - Python 依賴列表
- `skin_analyzer_cli.py` - 主程序
- `run_skin_analysis.bat` - 批處理文件

### Python 依賴套件
```
mediapipe==0.10.7
scikit-image==0.22.0
opencv-python==********
tensorflow==2.13.0
pillow
numpy
```

## 🖼️ 支持的圖片格式

- `.jpg` / `.jpeg`
- `.png`
- `.bmp`
- `.tiff`

## 📝 使用範例

### 輸入
```
圖片：selfie.jpg
```

### 輸出
```
✅ 分析完成!
==================================================
🎯 預測年齡: 約 28 歲
📊 皺紋評分: 15.23/100
📊 斑點評分: 8.45/100
📊 紋理評分: 32.67/100

📁 輸出文件:
• 分析圖像: output\selfie_20241226_143022_analyzed.jpg
• 詳細報告: output\selfie_20241226_143022_report.txt
• JSON數據: output\selfie_20241226_143022_report.json
```

## ⚠️ 注意事項

1. **圖片質量**：建議使用清晰、光線充足的正面照片
2. **面部可見**：確保面部完整且清晰可見
3. **文件路徑**：避免使用包含特殊字符的路徑
4. **模型文件**：確保 `more_data(3).h5` 文件存在且完整
5. **網絡連接**：首次運行可能需要下載 MediaPipe 模型

## 🔍 故障排除

### 常見問題

**Q: 提示「未找到 Python」**
A: 請安裝 Python 3.7+ 並確保添加到系統 PATH

**Q: 提示「缺少必要的依賴套件」**
A: 運行 `pip install -r requirements.txt` 安裝依賴

**Q: 提示「未找到模型文件」**
A: 確保 `more_data(3).h5` 文件在程序目錄中

**Q: 分析失敗**
A: 檢查圖片格式是否支持，圖片是否損壞

**Q: 年齡預測不準確**
A: 這是正常現象，AI 預測存在誤差範圍

## 📞 技術支持

如遇到問題，請檢查：
1. Python 版本是否符合要求
2. 所有依賴是否正確安裝
3. 模型文件是否完整
4. 圖片文件是否有效

## 🎉 享受使用！

感謝使用 MySkin.ai 皮膚分析工具！希望這個工具能幫助您更好地了解皮膚健康狀況。
