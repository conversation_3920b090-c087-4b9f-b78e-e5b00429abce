/* Copyright 2017 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

syntax = "proto3";

package tensorflow;

option cc_enable_arenas = true;
option java_outer_classname = "DevicePropertiesProtos";
option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto";

message DeviceProperties {
  // Device type (CPU, GPU, ...)
  string type = 1;
  // Vendor (Intel, nvidia, ...)
  string vendor = 2;
  // Model (Haswell, K40, ...)
  string model = 3;
  // Core Frequency in Mhz
  int64 frequency = 4;
  // Number of cores
  int64 num_cores = 5;
  // Version of the tools and libraries used with this device (e.g. gcc 4.9,
  // cudnn 5.1)
  map<string, string> environment = 6;
  // Number of registers per core.
  int64 num_registers = 7;
  // L1 cache size in bytes
  int64 l1_cache_size = 8;
  // L2 cache size in bytes
  int64 l2_cache_size = 9;
  // L3 cache size in bytes
  int64 l3_cache_size = 10;
  // Shared memory size per multiprocessor in bytes. This field is
  // applicable to GPUs only.
  int64 shared_memory_size_per_multiprocessor = 11;
  // Memory size in bytes
  int64 memory_size = 12;
  // Memory bandwidth in KB/s
  int64 bandwidth = 13;
}

message NamedDevice {
  string name = 1;
  DeviceProperties properties = 2;
}
