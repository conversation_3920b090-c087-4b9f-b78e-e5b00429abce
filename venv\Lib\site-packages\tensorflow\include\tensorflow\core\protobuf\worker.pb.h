// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/worker.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include "tensorflow/core/framework/cost_graph.pb.h"
#include "tensorflow/core/framework/device_attributes.pb.h"
#include "tensorflow/core/framework/graph.pb.h"
#include "tensorflow/core/framework/step_stats.pb.h"
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
#include "tensorflow/core/protobuf/config.pb.h"
#include "tensorflow/core/protobuf/debug.pb.h"
#include "tensorflow/core/protobuf/error_codes.pb.h"
#include "tensorflow/core/protobuf/named_tensor.pb.h"
#include "tensorflow/core/protobuf/tensorflow_server.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
namespace tensorflow {
class CleanupAllRequest;
struct CleanupAllRequestDefaultTypeInternal;
extern CleanupAllRequestDefaultTypeInternal _CleanupAllRequest_default_instance_;
class CleanupAllResponse;
struct CleanupAllResponseDefaultTypeInternal;
extern CleanupAllResponseDefaultTypeInternal _CleanupAllResponse_default_instance_;
class CleanupGraphRequest;
struct CleanupGraphRequestDefaultTypeInternal;
extern CleanupGraphRequestDefaultTypeInternal _CleanupGraphRequest_default_instance_;
class CleanupGraphResponse;
struct CleanupGraphResponseDefaultTypeInternal;
extern CleanupGraphResponseDefaultTypeInternal _CleanupGraphResponse_default_instance_;
class CompleteGroupRequest;
struct CompleteGroupRequestDefaultTypeInternal;
extern CompleteGroupRequestDefaultTypeInternal _CompleteGroupRequest_default_instance_;
class CompleteGroupResponse;
struct CompleteGroupResponseDefaultTypeInternal;
extern CompleteGroupResponseDefaultTypeInternal _CompleteGroupResponse_default_instance_;
class CompleteInstanceRequest;
struct CompleteInstanceRequestDefaultTypeInternal;
extern CompleteInstanceRequestDefaultTypeInternal _CompleteInstanceRequest_default_instance_;
class CompleteInstanceResponse;
struct CompleteInstanceResponseDefaultTypeInternal;
extern CompleteInstanceResponseDefaultTypeInternal _CompleteInstanceResponse_default_instance_;
class CreateWorkerSessionRequest;
struct CreateWorkerSessionRequestDefaultTypeInternal;
extern CreateWorkerSessionRequestDefaultTypeInternal _CreateWorkerSessionRequest_default_instance_;
class CreateWorkerSessionResponse;
struct CreateWorkerSessionResponseDefaultTypeInternal;
extern CreateWorkerSessionResponseDefaultTypeInternal _CreateWorkerSessionResponse_default_instance_;
class DeleteWorkerSessionRequest;
struct DeleteWorkerSessionRequestDefaultTypeInternal;
extern DeleteWorkerSessionRequestDefaultTypeInternal _DeleteWorkerSessionRequest_default_instance_;
class DeleteWorkerSessionResponse;
struct DeleteWorkerSessionResponseDefaultTypeInternal;
extern DeleteWorkerSessionResponseDefaultTypeInternal _DeleteWorkerSessionResponse_default_instance_;
class DeregisterGraphRequest;
struct DeregisterGraphRequestDefaultTypeInternal;
extern DeregisterGraphRequestDefaultTypeInternal _DeregisterGraphRequest_default_instance_;
class DeregisterGraphResponse;
struct DeregisterGraphResponseDefaultTypeInternal;
extern DeregisterGraphResponseDefaultTypeInternal _DeregisterGraphResponse_default_instance_;
class ExecutorOpts;
struct ExecutorOptsDefaultTypeInternal;
extern ExecutorOptsDefaultTypeInternal _ExecutorOpts_default_instance_;
class GetStatusRequest;
struct GetStatusRequestDefaultTypeInternal;
extern GetStatusRequestDefaultTypeInternal _GetStatusRequest_default_instance_;
class GetStatusResponse;
struct GetStatusResponseDefaultTypeInternal;
extern GetStatusResponseDefaultTypeInternal _GetStatusResponse_default_instance_;
class GetStepSequenceRequest;
struct GetStepSequenceRequestDefaultTypeInternal;
extern GetStepSequenceRequestDefaultTypeInternal _GetStepSequenceRequest_default_instance_;
class GetStepSequenceResponse;
struct GetStepSequenceResponseDefaultTypeInternal;
extern GetStepSequenceResponseDefaultTypeInternal _GetStepSequenceResponse_default_instance_;
class LabeledStepStats;
struct LabeledStepStatsDefaultTypeInternal;
extern LabeledStepStatsDefaultTypeInternal _LabeledStepStats_default_instance_;
class LoggingRequest;
struct LoggingRequestDefaultTypeInternal;
extern LoggingRequestDefaultTypeInternal _LoggingRequest_default_instance_;
class LoggingResponse;
struct LoggingResponseDefaultTypeInternal;
extern LoggingResponseDefaultTypeInternal _LoggingResponse_default_instance_;
class MarkRecvFinishedRequest;
struct MarkRecvFinishedRequestDefaultTypeInternal;
extern MarkRecvFinishedRequestDefaultTypeInternal _MarkRecvFinishedRequest_default_instance_;
class MarkRecvFinishedResponse;
struct MarkRecvFinishedResponseDefaultTypeInternal;
extern MarkRecvFinishedResponseDefaultTypeInternal _MarkRecvFinishedResponse_default_instance_;
class RecvBufRequest;
struct RecvBufRequestDefaultTypeInternal;
extern RecvBufRequestDefaultTypeInternal _RecvBufRequest_default_instance_;
class RecvBufResponse;
struct RecvBufResponseDefaultTypeInternal;
extern RecvBufResponseDefaultTypeInternal _RecvBufResponse_default_instance_;
class RecvTensorRequest;
struct RecvTensorRequestDefaultTypeInternal;
extern RecvTensorRequestDefaultTypeInternal _RecvTensorRequest_default_instance_;
class RecvTensorResponse;
struct RecvTensorResponseDefaultTypeInternal;
extern RecvTensorResponseDefaultTypeInternal _RecvTensorResponse_default_instance_;
class RegisterGraphRequest;
struct RegisterGraphRequestDefaultTypeInternal;
extern RegisterGraphRequestDefaultTypeInternal _RegisterGraphRequest_default_instance_;
class RegisterGraphResponse;
struct RegisterGraphResponseDefaultTypeInternal;
extern RegisterGraphResponseDefaultTypeInternal _RegisterGraphResponse_default_instance_;
class RunGraphRequest;
struct RunGraphRequestDefaultTypeInternal;
extern RunGraphRequestDefaultTypeInternal _RunGraphRequest_default_instance_;
class RunGraphResponse;
struct RunGraphResponseDefaultTypeInternal;
extern RunGraphResponseDefaultTypeInternal _RunGraphResponse_default_instance_;
class StepSequence;
struct StepSequenceDefaultTypeInternal;
extern StepSequenceDefaultTypeInternal _StepSequence_default_instance_;
class TraceOpts;
struct TraceOptsDefaultTypeInternal;
extern TraceOptsDefaultTypeInternal _TraceOpts_default_instance_;
class TracingRequest;
struct TracingRequestDefaultTypeInternal;
extern TracingRequestDefaultTypeInternal _TracingRequest_default_instance_;
class TracingResponse;
struct TracingResponseDefaultTypeInternal;
extern TracingResponseDefaultTypeInternal _TracingResponse_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::CleanupAllRequest* Arena::CreateMaybeMessage<::tensorflow::CleanupAllRequest>(Arena*);
template<> ::tensorflow::CleanupAllResponse* Arena::CreateMaybeMessage<::tensorflow::CleanupAllResponse>(Arena*);
template<> ::tensorflow::CleanupGraphRequest* Arena::CreateMaybeMessage<::tensorflow::CleanupGraphRequest>(Arena*);
template<> ::tensorflow::CleanupGraphResponse* Arena::CreateMaybeMessage<::tensorflow::CleanupGraphResponse>(Arena*);
template<> ::tensorflow::CompleteGroupRequest* Arena::CreateMaybeMessage<::tensorflow::CompleteGroupRequest>(Arena*);
template<> ::tensorflow::CompleteGroupResponse* Arena::CreateMaybeMessage<::tensorflow::CompleteGroupResponse>(Arena*);
template<> ::tensorflow::CompleteInstanceRequest* Arena::CreateMaybeMessage<::tensorflow::CompleteInstanceRequest>(Arena*);
template<> ::tensorflow::CompleteInstanceResponse* Arena::CreateMaybeMessage<::tensorflow::CompleteInstanceResponse>(Arena*);
template<> ::tensorflow::CreateWorkerSessionRequest* Arena::CreateMaybeMessage<::tensorflow::CreateWorkerSessionRequest>(Arena*);
template<> ::tensorflow::CreateWorkerSessionResponse* Arena::CreateMaybeMessage<::tensorflow::CreateWorkerSessionResponse>(Arena*);
template<> ::tensorflow::DeleteWorkerSessionRequest* Arena::CreateMaybeMessage<::tensorflow::DeleteWorkerSessionRequest>(Arena*);
template<> ::tensorflow::DeleteWorkerSessionResponse* Arena::CreateMaybeMessage<::tensorflow::DeleteWorkerSessionResponse>(Arena*);
template<> ::tensorflow::DeregisterGraphRequest* Arena::CreateMaybeMessage<::tensorflow::DeregisterGraphRequest>(Arena*);
template<> ::tensorflow::DeregisterGraphResponse* Arena::CreateMaybeMessage<::tensorflow::DeregisterGraphResponse>(Arena*);
template<> ::tensorflow::ExecutorOpts* Arena::CreateMaybeMessage<::tensorflow::ExecutorOpts>(Arena*);
template<> ::tensorflow::GetStatusRequest* Arena::CreateMaybeMessage<::tensorflow::GetStatusRequest>(Arena*);
template<> ::tensorflow::GetStatusResponse* Arena::CreateMaybeMessage<::tensorflow::GetStatusResponse>(Arena*);
template<> ::tensorflow::GetStepSequenceRequest* Arena::CreateMaybeMessage<::tensorflow::GetStepSequenceRequest>(Arena*);
template<> ::tensorflow::GetStepSequenceResponse* Arena::CreateMaybeMessage<::tensorflow::GetStepSequenceResponse>(Arena*);
template<> ::tensorflow::LabeledStepStats* Arena::CreateMaybeMessage<::tensorflow::LabeledStepStats>(Arena*);
template<> ::tensorflow::LoggingRequest* Arena::CreateMaybeMessage<::tensorflow::LoggingRequest>(Arena*);
template<> ::tensorflow::LoggingResponse* Arena::CreateMaybeMessage<::tensorflow::LoggingResponse>(Arena*);
template<> ::tensorflow::MarkRecvFinishedRequest* Arena::CreateMaybeMessage<::tensorflow::MarkRecvFinishedRequest>(Arena*);
template<> ::tensorflow::MarkRecvFinishedResponse* Arena::CreateMaybeMessage<::tensorflow::MarkRecvFinishedResponse>(Arena*);
template<> ::tensorflow::RecvBufRequest* Arena::CreateMaybeMessage<::tensorflow::RecvBufRequest>(Arena*);
template<> ::tensorflow::RecvBufResponse* Arena::CreateMaybeMessage<::tensorflow::RecvBufResponse>(Arena*);
template<> ::tensorflow::RecvTensorRequest* Arena::CreateMaybeMessage<::tensorflow::RecvTensorRequest>(Arena*);
template<> ::tensorflow::RecvTensorResponse* Arena::CreateMaybeMessage<::tensorflow::RecvTensorResponse>(Arena*);
template<> ::tensorflow::RegisterGraphRequest* Arena::CreateMaybeMessage<::tensorflow::RegisterGraphRequest>(Arena*);
template<> ::tensorflow::RegisterGraphResponse* Arena::CreateMaybeMessage<::tensorflow::RegisterGraphResponse>(Arena*);
template<> ::tensorflow::RunGraphRequest* Arena::CreateMaybeMessage<::tensorflow::RunGraphRequest>(Arena*);
template<> ::tensorflow::RunGraphResponse* Arena::CreateMaybeMessage<::tensorflow::RunGraphResponse>(Arena*);
template<> ::tensorflow::StepSequence* Arena::CreateMaybeMessage<::tensorflow::StepSequence>(Arena*);
template<> ::tensorflow::TraceOpts* Arena::CreateMaybeMessage<::tensorflow::TraceOpts>(Arena*);
template<> ::tensorflow::TracingRequest* Arena::CreateMaybeMessage<::tensorflow::TracingRequest>(Arena*);
template<> ::tensorflow::TracingResponse* Arena::CreateMaybeMessage<::tensorflow::TracingResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class GetStatusRequest final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.GetStatusRequest) */ {
 public:
  inline GetStatusRequest() : GetStatusRequest(nullptr) {}
  explicit PROTOBUF_CONSTEXPR GetStatusRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetStatusRequest(const GetStatusRequest& from);
  GetStatusRequest(GetStatusRequest&& from) noexcept
    : GetStatusRequest() {
    *this = ::std::move(from);
  }

  inline GetStatusRequest& operator=(const GetStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStatusRequest& operator=(GetStatusRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetStatusRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetStatusRequest* internal_default_instance() {
    return reinterpret_cast<const GetStatusRequest*>(
               &_GetStatusRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetStatusRequest& a, GetStatusRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStatusRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStatusRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetStatusRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetStatusRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GetStatusRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GetStatusRequest& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStatusRequest";
  }
  protected:
  explicit GetStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.GetStatusRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class GetStatusResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStatusResponse) */ {
 public:
  inline GetStatusResponse() : GetStatusResponse(nullptr) {}
  ~GetStatusResponse() override;
  explicit PROTOBUF_CONSTEXPR GetStatusResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetStatusResponse(const GetStatusResponse& from);
  GetStatusResponse(GetStatusResponse&& from) noexcept
    : GetStatusResponse() {
    *this = ::std::move(from);
  }

  inline GetStatusResponse& operator=(const GetStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStatusResponse& operator=(GetStatusResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetStatusResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetStatusResponse* internal_default_instance() {
    return reinterpret_cast<const GetStatusResponse*>(
               &_GetStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetStatusResponse& a, GetStatusResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStatusResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStatusResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetStatusResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetStatusResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetStatusResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetStatusResponse& from) {
    GetStatusResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStatusResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStatusResponse";
  }
  protected:
  explicit GetStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 1,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 1;
  int device_attributes_size() const;
  private:
  int _internal_device_attributes_size() const;
  public:
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetStatusResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CreateWorkerSessionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CreateWorkerSessionRequest) */ {
 public:
  inline CreateWorkerSessionRequest() : CreateWorkerSessionRequest(nullptr) {}
  ~CreateWorkerSessionRequest() override;
  explicit PROTOBUF_CONSTEXPR CreateWorkerSessionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateWorkerSessionRequest(const CreateWorkerSessionRequest& from);
  CreateWorkerSessionRequest(CreateWorkerSessionRequest&& from) noexcept
    : CreateWorkerSessionRequest() {
    *this = ::std::move(from);
  }

  inline CreateWorkerSessionRequest& operator=(const CreateWorkerSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateWorkerSessionRequest& operator=(CreateWorkerSessionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateWorkerSessionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateWorkerSessionRequest* internal_default_instance() {
    return reinterpret_cast<const CreateWorkerSessionRequest*>(
               &_CreateWorkerSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CreateWorkerSessionRequest& a, CreateWorkerSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateWorkerSessionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateWorkerSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateWorkerSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateWorkerSessionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CreateWorkerSessionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CreateWorkerSessionRequest& from) {
    CreateWorkerSessionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CreateWorkerSessionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateWorkerSessionRequest";
  }
  protected:
  explicit CreateWorkerSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClusterDeviceAttributesFieldNumber = 4,
    kSessionHandleFieldNumber = 1,
    kMasterTaskFieldNumber = 5,
    kServerDefFieldNumber = 2,
    kMasterIncarnationFieldNumber = 6,
    kIsolateSessionStateFieldNumber = 3,
  };
  // repeated .tensorflow.DeviceAttributes cluster_device_attributes = 4;
  int cluster_device_attributes_size() const;
  private:
  int _internal_cluster_device_attributes_size() const;
  public:
  void clear_cluster_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_cluster_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_cluster_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_cluster_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& cluster_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_cluster_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      cluster_device_attributes() const;

  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // string master_task = 5;
  void clear_master_task();
  const std::string& master_task() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_master_task(ArgT0&& arg0, ArgT... args);
  std::string* mutable_master_task();
  PROTOBUF_NODISCARD std::string* release_master_task();
  void set_allocated_master_task(std::string* master_task);
  private:
  const std::string& _internal_master_task() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_master_task(const std::string& value);
  std::string* _internal_mutable_master_task();
  public:

  // .tensorflow.ServerDef server_def = 2;
  bool has_server_def() const;
  private:
  bool _internal_has_server_def() const;
  public:
  void clear_server_def();
  const ::tensorflow::ServerDef& server_def() const;
  PROTOBUF_NODISCARD ::tensorflow::ServerDef* release_server_def();
  ::tensorflow::ServerDef* mutable_server_def();
  void set_allocated_server_def(::tensorflow::ServerDef* server_def);
  private:
  const ::tensorflow::ServerDef& _internal_server_def() const;
  ::tensorflow::ServerDef* _internal_mutable_server_def();
  public:
  void unsafe_arena_set_allocated_server_def(
      ::tensorflow::ServerDef* server_def);
  ::tensorflow::ServerDef* unsafe_arena_release_server_def();

  // int64 master_incarnation = 6;
  void clear_master_incarnation();
  int64_t master_incarnation() const;
  void set_master_incarnation(int64_t value);
  private:
  int64_t _internal_master_incarnation() const;
  void _internal_set_master_incarnation(int64_t value);
  public:

  // bool isolate_session_state = 3;
  void clear_isolate_session_state();
  bool isolate_session_state() const;
  void set_isolate_session_state(bool value);
  private:
  bool _internal_isolate_session_state() const;
  void _internal_set_isolate_session_state(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CreateWorkerSessionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > cluster_device_attributes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr master_task_;
    ::tensorflow::ServerDef* server_def_;
    int64_t master_incarnation_;
    bool isolate_session_state_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CreateWorkerSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.CreateWorkerSessionResponse) */ {
 public:
  inline CreateWorkerSessionResponse() : CreateWorkerSessionResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR CreateWorkerSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CreateWorkerSessionResponse(const CreateWorkerSessionResponse& from);
  CreateWorkerSessionResponse(CreateWorkerSessionResponse&& from) noexcept
    : CreateWorkerSessionResponse() {
    *this = ::std::move(from);
  }

  inline CreateWorkerSessionResponse& operator=(const CreateWorkerSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CreateWorkerSessionResponse& operator=(CreateWorkerSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CreateWorkerSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CreateWorkerSessionResponse* internal_default_instance() {
    return reinterpret_cast<const CreateWorkerSessionResponse*>(
               &_CreateWorkerSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CreateWorkerSessionResponse& a, CreateWorkerSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CreateWorkerSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CreateWorkerSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CreateWorkerSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CreateWorkerSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const CreateWorkerSessionResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const CreateWorkerSessionResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CreateWorkerSessionResponse";
  }
  protected:
  explicit CreateWorkerSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CreateWorkerSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeleteWorkerSessionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeleteWorkerSessionRequest) */ {
 public:
  inline DeleteWorkerSessionRequest() : DeleteWorkerSessionRequest(nullptr) {}
  ~DeleteWorkerSessionRequest() override;
  explicit PROTOBUF_CONSTEXPR DeleteWorkerSessionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteWorkerSessionRequest(const DeleteWorkerSessionRequest& from);
  DeleteWorkerSessionRequest(DeleteWorkerSessionRequest&& from) noexcept
    : DeleteWorkerSessionRequest() {
    *this = ::std::move(from);
  }

  inline DeleteWorkerSessionRequest& operator=(const DeleteWorkerSessionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteWorkerSessionRequest& operator=(DeleteWorkerSessionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteWorkerSessionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteWorkerSessionRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteWorkerSessionRequest*>(
               &_DeleteWorkerSessionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(DeleteWorkerSessionRequest& a, DeleteWorkerSessionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteWorkerSessionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteWorkerSessionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteWorkerSessionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteWorkerSessionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteWorkerSessionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeleteWorkerSessionRequest& from) {
    DeleteWorkerSessionRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteWorkerSessionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeleteWorkerSessionRequest";
  }
  protected:
  explicit DeleteWorkerSessionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteWorkerSessionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeleteWorkerSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.DeleteWorkerSessionResponse) */ {
 public:
  inline DeleteWorkerSessionResponse() : DeleteWorkerSessionResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR DeleteWorkerSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteWorkerSessionResponse(const DeleteWorkerSessionResponse& from);
  DeleteWorkerSessionResponse(DeleteWorkerSessionResponse&& from) noexcept
    : DeleteWorkerSessionResponse() {
    *this = ::std::move(from);
  }

  inline DeleteWorkerSessionResponse& operator=(const DeleteWorkerSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteWorkerSessionResponse& operator=(DeleteWorkerSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteWorkerSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteWorkerSessionResponse* internal_default_instance() {
    return reinterpret_cast<const DeleteWorkerSessionResponse*>(
               &_DeleteWorkerSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(DeleteWorkerSessionResponse& a, DeleteWorkerSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteWorkerSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteWorkerSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteWorkerSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteWorkerSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const DeleteWorkerSessionResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const DeleteWorkerSessionResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeleteWorkerSessionResponse";
  }
  protected:
  explicit DeleteWorkerSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeleteWorkerSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RegisterGraphRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterGraphRequest) */ {
 public:
  inline RegisterGraphRequest() : RegisterGraphRequest(nullptr) {}
  ~RegisterGraphRequest() override;
  explicit PROTOBUF_CONSTEXPR RegisterGraphRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterGraphRequest(const RegisterGraphRequest& from);
  RegisterGraphRequest(RegisterGraphRequest&& from) noexcept
    : RegisterGraphRequest() {
    *this = ::std::move(from);
  }

  inline RegisterGraphRequest& operator=(const RegisterGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterGraphRequest& operator=(RegisterGraphRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterGraphRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterGraphRequest* internal_default_instance() {
    return reinterpret_cast<const RegisterGraphRequest*>(
               &_RegisterGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RegisterGraphRequest& a, RegisterGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterGraphRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterGraphRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisterGraphRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisterGraphRequest& from) {
    RegisterGraphRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterGraphRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisterGraphRequest";
  }
  protected:
  explicit RegisterGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionHandleFieldNumber = 1,
    kGraphDefFieldNumber = 2,
    kGraphOptionsFieldNumber = 4,
    kDebugOptionsFieldNumber = 5,
    kConfigProtoFieldNumber = 8,
    kCollectiveGraphKeyFieldNumber = 7,
    kCreateWorkerSessionCalledFieldNumber = 6,
    kHasControlFlowFieldNumber = 3,
  };
  // string session_handle = 1;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // .tensorflow.GraphDef graph_def = 2;
  bool has_graph_def() const;
  private:
  bool _internal_has_graph_def() const;
  public:
  void clear_graph_def();
  const ::tensorflow::GraphDef& graph_def() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphDef* release_graph_def();
  ::tensorflow::GraphDef* mutable_graph_def();
  void set_allocated_graph_def(::tensorflow::GraphDef* graph_def);
  private:
  const ::tensorflow::GraphDef& _internal_graph_def() const;
  ::tensorflow::GraphDef* _internal_mutable_graph_def();
  public:
  void unsafe_arena_set_allocated_graph_def(
      ::tensorflow::GraphDef* graph_def);
  ::tensorflow::GraphDef* unsafe_arena_release_graph_def();

  // .tensorflow.GraphOptions graph_options = 4;
  bool has_graph_options() const;
  private:
  bool _internal_has_graph_options() const;
  public:
  void clear_graph_options();
  const ::tensorflow::GraphOptions& graph_options() const;
  PROTOBUF_NODISCARD ::tensorflow::GraphOptions* release_graph_options();
  ::tensorflow::GraphOptions* mutable_graph_options();
  void set_allocated_graph_options(::tensorflow::GraphOptions* graph_options);
  private:
  const ::tensorflow::GraphOptions& _internal_graph_options() const;
  ::tensorflow::GraphOptions* _internal_mutable_graph_options();
  public:
  void unsafe_arena_set_allocated_graph_options(
      ::tensorflow::GraphOptions* graph_options);
  ::tensorflow::GraphOptions* unsafe_arena_release_graph_options();

  // .tensorflow.DebugOptions debug_options = 5;
  bool has_debug_options() const;
  private:
  bool _internal_has_debug_options() const;
  public:
  void clear_debug_options();
  const ::tensorflow::DebugOptions& debug_options() const;
  PROTOBUF_NODISCARD ::tensorflow::DebugOptions* release_debug_options();
  ::tensorflow::DebugOptions* mutable_debug_options();
  void set_allocated_debug_options(::tensorflow::DebugOptions* debug_options);
  private:
  const ::tensorflow::DebugOptions& _internal_debug_options() const;
  ::tensorflow::DebugOptions* _internal_mutable_debug_options();
  public:
  void unsafe_arena_set_allocated_debug_options(
      ::tensorflow::DebugOptions* debug_options);
  ::tensorflow::DebugOptions* unsafe_arena_release_debug_options();

  // .tensorflow.ConfigProto config_proto = 8;
  bool has_config_proto() const;
  private:
  bool _internal_has_config_proto() const;
  public:
  void clear_config_proto();
  const ::tensorflow::ConfigProto& config_proto() const;
  PROTOBUF_NODISCARD ::tensorflow::ConfigProto* release_config_proto();
  ::tensorflow::ConfigProto* mutable_config_proto();
  void set_allocated_config_proto(::tensorflow::ConfigProto* config_proto);
  private:
  const ::tensorflow::ConfigProto& _internal_config_proto() const;
  ::tensorflow::ConfigProto* _internal_mutable_config_proto();
  public:
  void unsafe_arena_set_allocated_config_proto(
      ::tensorflow::ConfigProto* config_proto);
  ::tensorflow::ConfigProto* unsafe_arena_release_config_proto();

  // int64 collective_graph_key = 7;
  void clear_collective_graph_key();
  int64_t collective_graph_key() const;
  void set_collective_graph_key(int64_t value);
  private:
  int64_t _internal_collective_graph_key() const;
  void _internal_set_collective_graph_key(int64_t value);
  public:

  // bool create_worker_session_called = 6;
  void clear_create_worker_session_called();
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);
  private:
  bool _internal_create_worker_session_called() const;
  void _internal_set_create_worker_session_called(bool value);
  public:

  // bool has_control_flow = 3 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_has_control_flow();
  PROTOBUF_DEPRECATED bool has_control_flow() const;
  PROTOBUF_DEPRECATED void set_has_control_flow(bool value);
  private:
  bool _internal_has_control_flow() const;
  void _internal_set_has_control_flow(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterGraphRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::tensorflow::GraphDef* graph_def_;
    ::tensorflow::GraphOptions* graph_options_;
    ::tensorflow::DebugOptions* debug_options_;
    ::tensorflow::ConfigProto* config_proto_;
    int64_t collective_graph_key_;
    bool create_worker_session_called_;
    bool has_control_flow_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RegisterGraphResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RegisterGraphResponse) */ {
 public:
  inline RegisterGraphResponse() : RegisterGraphResponse(nullptr) {}
  ~RegisterGraphResponse() override;
  explicit PROTOBUF_CONSTEXPR RegisterGraphResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegisterGraphResponse(const RegisterGraphResponse& from);
  RegisterGraphResponse(RegisterGraphResponse&& from) noexcept
    : RegisterGraphResponse() {
    *this = ::std::move(from);
  }

  inline RegisterGraphResponse& operator=(const RegisterGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegisterGraphResponse& operator=(RegisterGraphResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegisterGraphResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegisterGraphResponse* internal_default_instance() {
    return reinterpret_cast<const RegisterGraphResponse*>(
               &_RegisterGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(RegisterGraphResponse& a, RegisterGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RegisterGraphResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegisterGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegisterGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegisterGraphResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegisterGraphResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RegisterGraphResponse& from) {
    RegisterGraphResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegisterGraphResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RegisterGraphResponse";
  }
  protected:
  explicit RegisterGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphHandleFieldNumber = 1,
  };
  // string graph_handle = 1;
  void clear_graph_handle();
  const std::string& graph_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_handle();
  PROTOBUF_NODISCARD std::string* release_graph_handle();
  void set_allocated_graph_handle(std::string* graph_handle);
  private:
  const std::string& _internal_graph_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_handle(const std::string& value);
  std::string* _internal_mutable_graph_handle();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RegisterGraphResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_handle_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeregisterGraphRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeregisterGraphRequest) */ {
 public:
  inline DeregisterGraphRequest() : DeregisterGraphRequest(nullptr) {}
  ~DeregisterGraphRequest() override;
  explicit PROTOBUF_CONSTEXPR DeregisterGraphRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeregisterGraphRequest(const DeregisterGraphRequest& from);
  DeregisterGraphRequest(DeregisterGraphRequest&& from) noexcept
    : DeregisterGraphRequest() {
    *this = ::std::move(from);
  }

  inline DeregisterGraphRequest& operator=(const DeregisterGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeregisterGraphRequest& operator=(DeregisterGraphRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeregisterGraphRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeregisterGraphRequest* internal_default_instance() {
    return reinterpret_cast<const DeregisterGraphRequest*>(
               &_DeregisterGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DeregisterGraphRequest& a, DeregisterGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeregisterGraphRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeregisterGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeregisterGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeregisterGraphRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeregisterGraphRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeregisterGraphRequest& from) {
    DeregisterGraphRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeregisterGraphRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeregisterGraphRequest";
  }
  protected:
  explicit DeregisterGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphHandleFieldNumber = 1,
    kSessionHandleFieldNumber = 2,
    kCreateWorkerSessionCalledFieldNumber = 3,
  };
  // string graph_handle = 1;
  void clear_graph_handle();
  const std::string& graph_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_handle();
  PROTOBUF_NODISCARD std::string* release_graph_handle();
  void set_allocated_graph_handle(std::string* graph_handle);
  private:
  const std::string& _internal_graph_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_handle(const std::string& value);
  std::string* _internal_mutable_graph_handle();
  public:

  // string session_handle = 2;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // bool create_worker_session_called = 3;
  void clear_create_worker_session_called();
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);
  private:
  bool _internal_create_worker_session_called() const;
  void _internal_set_create_worker_session_called(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeregisterGraphRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_handle_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    bool create_worker_session_called_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class DeregisterGraphResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.DeregisterGraphResponse) */ {
 public:
  inline DeregisterGraphResponse() : DeregisterGraphResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR DeregisterGraphResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeregisterGraphResponse(const DeregisterGraphResponse& from);
  DeregisterGraphResponse(DeregisterGraphResponse&& from) noexcept
    : DeregisterGraphResponse() {
    *this = ::std::move(from);
  }

  inline DeregisterGraphResponse& operator=(const DeregisterGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeregisterGraphResponse& operator=(DeregisterGraphResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeregisterGraphResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeregisterGraphResponse* internal_default_instance() {
    return reinterpret_cast<const DeregisterGraphResponse*>(
               &_DeregisterGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(DeregisterGraphResponse& a, DeregisterGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeregisterGraphResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeregisterGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeregisterGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeregisterGraphResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const DeregisterGraphResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const DeregisterGraphResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeregisterGraphResponse";
  }
  protected:
  explicit DeregisterGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.DeregisterGraphResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupAllRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupAllRequest) */ {
 public:
  inline CleanupAllRequest() : CleanupAllRequest(nullptr) {}
  ~CleanupAllRequest() override;
  explicit PROTOBUF_CONSTEXPR CleanupAllRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CleanupAllRequest(const CleanupAllRequest& from);
  CleanupAllRequest(CleanupAllRequest&& from) noexcept
    : CleanupAllRequest() {
    *this = ::std::move(from);
  }

  inline CleanupAllRequest& operator=(const CleanupAllRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupAllRequest& operator=(CleanupAllRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CleanupAllRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CleanupAllRequest* internal_default_instance() {
    return reinterpret_cast<const CleanupAllRequest*>(
               &_CleanupAllRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(CleanupAllRequest& a, CleanupAllRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupAllRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupAllRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CleanupAllRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CleanupAllRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CleanupAllRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CleanupAllRequest& from) {
    CleanupAllRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupAllRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupAllRequest";
  }
  protected:
  explicit CleanupAllRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kContainerFieldNumber = 1,
  };
  // repeated string container = 1;
  int container_size() const;
  private:
  int _internal_container_size() const;
  public:
  void clear_container();
  const std::string& container(int index) const;
  std::string* mutable_container(int index);
  void set_container(int index, const std::string& value);
  void set_container(int index, std::string&& value);
  void set_container(int index, const char* value);
  void set_container(int index, const char* value, size_t size);
  std::string* add_container();
  void add_container(const std::string& value);
  void add_container(std::string&& value);
  void add_container(const char* value);
  void add_container(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& container() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_container();
  private:
  const std::string& _internal_container(int index) const;
  std::string* _internal_add_container();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupAllRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> container_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupAllResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.CleanupAllResponse) */ {
 public:
  inline CleanupAllResponse() : CleanupAllResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR CleanupAllResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CleanupAllResponse(const CleanupAllResponse& from);
  CleanupAllResponse(CleanupAllResponse&& from) noexcept
    : CleanupAllResponse() {
    *this = ::std::move(from);
  }

  inline CleanupAllResponse& operator=(const CleanupAllResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupAllResponse& operator=(CleanupAllResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CleanupAllResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CleanupAllResponse* internal_default_instance() {
    return reinterpret_cast<const CleanupAllResponse*>(
               &_CleanupAllResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(CleanupAllResponse& a, CleanupAllResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupAllResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupAllResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CleanupAllResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CleanupAllResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const CleanupAllResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const CleanupAllResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupAllResponse";
  }
  protected:
  explicit CleanupAllResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupAllResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class ExecutorOpts final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ExecutorOpts) */ {
 public:
  inline ExecutorOpts() : ExecutorOpts(nullptr) {}
  ~ExecutorOpts() override;
  explicit PROTOBUF_CONSTEXPR ExecutorOpts(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExecutorOpts(const ExecutorOpts& from);
  ExecutorOpts(ExecutorOpts&& from) noexcept
    : ExecutorOpts() {
    *this = ::std::move(from);
  }

  inline ExecutorOpts& operator=(const ExecutorOpts& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExecutorOpts& operator=(ExecutorOpts&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExecutorOpts& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExecutorOpts* internal_default_instance() {
    return reinterpret_cast<const ExecutorOpts*>(
               &_ExecutorOpts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(ExecutorOpts& a, ExecutorOpts& b) {
    a.Swap(&b);
  }
  inline void Swap(ExecutorOpts* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExecutorOpts* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExecutorOpts* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExecutorOpts>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExecutorOpts& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ExecutorOpts& from) {
    ExecutorOpts::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExecutorOpts* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ExecutorOpts";
  }
  protected:
  explicit ExecutorOpts(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecordCostsFieldNumber = 1,
    kRecordTimelineFieldNumber = 3,
    kRecordPartitionGraphsFieldNumber = 4,
    kReportTensorAllocationsUponOomFieldNumber = 5,
  };
  // bool record_costs = 1;
  void clear_record_costs();
  bool record_costs() const;
  void set_record_costs(bool value);
  private:
  bool _internal_record_costs() const;
  void _internal_set_record_costs(bool value);
  public:

  // bool record_timeline = 3;
  void clear_record_timeline();
  bool record_timeline() const;
  void set_record_timeline(bool value);
  private:
  bool _internal_record_timeline() const;
  void _internal_set_record_timeline(bool value);
  public:

  // bool record_partition_graphs = 4;
  void clear_record_partition_graphs();
  bool record_partition_graphs() const;
  void set_record_partition_graphs(bool value);
  private:
  bool _internal_record_partition_graphs() const;
  void _internal_set_record_partition_graphs(bool value);
  public:

  // bool report_tensor_allocations_upon_oom = 5;
  void clear_report_tensor_allocations_upon_oom();
  bool report_tensor_allocations_upon_oom() const;
  void set_report_tensor_allocations_upon_oom(bool value);
  private:
  bool _internal_report_tensor_allocations_upon_oom() const;
  void _internal_set_report_tensor_allocations_upon_oom(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ExecutorOpts)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool record_costs_;
    bool record_timeline_;
    bool record_partition_graphs_;
    bool report_tensor_allocations_upon_oom_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RunGraphRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunGraphRequest) */ {
 public:
  inline RunGraphRequest() : RunGraphRequest(nullptr) {}
  ~RunGraphRequest() override;
  explicit PROTOBUF_CONSTEXPR RunGraphRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunGraphRequest(const RunGraphRequest& from);
  RunGraphRequest(RunGraphRequest&& from) noexcept
    : RunGraphRequest() {
    *this = ::std::move(from);
  }

  inline RunGraphRequest& operator=(const RunGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunGraphRequest& operator=(RunGraphRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunGraphRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunGraphRequest* internal_default_instance() {
    return reinterpret_cast<const RunGraphRequest*>(
               &_RunGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RunGraphRequest& a, RunGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RunGraphRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunGraphRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunGraphRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunGraphRequest& from) {
    RunGraphRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunGraphRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunGraphRequest";
  }
  protected:
  explicit RunGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSendFieldNumber = 3,
    kRecvKeyFieldNumber = 4,
    kGraphHandleFieldNumber = 1,
    kSessionHandleFieldNumber = 8,
    kExecOptsFieldNumber = 5,
    kStepIdFieldNumber = 2,
    kRequestIdFieldNumber = 11,
    kCreateWorkerSessionCalledFieldNumber = 10,
    kIsPartialFieldNumber = 6,
    kIsLastPartialRunFieldNumber = 7,
    kStoreErrorsInResponseBodyFieldNumber = 9,
  };
  // repeated .tensorflow.NamedTensorProto send = 3;
  int send_size() const;
  private:
  int _internal_send_size() const;
  public:
  void clear_send();
  ::tensorflow::NamedTensorProto* mutable_send(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_send();
  private:
  const ::tensorflow::NamedTensorProto& _internal_send(int index) const;
  ::tensorflow::NamedTensorProto* _internal_add_send();
  public:
  const ::tensorflow::NamedTensorProto& send(int index) const;
  ::tensorflow::NamedTensorProto* add_send();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      send() const;

  // repeated string recv_key = 4;
  int recv_key_size() const;
  private:
  int _internal_recv_key_size() const;
  public:
  void clear_recv_key();
  const std::string& recv_key(int index) const;
  std::string* mutable_recv_key(int index);
  void set_recv_key(int index, const std::string& value);
  void set_recv_key(int index, std::string&& value);
  void set_recv_key(int index, const char* value);
  void set_recv_key(int index, const char* value, size_t size);
  std::string* add_recv_key();
  void add_recv_key(const std::string& value);
  void add_recv_key(std::string&& value);
  void add_recv_key(const char* value);
  void add_recv_key(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& recv_key() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_recv_key();
  private:
  const std::string& _internal_recv_key(int index) const;
  std::string* _internal_add_recv_key();
  public:

  // string graph_handle = 1;
  void clear_graph_handle();
  const std::string& graph_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_graph_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_graph_handle();
  PROTOBUF_NODISCARD std::string* release_graph_handle();
  void set_allocated_graph_handle(std::string* graph_handle);
  private:
  const std::string& _internal_graph_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_graph_handle(const std::string& value);
  std::string* _internal_mutable_graph_handle();
  public:

  // string session_handle = 8;
  void clear_session_handle();
  const std::string& session_handle() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_handle(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_handle();
  PROTOBUF_NODISCARD std::string* release_session_handle();
  void set_allocated_session_handle(std::string* session_handle);
  private:
  const std::string& _internal_session_handle() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_handle(const std::string& value);
  std::string* _internal_mutable_session_handle();
  public:

  // .tensorflow.ExecutorOpts exec_opts = 5;
  bool has_exec_opts() const;
  private:
  bool _internal_has_exec_opts() const;
  public:
  void clear_exec_opts();
  const ::tensorflow::ExecutorOpts& exec_opts() const;
  PROTOBUF_NODISCARD ::tensorflow::ExecutorOpts* release_exec_opts();
  ::tensorflow::ExecutorOpts* mutable_exec_opts();
  void set_allocated_exec_opts(::tensorflow::ExecutorOpts* exec_opts);
  private:
  const ::tensorflow::ExecutorOpts& _internal_exec_opts() const;
  ::tensorflow::ExecutorOpts* _internal_mutable_exec_opts();
  public:
  void unsafe_arena_set_allocated_exec_opts(
      ::tensorflow::ExecutorOpts* exec_opts);
  ::tensorflow::ExecutorOpts* unsafe_arena_release_exec_opts();

  // int64 step_id = 2;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // int64 request_id = 11;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // bool create_worker_session_called = 10;
  void clear_create_worker_session_called();
  bool create_worker_session_called() const;
  void set_create_worker_session_called(bool value);
  private:
  bool _internal_create_worker_session_called() const;
  void _internal_set_create_worker_session_called(bool value);
  public:

  // bool is_partial = 6;
  void clear_is_partial();
  bool is_partial() const;
  void set_is_partial(bool value);
  private:
  bool _internal_is_partial() const;
  void _internal_set_is_partial(bool value);
  public:

  // bool is_last_partial_run = 7;
  void clear_is_last_partial_run();
  bool is_last_partial_run() const;
  void set_is_last_partial_run(bool value);
  private:
  bool _internal_is_last_partial_run() const;
  void _internal_set_is_last_partial_run(bool value);
  public:

  // bool store_errors_in_response_body = 9;
  void clear_store_errors_in_response_body();
  bool store_errors_in_response_body() const;
  void set_store_errors_in_response_body(bool value);
  private:
  bool _internal_store_errors_in_response_body() const;
  void _internal_set_store_errors_in_response_body(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunGraphRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > send_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> recv_key_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr graph_handle_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_handle_;
    ::tensorflow::ExecutorOpts* exec_opts_;
    int64_t step_id_;
    int64_t request_id_;
    bool create_worker_session_called_;
    bool is_partial_;
    bool is_last_partial_run_;
    bool store_errors_in_response_body_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RunGraphResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RunGraphResponse) */ {
 public:
  inline RunGraphResponse() : RunGraphResponse(nullptr) {}
  ~RunGraphResponse() override;
  explicit PROTOBUF_CONSTEXPR RunGraphResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RunGraphResponse(const RunGraphResponse& from);
  RunGraphResponse(RunGraphResponse&& from) noexcept
    : RunGraphResponse() {
    *this = ::std::move(from);
  }

  inline RunGraphResponse& operator=(const RunGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RunGraphResponse& operator=(RunGraphResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RunGraphResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RunGraphResponse* internal_default_instance() {
    return reinterpret_cast<const RunGraphResponse*>(
               &_RunGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(RunGraphResponse& a, RunGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RunGraphResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RunGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RunGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RunGraphResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RunGraphResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RunGraphResponse& from) {
    RunGraphResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RunGraphResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RunGraphResponse";
  }
  protected:
  explicit RunGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRecvFieldNumber = 1,
    kPartitionGraphFieldNumber = 4,
    kStatusErrorMessageFieldNumber = 6,
    kStepStatsFieldNumber = 2,
    kCostGraphFieldNumber = 3,
    kStatusCodeFieldNumber = 5,
  };
  // repeated .tensorflow.NamedTensorProto recv = 1;
  int recv_size() const;
  private:
  int _internal_recv_size() const;
  public:
  void clear_recv();
  ::tensorflow::NamedTensorProto* mutable_recv(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
      mutable_recv();
  private:
  const ::tensorflow::NamedTensorProto& _internal_recv(int index) const;
  ::tensorflow::NamedTensorProto* _internal_add_recv();
  public:
  const ::tensorflow::NamedTensorProto& recv(int index) const;
  ::tensorflow::NamedTensorProto* add_recv();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
      recv() const;

  // repeated .tensorflow.GraphDef partition_graph = 4;
  int partition_graph_size() const;
  private:
  int _internal_partition_graph_size() const;
  public:
  void clear_partition_graph();
  ::tensorflow::GraphDef* mutable_partition_graph(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
      mutable_partition_graph();
  private:
  const ::tensorflow::GraphDef& _internal_partition_graph(int index) const;
  ::tensorflow::GraphDef* _internal_add_partition_graph();
  public:
  const ::tensorflow::GraphDef& partition_graph(int index) const;
  ::tensorflow::GraphDef* add_partition_graph();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
      partition_graph() const;

  // string status_error_message = 6;
  void clear_status_error_message();
  const std::string& status_error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status_error_message();
  PROTOBUF_NODISCARD std::string* release_status_error_message();
  void set_allocated_status_error_message(std::string* status_error_message);
  private:
  const std::string& _internal_status_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status_error_message(const std::string& value);
  std::string* _internal_mutable_status_error_message();
  public:

  // .tensorflow.StepStats step_stats = 2;
  bool has_step_stats() const;
  private:
  bool _internal_has_step_stats() const;
  public:
  void clear_step_stats();
  const ::tensorflow::StepStats& step_stats() const;
  PROTOBUF_NODISCARD ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  private:
  const ::tensorflow::StepStats& _internal_step_stats() const;
  ::tensorflow::StepStats* _internal_mutable_step_stats();
  public:
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // .tensorflow.CostGraphDef cost_graph = 3;
  bool has_cost_graph() const;
  private:
  bool _internal_has_cost_graph() const;
  public:
  void clear_cost_graph();
  const ::tensorflow::CostGraphDef& cost_graph() const;
  PROTOBUF_NODISCARD ::tensorflow::CostGraphDef* release_cost_graph();
  ::tensorflow::CostGraphDef* mutable_cost_graph();
  void set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph);
  private:
  const ::tensorflow::CostGraphDef& _internal_cost_graph() const;
  ::tensorflow::CostGraphDef* _internal_mutable_cost_graph();
  public:
  void unsafe_arena_set_allocated_cost_graph(
      ::tensorflow::CostGraphDef* cost_graph);
  ::tensorflow::CostGraphDef* unsafe_arena_release_cost_graph();

  // .tensorflow.error.Code status_code = 5;
  void clear_status_code();
  ::tensorflow::error::Code status_code() const;
  void set_status_code(::tensorflow::error::Code value);
  private:
  ::tensorflow::error::Code _internal_status_code() const;
  void _internal_set_status_code(::tensorflow::error::Code value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RunGraphResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto > recv_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef > partition_graph_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_error_message_;
    ::tensorflow::StepStats* step_stats_;
    ::tensorflow::CostGraphDef* cost_graph_;
    int status_code_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupGraphRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CleanupGraphRequest) */ {
 public:
  inline CleanupGraphRequest() : CleanupGraphRequest(nullptr) {}
  ~CleanupGraphRequest() override;
  explicit PROTOBUF_CONSTEXPR CleanupGraphRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CleanupGraphRequest(const CleanupGraphRequest& from);
  CleanupGraphRequest(CleanupGraphRequest&& from) noexcept
    : CleanupGraphRequest() {
    *this = ::std::move(from);
  }

  inline CleanupGraphRequest& operator=(const CleanupGraphRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupGraphRequest& operator=(CleanupGraphRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CleanupGraphRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CleanupGraphRequest* internal_default_instance() {
    return reinterpret_cast<const CleanupGraphRequest*>(
               &_CleanupGraphRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(CleanupGraphRequest& a, CleanupGraphRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupGraphRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupGraphRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CleanupGraphRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CleanupGraphRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CleanupGraphRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CleanupGraphRequest& from) {
    CleanupGraphRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CleanupGraphRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupGraphRequest";
  }
  protected:
  explicit CleanupGraphRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepIdFieldNumber = 1,
  };
  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupGraphRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CleanupGraphResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.CleanupGraphResponse) */ {
 public:
  inline CleanupGraphResponse() : CleanupGraphResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR CleanupGraphResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CleanupGraphResponse(const CleanupGraphResponse& from);
  CleanupGraphResponse(CleanupGraphResponse&& from) noexcept
    : CleanupGraphResponse() {
    *this = ::std::move(from);
  }

  inline CleanupGraphResponse& operator=(const CleanupGraphResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CleanupGraphResponse& operator=(CleanupGraphResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CleanupGraphResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CleanupGraphResponse* internal_default_instance() {
    return reinterpret_cast<const CleanupGraphResponse*>(
               &_CleanupGraphResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(CleanupGraphResponse& a, CleanupGraphResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CleanupGraphResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CleanupGraphResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CleanupGraphResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CleanupGraphResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const CleanupGraphResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const CleanupGraphResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CleanupGraphResponse";
  }
  protected:
  explicit CleanupGraphResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.CleanupGraphResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvTensorRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvTensorRequest) */ {
 public:
  inline RecvTensorRequest() : RecvTensorRequest(nullptr) {}
  ~RecvTensorRequest() override;
  explicit PROTOBUF_CONSTEXPR RecvTensorRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RecvTensorRequest(const RecvTensorRequest& from);
  RecvTensorRequest(RecvTensorRequest&& from) noexcept
    : RecvTensorRequest() {
    *this = ::std::move(from);
  }

  inline RecvTensorRequest& operator=(const RecvTensorRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvTensorRequest& operator=(RecvTensorRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RecvTensorRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RecvTensorRequest* internal_default_instance() {
    return reinterpret_cast<const RecvTensorRequest*>(
               &_RecvTensorRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(RecvTensorRequest& a, RecvTensorRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvTensorRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvTensorRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RecvTensorRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RecvTensorRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RecvTensorRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RecvTensorRequest& from) {
    RecvTensorRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvTensorRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvTensorRequest";
  }
  protected:
  explicit RecvTensorRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRendezvousKeyFieldNumber = 2,
    kClientLocalityFieldNumber = 4,
    kServerLocalityFieldNumber = 5,
    kTransportOptionsFieldNumber = 6,
    kStepIdFieldNumber = 1,
    kRequestIdFieldNumber = 7,
    kDmaOkFieldNumber = 3,
  };
  // string rendezvous_key = 2;
  void clear_rendezvous_key();
  const std::string& rendezvous_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_rendezvous_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_rendezvous_key();
  PROTOBUF_NODISCARD std::string* release_rendezvous_key();
  void set_allocated_rendezvous_key(std::string* rendezvous_key);
  private:
  const std::string& _internal_rendezvous_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_rendezvous_key(const std::string& value);
  std::string* _internal_mutable_rendezvous_key();
  public:

  // .tensorflow.DeviceLocality client_locality = 4;
  bool has_client_locality() const;
  private:
  bool _internal_has_client_locality() const;
  public:
  void clear_client_locality();
  const ::tensorflow::DeviceLocality& client_locality() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceLocality* release_client_locality();
  ::tensorflow::DeviceLocality* mutable_client_locality();
  void set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality);
  private:
  const ::tensorflow::DeviceLocality& _internal_client_locality() const;
  ::tensorflow::DeviceLocality* _internal_mutable_client_locality();
  public:
  void unsafe_arena_set_allocated_client_locality(
      ::tensorflow::DeviceLocality* client_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_client_locality();

  // .tensorflow.DeviceLocality server_locality = 5;
  bool has_server_locality() const;
  private:
  bool _internal_has_server_locality() const;
  public:
  void clear_server_locality();
  const ::tensorflow::DeviceLocality& server_locality() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceLocality* release_server_locality();
  ::tensorflow::DeviceLocality* mutable_server_locality();
  void set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality);
  private:
  const ::tensorflow::DeviceLocality& _internal_server_locality() const;
  ::tensorflow::DeviceLocality* _internal_mutable_server_locality();
  public:
  void unsafe_arena_set_allocated_server_locality(
      ::tensorflow::DeviceLocality* server_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_server_locality();

  // .google.protobuf.Any transport_options = 6;
  bool has_transport_options() const;
  private:
  bool _internal_has_transport_options() const;
  public:
  void clear_transport_options();
  const ::PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_transport_options() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_transport_options();
  public:
  void unsafe_arena_set_allocated_transport_options(
      ::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // int64 request_id = 7;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // bool dma_ok = 3;
  void clear_dma_ok();
  bool dma_ok() const;
  void set_dma_ok(bool value);
  private:
  bool _internal_dma_ok() const;
  void _internal_set_dma_ok(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RecvTensorRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr rendezvous_key_;
    ::tensorflow::DeviceLocality* client_locality_;
    ::tensorflow::DeviceLocality* server_locality_;
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options_;
    int64_t step_id_;
    int64_t request_id_;
    bool dma_ok_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvTensorResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvTensorResponse) */ {
 public:
  inline RecvTensorResponse() : RecvTensorResponse(nullptr) {}
  ~RecvTensorResponse() override;
  explicit PROTOBUF_CONSTEXPR RecvTensorResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RecvTensorResponse(const RecvTensorResponse& from);
  RecvTensorResponse(RecvTensorResponse&& from) noexcept
    : RecvTensorResponse() {
    *this = ::std::move(from);
  }

  inline RecvTensorResponse& operator=(const RecvTensorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvTensorResponse& operator=(RecvTensorResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RecvTensorResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RecvTensorResponse* internal_default_instance() {
    return reinterpret_cast<const RecvTensorResponse*>(
               &_RecvTensorResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(RecvTensorResponse& a, RecvTensorResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvTensorResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvTensorResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RecvTensorResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RecvTensorResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RecvTensorResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RecvTensorResponse& from) {
    RecvTensorResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvTensorResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvTensorResponse";
  }
  protected:
  explicit RecvTensorResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorFieldNumber = 1,
    kTransportOptionsFieldNumber = 4,
    kSendStartMicrosFieldNumber = 3,
    kIsDeadFieldNumber = 2,
    kRequireAckFieldNumber = 5,
  };
  // .tensorflow.TensorProto tensor = 1;
  bool has_tensor() const;
  private:
  bool _internal_has_tensor() const;
  public:
  void clear_tensor();
  const ::tensorflow::TensorProto& tensor() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor();
  ::tensorflow::TensorProto* mutable_tensor();
  void set_allocated_tensor(::tensorflow::TensorProto* tensor);
  private:
  const ::tensorflow::TensorProto& _internal_tensor() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor();
  public:
  void unsafe_arena_set_allocated_tensor(
      ::tensorflow::TensorProto* tensor);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor();

  // .google.protobuf.Any transport_options = 4;
  bool has_transport_options() const;
  private:
  bool _internal_has_transport_options() const;
  public:
  void clear_transport_options();
  const ::PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_transport_options() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_transport_options();
  public:
  void unsafe_arena_set_allocated_transport_options(
      ::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // int64 send_start_micros = 3;
  void clear_send_start_micros();
  int64_t send_start_micros() const;
  void set_send_start_micros(int64_t value);
  private:
  int64_t _internal_send_start_micros() const;
  void _internal_set_send_start_micros(int64_t value);
  public:

  // bool is_dead = 2;
  void clear_is_dead();
  bool is_dead() const;
  void set_is_dead(bool value);
  private:
  bool _internal_is_dead() const;
  void _internal_set_is_dead(bool value);
  public:

  // bool require_ack = 5;
  void clear_require_ack();
  bool require_ack() const;
  void set_require_ack(bool value);
  private:
  bool _internal_require_ack() const;
  void _internal_set_require_ack(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RecvTensorResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TensorProto* tensor_;
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options_;
    int64_t send_start_micros_;
    bool is_dead_;
    bool require_ack_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class MarkRecvFinishedRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MarkRecvFinishedRequest) */ {
 public:
  inline MarkRecvFinishedRequest() : MarkRecvFinishedRequest(nullptr) {}
  ~MarkRecvFinishedRequest() override;
  explicit PROTOBUF_CONSTEXPR MarkRecvFinishedRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarkRecvFinishedRequest(const MarkRecvFinishedRequest& from);
  MarkRecvFinishedRequest(MarkRecvFinishedRequest&& from) noexcept
    : MarkRecvFinishedRequest() {
    *this = ::std::move(from);
  }

  inline MarkRecvFinishedRequest& operator=(const MarkRecvFinishedRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkRecvFinishedRequest& operator=(MarkRecvFinishedRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarkRecvFinishedRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarkRecvFinishedRequest* internal_default_instance() {
    return reinterpret_cast<const MarkRecvFinishedRequest*>(
               &_MarkRecvFinishedRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(MarkRecvFinishedRequest& a, MarkRecvFinishedRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkRecvFinishedRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkRecvFinishedRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarkRecvFinishedRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarkRecvFinishedRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MarkRecvFinishedRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MarkRecvFinishedRequest& from) {
    MarkRecvFinishedRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkRecvFinishedRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MarkRecvFinishedRequest";
  }
  protected:
  explicit MarkRecvFinishedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRequestIdFieldNumber = 1,
  };
  // int64 request_id = 1;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MarkRecvFinishedRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t request_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class MarkRecvFinishedResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.MarkRecvFinishedResponse) */ {
 public:
  inline MarkRecvFinishedResponse() : MarkRecvFinishedResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR MarkRecvFinishedResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarkRecvFinishedResponse(const MarkRecvFinishedResponse& from);
  MarkRecvFinishedResponse(MarkRecvFinishedResponse&& from) noexcept
    : MarkRecvFinishedResponse() {
    *this = ::std::move(from);
  }

  inline MarkRecvFinishedResponse& operator=(const MarkRecvFinishedResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkRecvFinishedResponse& operator=(MarkRecvFinishedResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarkRecvFinishedResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarkRecvFinishedResponse* internal_default_instance() {
    return reinterpret_cast<const MarkRecvFinishedResponse*>(
               &_MarkRecvFinishedResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(MarkRecvFinishedResponse& a, MarkRecvFinishedResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkRecvFinishedResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkRecvFinishedResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarkRecvFinishedResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarkRecvFinishedResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const MarkRecvFinishedResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const MarkRecvFinishedResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MarkRecvFinishedResponse";
  }
  protected:
  explicit MarkRecvFinishedResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.MarkRecvFinishedResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class LoggingRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LoggingRequest) */ {
 public:
  inline LoggingRequest() : LoggingRequest(nullptr) {}
  ~LoggingRequest() override;
  explicit PROTOBUF_CONSTEXPR LoggingRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoggingRequest(const LoggingRequest& from);
  LoggingRequest(LoggingRequest&& from) noexcept
    : LoggingRequest() {
    *this = ::std::move(from);
  }

  inline LoggingRequest& operator=(const LoggingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoggingRequest& operator=(LoggingRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoggingRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoggingRequest* internal_default_instance() {
    return reinterpret_cast<const LoggingRequest*>(
               &_LoggingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(LoggingRequest& a, LoggingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LoggingRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoggingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoggingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoggingRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoggingRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LoggingRequest& from) {
    LoggingRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoggingRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LoggingRequest";
  }
  protected:
  explicit LoggingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFetchStepIdFieldNumber = 3,
    kEnableRpcLoggingFieldNumber = 1,
    kDisableRpcLoggingFieldNumber = 4,
    kClearFieldNumber = 2,
  };
  // repeated int64 fetch_step_id = 3;
  int fetch_step_id_size() const;
  private:
  int _internal_fetch_step_id_size() const;
  public:
  void clear_fetch_step_id();
  private:
  int64_t _internal_fetch_step_id(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_fetch_step_id() const;
  void _internal_add_fetch_step_id(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_fetch_step_id();
  public:
  int64_t fetch_step_id(int index) const;
  void set_fetch_step_id(int index, int64_t value);
  void add_fetch_step_id(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      fetch_step_id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_fetch_step_id();

  // bool enable_rpc_logging = 1;
  void clear_enable_rpc_logging();
  bool enable_rpc_logging() const;
  void set_enable_rpc_logging(bool value);
  private:
  bool _internal_enable_rpc_logging() const;
  void _internal_set_enable_rpc_logging(bool value);
  public:

  // bool disable_rpc_logging = 4;
  void clear_disable_rpc_logging();
  bool disable_rpc_logging() const;
  void set_disable_rpc_logging(bool value);
  private:
  bool _internal_disable_rpc_logging() const;
  void _internal_set_disable_rpc_logging(bool value);
  public:

  // bool clear = 2;
  void clear_clear();
  bool clear() const;
  void set_clear(bool value);
  private:
  bool _internal_clear() const;
  void _internal_set_clear(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.LoggingRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > fetch_step_id_;
    mutable std::atomic<int> _fetch_step_id_cached_byte_size_;
    bool enable_rpc_logging_;
    bool disable_rpc_logging_;
    bool clear_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class LabeledStepStats final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LabeledStepStats) */ {
 public:
  inline LabeledStepStats() : LabeledStepStats(nullptr) {}
  ~LabeledStepStats() override;
  explicit PROTOBUF_CONSTEXPR LabeledStepStats(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LabeledStepStats(const LabeledStepStats& from);
  LabeledStepStats(LabeledStepStats&& from) noexcept
    : LabeledStepStats() {
    *this = ::std::move(from);
  }

  inline LabeledStepStats& operator=(const LabeledStepStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline LabeledStepStats& operator=(LabeledStepStats&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LabeledStepStats& default_instance() {
    return *internal_default_instance();
  }
  static inline const LabeledStepStats* internal_default_instance() {
    return reinterpret_cast<const LabeledStepStats*>(
               &_LabeledStepStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(LabeledStepStats& a, LabeledStepStats& b) {
    a.Swap(&b);
  }
  inline void Swap(LabeledStepStats* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LabeledStepStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LabeledStepStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LabeledStepStats>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LabeledStepStats& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LabeledStepStats& from) {
    LabeledStepStats::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LabeledStepStats* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LabeledStepStats";
  }
  protected:
  explicit LabeledStepStats(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepStatsFieldNumber = 2,
    kStepIdFieldNumber = 1,
  };
  // .tensorflow.StepStats step_stats = 2;
  bool has_step_stats() const;
  private:
  bool _internal_has_step_stats() const;
  public:
  void clear_step_stats();
  const ::tensorflow::StepStats& step_stats() const;
  PROTOBUF_NODISCARD ::tensorflow::StepStats* release_step_stats();
  ::tensorflow::StepStats* mutable_step_stats();
  void set_allocated_step_stats(::tensorflow::StepStats* step_stats);
  private:
  const ::tensorflow::StepStats& _internal_step_stats() const;
  ::tensorflow::StepStats* _internal_mutable_step_stats();
  public:
  void unsafe_arena_set_allocated_step_stats(
      ::tensorflow::StepStats* step_stats);
  ::tensorflow::StepStats* unsafe_arena_release_step_stats();

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.LabeledStepStats)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::StepStats* step_stats_;
    int64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class LoggingResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.LoggingResponse) */ {
 public:
  inline LoggingResponse() : LoggingResponse(nullptr) {}
  ~LoggingResponse() override;
  explicit PROTOBUF_CONSTEXPR LoggingResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoggingResponse(const LoggingResponse& from);
  LoggingResponse(LoggingResponse&& from) noexcept
    : LoggingResponse() {
    *this = ::std::move(from);
  }

  inline LoggingResponse& operator=(const LoggingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoggingResponse& operator=(LoggingResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoggingResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoggingResponse* internal_default_instance() {
    return reinterpret_cast<const LoggingResponse*>(
               &_LoggingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(LoggingResponse& a, LoggingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoggingResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoggingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoggingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoggingResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoggingResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LoggingResponse& from) {
    LoggingResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoggingResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.LoggingResponse";
  }
  protected:
  explicit LoggingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepFieldNumber = 1,
  };
  // repeated .tensorflow.LabeledStepStats step = 1;
  int step_size() const;
  private:
  int _internal_step_size() const;
  public:
  void clear_step();
  ::tensorflow::LabeledStepStats* mutable_step(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >*
      mutable_step();
  private:
  const ::tensorflow::LabeledStepStats& _internal_step(int index) const;
  ::tensorflow::LabeledStepStats* _internal_add_step();
  public:
  const ::tensorflow::LabeledStepStats& step(int index) const;
  ::tensorflow::LabeledStepStats* add_step();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >&
      step() const;

  // @@protoc_insertion_point(class_scope:tensorflow.LoggingResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats > step_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class TraceOpts final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TraceOpts) */ {
 public:
  inline TraceOpts() : TraceOpts(nullptr) {}
  ~TraceOpts() override;
  explicit PROTOBUF_CONSTEXPR TraceOpts(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TraceOpts(const TraceOpts& from);
  TraceOpts(TraceOpts&& from) noexcept
    : TraceOpts() {
    *this = ::std::move(from);
  }

  inline TraceOpts& operator=(const TraceOpts& from) {
    CopyFrom(from);
    return *this;
  }
  inline TraceOpts& operator=(TraceOpts&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TraceOpts& default_instance() {
    return *internal_default_instance();
  }
  static inline const TraceOpts* internal_default_instance() {
    return reinterpret_cast<const TraceOpts*>(
               &_TraceOpts_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(TraceOpts& a, TraceOpts& b) {
    a.Swap(&b);
  }
  inline void Swap(TraceOpts* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TraceOpts* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TraceOpts* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TraceOpts>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TraceOpts& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TraceOpts& from) {
    TraceOpts::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TraceOpts* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TraceOpts";
  }
  protected:
  explicit TraceOpts(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDurationFieldNumber = 1,
    kUseStepProfilerFieldNumber = 2,
    kUseKernelProfilerFieldNumber = 3,
    kUseExtendedProfilerFieldNumber = 4,
    kUseGpuProfilerFieldNumber = 5,
    kUseSampleProfilerFieldNumber = 6,
  };
  // double duration = 1;
  void clear_duration();
  double duration() const;
  void set_duration(double value);
  private:
  double _internal_duration() const;
  void _internal_set_duration(double value);
  public:

  // bool use_step_profiler = 2;
  void clear_use_step_profiler();
  bool use_step_profiler() const;
  void set_use_step_profiler(bool value);
  private:
  bool _internal_use_step_profiler() const;
  void _internal_set_use_step_profiler(bool value);
  public:

  // bool use_kernel_profiler = 3;
  void clear_use_kernel_profiler();
  bool use_kernel_profiler() const;
  void set_use_kernel_profiler(bool value);
  private:
  bool _internal_use_kernel_profiler() const;
  void _internal_set_use_kernel_profiler(bool value);
  public:

  // bool use_extended_profiler = 4;
  void clear_use_extended_profiler();
  bool use_extended_profiler() const;
  void set_use_extended_profiler(bool value);
  private:
  bool _internal_use_extended_profiler() const;
  void _internal_set_use_extended_profiler(bool value);
  public:

  // bool use_gpu_profiler = 5;
  void clear_use_gpu_profiler();
  bool use_gpu_profiler() const;
  void set_use_gpu_profiler(bool value);
  private:
  bool _internal_use_gpu_profiler() const;
  void _internal_set_use_gpu_profiler(bool value);
  public:

  // bool use_sample_profiler = 6;
  void clear_use_sample_profiler();
  bool use_sample_profiler() const;
  void set_use_sample_profiler(bool value);
  private:
  bool _internal_use_sample_profiler() const;
  void _internal_set_use_sample_profiler(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TraceOpts)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double duration_;
    bool use_step_profiler_;
    bool use_kernel_profiler_;
    bool use_extended_profiler_;
    bool use_gpu_profiler_;
    bool use_sample_profiler_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class TracingRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TracingRequest) */ {
 public:
  inline TracingRequest() : TracingRequest(nullptr) {}
  ~TracingRequest() override;
  explicit PROTOBUF_CONSTEXPR TracingRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TracingRequest(const TracingRequest& from);
  TracingRequest(TracingRequest&& from) noexcept
    : TracingRequest() {
    *this = ::std::move(from);
  }

  inline TracingRequest& operator=(const TracingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline TracingRequest& operator=(TracingRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TracingRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const TracingRequest* internal_default_instance() {
    return reinterpret_cast<const TracingRequest*>(
               &_TracingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(TracingRequest& a, TracingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(TracingRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TracingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TracingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TracingRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TracingRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TracingRequest& from) {
    TracingRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TracingRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TracingRequest";
  }
  protected:
  explicit TracingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOptionsFieldNumber = 1,
  };
  // .tensorflow.TraceOpts options = 1;
  bool has_options() const;
  private:
  bool _internal_has_options() const;
  public:
  void clear_options();
  const ::tensorflow::TraceOpts& options() const;
  PROTOBUF_NODISCARD ::tensorflow::TraceOpts* release_options();
  ::tensorflow::TraceOpts* mutable_options();
  void set_allocated_options(::tensorflow::TraceOpts* options);
  private:
  const ::tensorflow::TraceOpts& _internal_options() const;
  ::tensorflow::TraceOpts* _internal_mutable_options();
  public:
  void unsafe_arena_set_allocated_options(
      ::tensorflow::TraceOpts* options);
  ::tensorflow::TraceOpts* unsafe_arena_release_options();

  // @@protoc_insertion_point(class_scope:tensorflow.TracingRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::TraceOpts* options_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class TracingResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.TracingResponse) */ {
 public:
  inline TracingResponse() : TracingResponse(nullptr) {}
  explicit PROTOBUF_CONSTEXPR TracingResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TracingResponse(const TracingResponse& from);
  TracingResponse(TracingResponse&& from) noexcept
    : TracingResponse() {
    *this = ::std::move(from);
  }

  inline TracingResponse& operator=(const TracingResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline TracingResponse& operator=(TracingResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TracingResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const TracingResponse* internal_default_instance() {
    return reinterpret_cast<const TracingResponse*>(
               &_TracingResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(TracingResponse& a, TracingResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(TracingResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TracingResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TracingResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TracingResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const TracingResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const TracingResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TracingResponse";
  }
  protected:
  explicit TracingResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.TracingResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvBufRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufRequest) */ {
 public:
  inline RecvBufRequest() : RecvBufRequest(nullptr) {}
  ~RecvBufRequest() override;
  explicit PROTOBUF_CONSTEXPR RecvBufRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RecvBufRequest(const RecvBufRequest& from);
  RecvBufRequest(RecvBufRequest&& from) noexcept
    : RecvBufRequest() {
    *this = ::std::move(from);
  }

  inline RecvBufRequest& operator=(const RecvBufRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvBufRequest& operator=(RecvBufRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RecvBufRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RecvBufRequest* internal_default_instance() {
    return reinterpret_cast<const RecvBufRequest*>(
               &_RecvBufRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(RecvBufRequest& a, RecvBufRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvBufRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvBufRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RecvBufRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RecvBufRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RecvBufRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RecvBufRequest& from) {
    RecvBufRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvBufRequest";
  }
  protected:
  explicit RecvBufRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBufRendezvousKeyFieldNumber = 2,
    kSrcDeviceFieldNumber = 8,
    kDstDeviceFieldNumber = 9,
    kClientLocalityFieldNumber = 5,
    kServerLocalityFieldNumber = 6,
    kTransportOptionsFieldNumber = 7,
    kStepIdFieldNumber = 1,
    kNumBytesFieldNumber = 3,
    kBufPtrFieldNumber = 4,
    kRequestIdFieldNumber = 10,
    kSrcIncarnationFieldNumber = 11,
  };
  // string buf_rendezvous_key = 2;
  void clear_buf_rendezvous_key();
  const std::string& buf_rendezvous_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_buf_rendezvous_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_buf_rendezvous_key();
  PROTOBUF_NODISCARD std::string* release_buf_rendezvous_key();
  void set_allocated_buf_rendezvous_key(std::string* buf_rendezvous_key);
  private:
  const std::string& _internal_buf_rendezvous_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_buf_rendezvous_key(const std::string& value);
  std::string* _internal_mutable_buf_rendezvous_key();
  public:

  // string src_device = 8;
  void clear_src_device();
  const std::string& src_device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_src_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_src_device();
  PROTOBUF_NODISCARD std::string* release_src_device();
  void set_allocated_src_device(std::string* src_device);
  private:
  const std::string& _internal_src_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_src_device(const std::string& value);
  std::string* _internal_mutable_src_device();
  public:

  // string dst_device = 9;
  void clear_dst_device();
  const std::string& dst_device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_dst_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_dst_device();
  PROTOBUF_NODISCARD std::string* release_dst_device();
  void set_allocated_dst_device(std::string* dst_device);
  private:
  const std::string& _internal_dst_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_dst_device(const std::string& value);
  std::string* _internal_mutable_dst_device();
  public:

  // .tensorflow.DeviceLocality client_locality = 5;
  bool has_client_locality() const;
  private:
  bool _internal_has_client_locality() const;
  public:
  void clear_client_locality();
  const ::tensorflow::DeviceLocality& client_locality() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceLocality* release_client_locality();
  ::tensorflow::DeviceLocality* mutable_client_locality();
  void set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality);
  private:
  const ::tensorflow::DeviceLocality& _internal_client_locality() const;
  ::tensorflow::DeviceLocality* _internal_mutable_client_locality();
  public:
  void unsafe_arena_set_allocated_client_locality(
      ::tensorflow::DeviceLocality* client_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_client_locality();

  // .tensorflow.DeviceLocality server_locality = 6;
  bool has_server_locality() const;
  private:
  bool _internal_has_server_locality() const;
  public:
  void clear_server_locality();
  const ::tensorflow::DeviceLocality& server_locality() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceLocality* release_server_locality();
  ::tensorflow::DeviceLocality* mutable_server_locality();
  void set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality);
  private:
  const ::tensorflow::DeviceLocality& _internal_server_locality() const;
  ::tensorflow::DeviceLocality* _internal_mutable_server_locality();
  public:
  void unsafe_arena_set_allocated_server_locality(
      ::tensorflow::DeviceLocality* server_locality);
  ::tensorflow::DeviceLocality* unsafe_arena_release_server_locality();

  // .google.protobuf.Any transport_options = 7;
  bool has_transport_options() const;
  private:
  bool _internal_has_transport_options() const;
  public:
  void clear_transport_options();
  const ::PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_transport_options() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_transport_options();
  public:
  void unsafe_arena_set_allocated_transport_options(
      ::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // int64 step_id = 1;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // int64 num_bytes = 3;
  void clear_num_bytes();
  int64_t num_bytes() const;
  void set_num_bytes(int64_t value);
  private:
  int64_t _internal_num_bytes() const;
  void _internal_set_num_bytes(int64_t value);
  public:

  // fixed64 buf_ptr = 4;
  void clear_buf_ptr();
  uint64_t buf_ptr() const;
  void set_buf_ptr(uint64_t value);
  private:
  uint64_t _internal_buf_ptr() const;
  void _internal_set_buf_ptr(uint64_t value);
  public:

  // int64 request_id = 10;
  void clear_request_id();
  int64_t request_id() const;
  void set_request_id(int64_t value);
  private:
  int64_t _internal_request_id() const;
  void _internal_set_request_id(int64_t value);
  public:

  // uint64 src_incarnation = 11;
  void clear_src_incarnation();
  uint64_t src_incarnation() const;
  void set_src_incarnation(uint64_t value);
  private:
  uint64_t _internal_src_incarnation() const;
  void _internal_set_src_incarnation(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr buf_rendezvous_key_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr src_device_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr dst_device_;
    ::tensorflow::DeviceLocality* client_locality_;
    ::tensorflow::DeviceLocality* server_locality_;
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options_;
    int64_t step_id_;
    int64_t num_bytes_;
    uint64_t buf_ptr_;
    int64_t request_id_;
    uint64_t src_incarnation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class RecvBufResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufResponse) */ {
 public:
  inline RecvBufResponse() : RecvBufResponse(nullptr) {}
  ~RecvBufResponse() override;
  explicit PROTOBUF_CONSTEXPR RecvBufResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RecvBufResponse(const RecvBufResponse& from);
  RecvBufResponse(RecvBufResponse&& from) noexcept
    : RecvBufResponse() {
    *this = ::std::move(from);
  }

  inline RecvBufResponse& operator=(const RecvBufResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvBufResponse& operator=(RecvBufResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RecvBufResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RecvBufResponse* internal_default_instance() {
    return reinterpret_cast<const RecvBufResponse*>(
               &_RecvBufResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(RecvBufResponse& a, RecvBufResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvBufResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvBufResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RecvBufResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RecvBufResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RecvBufResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RecvBufResponse& from) {
    RecvBufResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvBufResponse";
  }
  protected:
  explicit RecvBufResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTransportOptionsFieldNumber = 4,
    kBufPtrFieldNumber = 1,
    kNumBytesFieldNumber = 2,
    kSendStartMicrosFieldNumber = 5,
    kIsDeadFieldNumber = 3,
    kRequireAckFieldNumber = 6,
  };
  // .google.protobuf.Any transport_options = 4;
  bool has_transport_options() const;
  private:
  bool _internal_has_transport_options() const;
  public:
  void clear_transport_options();
  const ::PROTOBUF_NAMESPACE_ID::Any& transport_options() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::Any* release_transport_options();
  ::PROTOBUF_NAMESPACE_ID::Any* mutable_transport_options();
  void set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  private:
  const ::PROTOBUF_NAMESPACE_ID::Any& _internal_transport_options() const;
  ::PROTOBUF_NAMESPACE_ID::Any* _internal_mutable_transport_options();
  public:
  void unsafe_arena_set_allocated_transport_options(
      ::PROTOBUF_NAMESPACE_ID::Any* transport_options);
  ::PROTOBUF_NAMESPACE_ID::Any* unsafe_arena_release_transport_options();

  // fixed64 buf_ptr = 1;
  void clear_buf_ptr();
  uint64_t buf_ptr() const;
  void set_buf_ptr(uint64_t value);
  private:
  uint64_t _internal_buf_ptr() const;
  void _internal_set_buf_ptr(uint64_t value);
  public:

  // int64 num_bytes = 2;
  void clear_num_bytes();
  int64_t num_bytes() const;
  void set_num_bytes(int64_t value);
  private:
  int64_t _internal_num_bytes() const;
  void _internal_set_num_bytes(int64_t value);
  public:

  // int64 send_start_micros = 5;
  void clear_send_start_micros();
  int64_t send_start_micros() const;
  void set_send_start_micros(int64_t value);
  private:
  int64_t _internal_send_start_micros() const;
  void _internal_set_send_start_micros(int64_t value);
  public:

  // bool is_dead = 3;
  void clear_is_dead();
  bool is_dead() const;
  void set_is_dead(bool value);
  private:
  bool _internal_is_dead() const;
  void _internal_set_is_dead(bool value);
  public:

  // bool require_ack = 6;
  void clear_require_ack();
  bool require_ack() const;
  void set_require_ack(bool value);
  private:
  bool _internal_require_ack() const;
  void _internal_set_require_ack(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options_;
    uint64_t buf_ptr_;
    int64_t num_bytes_;
    int64_t send_start_micros_;
    bool is_dead_;
    bool require_ack_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteGroupRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteGroupRequest) */ {
 public:
  inline CompleteGroupRequest() : CompleteGroupRequest(nullptr) {}
  ~CompleteGroupRequest() override;
  explicit PROTOBUF_CONSTEXPR CompleteGroupRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompleteGroupRequest(const CompleteGroupRequest& from);
  CompleteGroupRequest(CompleteGroupRequest&& from) noexcept
    : CompleteGroupRequest() {
    *this = ::std::move(from);
  }

  inline CompleteGroupRequest& operator=(const CompleteGroupRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteGroupRequest& operator=(CompleteGroupRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompleteGroupRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompleteGroupRequest* internal_default_instance() {
    return reinterpret_cast<const CompleteGroupRequest*>(
               &_CompleteGroupRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    29;

  friend void swap(CompleteGroupRequest& a, CompleteGroupRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteGroupRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteGroupRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompleteGroupRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompleteGroupRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompleteGroupRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompleteGroupRequest& from) {
    CompleteGroupRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteGroupRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteGroupRequest";
  }
  protected:
  explicit CompleteGroupRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceTypeFieldNumber = 3,
    kDeviceAttributesFieldNumber = 6,
    kGroupKeyFieldNumber = 1,
    kGroupSizeFieldNumber = 2,
    kCollectiveTypeFieldNumber = 5,
  };
  // string device_type = 3;
  void clear_device_type();
  const std::string& device_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_type();
  PROTOBUF_NODISCARD std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  private:
  const std::string& _internal_device_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_type(const std::string& value);
  std::string* _internal_mutable_device_type();
  public:

  // .tensorflow.DeviceAttributes device_attributes = 6;
  bool has_device_attributes() const;
  private:
  bool _internal_has_device_attributes() const;
  public:
  void clear_device_attributes();
  const ::tensorflow::DeviceAttributes& device_attributes() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceAttributes* release_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes();
  void set_allocated_device_attributes(::tensorflow::DeviceAttributes* device_attributes);
  private:
  const ::tensorflow::DeviceAttributes& _internal_device_attributes() const;
  ::tensorflow::DeviceAttributes* _internal_mutable_device_attributes();
  public:
  void unsafe_arena_set_allocated_device_attributes(
      ::tensorflow::DeviceAttributes* device_attributes);
  ::tensorflow::DeviceAttributes* unsafe_arena_release_device_attributes();

  // int32 group_key = 1;
  void clear_group_key();
  int32_t group_key() const;
  void set_group_key(int32_t value);
  private:
  int32_t _internal_group_key() const;
  void _internal_set_group_key(int32_t value);
  public:

  // int32 group_size = 2;
  void clear_group_size();
  int32_t group_size() const;
  void set_group_size(int32_t value);
  private:
  int32_t _internal_group_size() const;
  void _internal_set_group_size(int32_t value);
  public:

  // int32 collective_type = 5;
  void clear_collective_type();
  int32_t collective_type() const;
  void set_collective_type(int32_t value);
  private:
  int32_t _internal_collective_type() const;
  void _internal_set_collective_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteGroupRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
    ::tensorflow::DeviceAttributes* device_attributes_;
    int32_t group_key_;
    int32_t group_size_;
    int32_t collective_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteGroupResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteGroupResponse) */ {
 public:
  inline CompleteGroupResponse() : CompleteGroupResponse(nullptr) {}
  ~CompleteGroupResponse() override;
  explicit PROTOBUF_CONSTEXPR CompleteGroupResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompleteGroupResponse(const CompleteGroupResponse& from);
  CompleteGroupResponse(CompleteGroupResponse&& from) noexcept
    : CompleteGroupResponse() {
    *this = ::std::move(from);
  }

  inline CompleteGroupResponse& operator=(const CompleteGroupResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteGroupResponse& operator=(CompleteGroupResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompleteGroupResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompleteGroupResponse* internal_default_instance() {
    return reinterpret_cast<const CompleteGroupResponse*>(
               &_CompleteGroupResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    30;

  friend void swap(CompleteGroupResponse& a, CompleteGroupResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteGroupResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteGroupResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompleteGroupResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompleteGroupResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompleteGroupResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompleteGroupResponse& from) {
    CompleteGroupResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteGroupResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteGroupResponse";
  }
  protected:
  explicit CompleteGroupResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceAttributesFieldNumber = 8,
    kDeviceTypeFieldNumber = 3,
    kCommunicatorKeyFieldNumber = 7,
    kGroupKeyFieldNumber = 1,
    kGroupSizeFieldNumber = 2,
    kNumTasksFieldNumber = 4,
  };
  // repeated .tensorflow.DeviceAttributes device_attributes = 8;
  int device_attributes_size() const;
  private:
  int _internal_device_attributes_size() const;
  public:
  void clear_device_attributes();
  ::tensorflow::DeviceAttributes* mutable_device_attributes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
      mutable_device_attributes();
  private:
  const ::tensorflow::DeviceAttributes& _internal_device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* _internal_add_device_attributes();
  public:
  const ::tensorflow::DeviceAttributes& device_attributes(int index) const;
  ::tensorflow::DeviceAttributes* add_device_attributes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
      device_attributes() const;

  // string device_type = 3;
  void clear_device_type();
  const std::string& device_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_type();
  PROTOBUF_NODISCARD std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  private:
  const std::string& _internal_device_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_type(const std::string& value);
  std::string* _internal_mutable_device_type();
  public:

  // bytes communicator_key = 7;
  void clear_communicator_key();
  const std::string& communicator_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_communicator_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_communicator_key();
  PROTOBUF_NODISCARD std::string* release_communicator_key();
  void set_allocated_communicator_key(std::string* communicator_key);
  private:
  const std::string& _internal_communicator_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_communicator_key(const std::string& value);
  std::string* _internal_mutable_communicator_key();
  public:

  // int32 group_key = 1;
  void clear_group_key();
  int32_t group_key() const;
  void set_group_key(int32_t value);
  private:
  int32_t _internal_group_key() const;
  void _internal_set_group_key(int32_t value);
  public:

  // int32 group_size = 2;
  void clear_group_size();
  int32_t group_size() const;
  void set_group_size(int32_t value);
  private:
  int32_t _internal_group_size() const;
  void _internal_set_group_size(int32_t value);
  public:

  // int32 num_tasks = 4;
  void clear_num_tasks();
  int32_t num_tasks() const;
  void set_num_tasks(int32_t value);
  private:
  int32_t _internal_num_tasks() const;
  void _internal_set_num_tasks(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteGroupResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes > device_attributes_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr communicator_key_;
    int32_t group_key_;
    int32_t group_size_;
    int32_t num_tasks_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteInstanceRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteInstanceRequest) */ {
 public:
  inline CompleteInstanceRequest() : CompleteInstanceRequest(nullptr) {}
  ~CompleteInstanceRequest() override;
  explicit PROTOBUF_CONSTEXPR CompleteInstanceRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompleteInstanceRequest(const CompleteInstanceRequest& from);
  CompleteInstanceRequest(CompleteInstanceRequest&& from) noexcept
    : CompleteInstanceRequest() {
    *this = ::std::move(from);
  }

  inline CompleteInstanceRequest& operator=(const CompleteInstanceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteInstanceRequest& operator=(CompleteInstanceRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompleteInstanceRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompleteInstanceRequest* internal_default_instance() {
    return reinterpret_cast<const CompleteInstanceRequest*>(
               &_CompleteInstanceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    31;

  friend void swap(CompleteInstanceRequest& a, CompleteInstanceRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteInstanceRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteInstanceRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompleteInstanceRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompleteInstanceRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompleteInstanceRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompleteInstanceRequest& from) {
    CompleteInstanceRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteInstanceRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteInstanceRequest";
  }
  protected:
  explicit CompleteInstanceRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSubdivOffsetFieldNumber = 9,
    kNameFieldNumber = 1,
    kDeviceTypeFieldNumber = 8,
    kDeviceFieldNumber = 10,
    kShapeFieldNumber = 4,
    kTypeFieldNumber = 2,
    kDataTypeFieldNumber = 3,
    kGroupKeyFieldNumber = 5,
    kGroupSizeFieldNumber = 6,
    kInstanceKeyFieldNumber = 7,
    kIsSourceFieldNumber = 11,
    kStepIdFieldNumber = 12,
  };
  // repeated int32 subdiv_offset = 9;
  int subdiv_offset_size() const;
  private:
  int _internal_subdiv_offset_size() const;
  public:
  void clear_subdiv_offset();
  private:
  int32_t _internal_subdiv_offset(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_subdiv_offset() const;
  void _internal_add_subdiv_offset(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_subdiv_offset();
  public:
  int32_t subdiv_offset(int index) const;
  void set_subdiv_offset(int index, int32_t value);
  void add_subdiv_offset(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      subdiv_offset() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_subdiv_offset();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string device_type = 8;
  void clear_device_type();
  const std::string& device_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device_type();
  PROTOBUF_NODISCARD std::string* release_device_type();
  void set_allocated_device_type(std::string* device_type);
  private:
  const std::string& _internal_device_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device_type(const std::string& value);
  std::string* _internal_mutable_device_type();
  public:

  // string device = 10;
  void clear_device();
  const std::string& device() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_device(ArgT0&& arg0, ArgT... args);
  std::string* mutable_device();
  PROTOBUF_NODISCARD std::string* release_device();
  void set_allocated_device(std::string* device);
  private:
  const std::string& _internal_device() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_device(const std::string& value);
  std::string* _internal_mutable_device();
  public:

  // .tensorflow.TensorShapeProto shape = 4;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // int32 type = 2;
  void clear_type();
  int32_t type() const;
  void set_type(int32_t value);
  private:
  int32_t _internal_type() const;
  void _internal_set_type(int32_t value);
  public:

  // .tensorflow.DataType data_type = 3;
  void clear_data_type();
  ::tensorflow::DataType data_type() const;
  void set_data_type(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_data_type() const;
  void _internal_set_data_type(::tensorflow::DataType value);
  public:

  // int32 group_key = 5;
  void clear_group_key();
  int32_t group_key() const;
  void set_group_key(int32_t value);
  private:
  int32_t _internal_group_key() const;
  void _internal_set_group_key(int32_t value);
  public:

  // int32 group_size = 6;
  void clear_group_size();
  int32_t group_size() const;
  void set_group_size(int32_t value);
  private:
  int32_t _internal_group_size() const;
  void _internal_set_group_size(int32_t value);
  public:

  // int32 instance_key = 7;
  void clear_instance_key();
  int32_t instance_key() const;
  void set_instance_key(int32_t value);
  private:
  int32_t _internal_instance_key() const;
  void _internal_set_instance_key(int32_t value);
  public:

  // bool is_source = 11;
  void clear_is_source();
  bool is_source() const;
  void set_is_source(bool value);
  private:
  bool _internal_is_source() const;
  void _internal_set_is_source(bool value);
  public:

  // int64 step_id = 12;
  void clear_step_id();
  int64_t step_id() const;
  void set_step_id(int64_t value);
  private:
  int64_t _internal_step_id() const;
  void _internal_set_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteInstanceRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > subdiv_offset_;
    mutable std::atomic<int> _subdiv_offset_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr device_;
    ::tensorflow::TensorShapeProto* shape_;
    int32_t type_;
    int data_type_;
    int32_t group_key_;
    int32_t group_size_;
    int32_t instance_key_;
    bool is_source_;
    int64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class CompleteInstanceResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.CompleteInstanceResponse) */ {
 public:
  inline CompleteInstanceResponse() : CompleteInstanceResponse(nullptr) {}
  ~CompleteInstanceResponse() override;
  explicit PROTOBUF_CONSTEXPR CompleteInstanceResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CompleteInstanceResponse(const CompleteInstanceResponse& from);
  CompleteInstanceResponse(CompleteInstanceResponse&& from) noexcept
    : CompleteInstanceResponse() {
    *this = ::std::move(from);
  }

  inline CompleteInstanceResponse& operator=(const CompleteInstanceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CompleteInstanceResponse& operator=(CompleteInstanceResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CompleteInstanceResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CompleteInstanceResponse* internal_default_instance() {
    return reinterpret_cast<const CompleteInstanceResponse*>(
               &_CompleteInstanceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    32;

  friend void swap(CompleteInstanceResponse& a, CompleteInstanceResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CompleteInstanceResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CompleteInstanceResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CompleteInstanceResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CompleteInstanceResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CompleteInstanceResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CompleteInstanceResponse& from) {
    CompleteInstanceResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CompleteInstanceResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.CompleteInstanceResponse";
  }
  protected:
  explicit CompleteInstanceResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInstanceKeyFieldNumber = 1,
    kSourceRankFieldNumber = 2,
  };
  // int32 instance_key = 1;
  void clear_instance_key();
  int32_t instance_key() const;
  void set_instance_key(int32_t value);
  private:
  int32_t _internal_instance_key() const;
  void _internal_set_instance_key(int32_t value);
  public:

  // int32 source_rank = 2;
  void clear_source_rank();
  int32_t source_rank() const;
  void set_source_rank(int32_t value);
  private:
  int32_t _internal_source_rank() const;
  void _internal_set_source_rank(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.CompleteInstanceResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t instance_key_;
    int32_t source_rank_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class GetStepSequenceRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStepSequenceRequest) */ {
 public:
  inline GetStepSequenceRequest() : GetStepSequenceRequest(nullptr) {}
  ~GetStepSequenceRequest() override;
  explicit PROTOBUF_CONSTEXPR GetStepSequenceRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetStepSequenceRequest(const GetStepSequenceRequest& from);
  GetStepSequenceRequest(GetStepSequenceRequest&& from) noexcept
    : GetStepSequenceRequest() {
    *this = ::std::move(from);
  }

  inline GetStepSequenceRequest& operator=(const GetStepSequenceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStepSequenceRequest& operator=(GetStepSequenceRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetStepSequenceRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetStepSequenceRequest* internal_default_instance() {
    return reinterpret_cast<const GetStepSequenceRequest*>(
               &_GetStepSequenceRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    33;

  friend void swap(GetStepSequenceRequest& a, GetStepSequenceRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStepSequenceRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStepSequenceRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetStepSequenceRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetStepSequenceRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetStepSequenceRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetStepSequenceRequest& from) {
    GetStepSequenceRequest::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStepSequenceRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStepSequenceRequest";
  }
  protected:
  explicit GetStepSequenceRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphKeyFieldNumber = 1,
  };
  // repeated int64 graph_key = 1;
  int graph_key_size() const;
  private:
  int _internal_graph_key_size() const;
  public:
  void clear_graph_key();
  private:
  int64_t _internal_graph_key(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_graph_key() const;
  void _internal_add_graph_key(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_graph_key();
  public:
  int64_t graph_key(int index) const;
  void set_graph_key(int index, int64_t value);
  void add_graph_key(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      graph_key() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_graph_key();

  // @@protoc_insertion_point(class_scope:tensorflow.GetStepSequenceRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > graph_key_;
    mutable std::atomic<int> _graph_key_cached_byte_size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class StepSequence final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StepSequence) */ {
 public:
  inline StepSequence() : StepSequence(nullptr) {}
  ~StepSequence() override;
  explicit PROTOBUF_CONSTEXPR StepSequence(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StepSequence(const StepSequence& from);
  StepSequence(StepSequence&& from) noexcept
    : StepSequence() {
    *this = ::std::move(from);
  }

  inline StepSequence& operator=(const StepSequence& from) {
    CopyFrom(from);
    return *this;
  }
  inline StepSequence& operator=(StepSequence&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StepSequence& default_instance() {
    return *internal_default_instance();
  }
  static inline const StepSequence* internal_default_instance() {
    return reinterpret_cast<const StepSequence*>(
               &_StepSequence_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    34;

  friend void swap(StepSequence& a, StepSequence& b) {
    a.Swap(&b);
  }
  inline void Swap(StepSequence* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StepSequence* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StepSequence* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StepSequence>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StepSequence& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StepSequence& from) {
    StepSequence::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StepSequence* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StepSequence";
  }
  protected:
  explicit StepSequence(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kGraphKeyFieldNumber = 1,
    kNextStepIdFieldNumber = 2,
  };
  // int64 graph_key = 1;
  void clear_graph_key();
  int64_t graph_key() const;
  void set_graph_key(int64_t value);
  private:
  int64_t _internal_graph_key() const;
  void _internal_set_graph_key(int64_t value);
  public:

  // int64 next_step_id = 2;
  void clear_next_step_id();
  int64_t next_step_id() const;
  void set_next_step_id(int64_t value);
  private:
  int64_t _internal_next_step_id() const;
  void _internal_set_next_step_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.StepSequence)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t graph_key_;
    int64_t next_step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// -------------------------------------------------------------------

class GetStepSequenceResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GetStepSequenceResponse) */ {
 public:
  inline GetStepSequenceResponse() : GetStepSequenceResponse(nullptr) {}
  ~GetStepSequenceResponse() override;
  explicit PROTOBUF_CONSTEXPR GetStepSequenceResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetStepSequenceResponse(const GetStepSequenceResponse& from);
  GetStepSequenceResponse(GetStepSequenceResponse&& from) noexcept
    : GetStepSequenceResponse() {
    *this = ::std::move(from);
  }

  inline GetStepSequenceResponse& operator=(const GetStepSequenceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetStepSequenceResponse& operator=(GetStepSequenceResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetStepSequenceResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetStepSequenceResponse* internal_default_instance() {
    return reinterpret_cast<const GetStepSequenceResponse*>(
               &_GetStepSequenceResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    35;

  friend void swap(GetStepSequenceResponse& a, GetStepSequenceResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetStepSequenceResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetStepSequenceResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetStepSequenceResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetStepSequenceResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetStepSequenceResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GetStepSequenceResponse& from) {
    GetStepSequenceResponse::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetStepSequenceResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GetStepSequenceResponse";
  }
  protected:
  explicit GetStepSequenceResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStepSequenceFieldNumber = 1,
  };
  // repeated .tensorflow.StepSequence step_sequence = 1;
  int step_sequence_size() const;
  private:
  int _internal_step_sequence_size() const;
  public:
  void clear_step_sequence();
  ::tensorflow::StepSequence* mutable_step_sequence(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >*
      mutable_step_sequence();
  private:
  const ::tensorflow::StepSequence& _internal_step_sequence(int index) const;
  ::tensorflow::StepSequence* _internal_add_step_sequence();
  public:
  const ::tensorflow::StepSequence& step_sequence(int index) const;
  ::tensorflow::StepSequence* add_step_sequence();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >&
      step_sequence() const;

  // @@protoc_insertion_point(class_scope:tensorflow.GetStepSequenceResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence > step_sequence_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fworker_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetStatusRequest

// -------------------------------------------------------------------

// GetStatusResponse

// repeated .tensorflow.DeviceAttributes device_attributes = 1;
inline int GetStatusResponse::_internal_device_attributes_size() const {
  return _impl_.device_attributes_.size();
}
inline int GetStatusResponse::device_attributes_size() const {
  return _internal_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetStatusResponse.device_attributes)
  return _impl_.device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
GetStatusResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStatusResponse.device_attributes)
  return &_impl_.device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& GetStatusResponse::_internal_device_attributes(int index) const {
  return _impl_.device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& GetStatusResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStatusResponse.device_attributes)
  return _internal_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::_internal_add_device_attributes() {
  return _impl_.device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* GetStatusResponse::add_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.GetStatusResponse.device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
GetStatusResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStatusResponse.device_attributes)
  return _impl_.device_attributes_;
}

// -------------------------------------------------------------------

// CreateWorkerSessionRequest

// string session_handle = 1;
inline void CreateWorkerSessionRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& CreateWorkerSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateWorkerSessionRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.session_handle)
}
inline std::string* CreateWorkerSessionRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.session_handle)
  return _s;
}
inline const std::string& CreateWorkerSessionRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void CreateWorkerSessionRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* CreateWorkerSessionRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* CreateWorkerSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void CreateWorkerSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.session_handle)
}

// .tensorflow.ServerDef server_def = 2;
inline bool CreateWorkerSessionRequest::_internal_has_server_def() const {
  return this != internal_default_instance() && _impl_.server_def_ != nullptr;
}
inline bool CreateWorkerSessionRequest::has_server_def() const {
  return _internal_has_server_def();
}
inline const ::tensorflow::ServerDef& CreateWorkerSessionRequest::_internal_server_def() const {
  const ::tensorflow::ServerDef* p = _impl_.server_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ServerDef&>(
      ::tensorflow::_ServerDef_default_instance_);
}
inline const ::tensorflow::ServerDef& CreateWorkerSessionRequest::server_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.server_def)
  return _internal_server_def();
}
inline void CreateWorkerSessionRequest::unsafe_arena_set_allocated_server_def(
    ::tensorflow::ServerDef* server_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_def_);
  }
  _impl_.server_def_ = server_def;
  if (server_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CreateWorkerSessionRequest.server_def)
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::release_server_def() {
  
  ::tensorflow::ServerDef* temp = _impl_.server_def_;
  _impl_.server_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::unsafe_arena_release_server_def() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.server_def)
  
  ::tensorflow::ServerDef* temp = _impl_.server_def_;
  _impl_.server_def_ = nullptr;
  return temp;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::_internal_mutable_server_def() {
  
  if (_impl_.server_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ServerDef>(GetArenaForAllocation());
    _impl_.server_def_ = p;
  }
  return _impl_.server_def_;
}
inline ::tensorflow::ServerDef* CreateWorkerSessionRequest::mutable_server_def() {
  ::tensorflow::ServerDef* _msg = _internal_mutable_server_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.server_def)
  return _msg;
}
inline void CreateWorkerSessionRequest::set_allocated_server_def(::tensorflow::ServerDef* server_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_def_);
  }
  if (server_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_def));
    if (message_arena != submessage_arena) {
      server_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.server_def_ = server_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.server_def)
}

// bool isolate_session_state = 3;
inline void CreateWorkerSessionRequest::clear_isolate_session_state() {
  _impl_.isolate_session_state_ = false;
}
inline bool CreateWorkerSessionRequest::_internal_isolate_session_state() const {
  return _impl_.isolate_session_state_;
}
inline bool CreateWorkerSessionRequest::isolate_session_state() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.isolate_session_state)
  return _internal_isolate_session_state();
}
inline void CreateWorkerSessionRequest::_internal_set_isolate_session_state(bool value) {
  
  _impl_.isolate_session_state_ = value;
}
inline void CreateWorkerSessionRequest::set_isolate_session_state(bool value) {
  _internal_set_isolate_session_state(value);
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.isolate_session_state)
}

// repeated .tensorflow.DeviceAttributes cluster_device_attributes = 4;
inline int CreateWorkerSessionRequest::_internal_cluster_device_attributes_size() const {
  return _impl_.cluster_device_attributes_.size();
}
inline int CreateWorkerSessionRequest::cluster_device_attributes_size() const {
  return _internal_cluster_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* CreateWorkerSessionRequest::mutable_cluster_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return _impl_.cluster_device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CreateWorkerSessionRequest::mutable_cluster_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return &_impl_.cluster_device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CreateWorkerSessionRequest::_internal_cluster_device_attributes(int index) const {
  return _impl_.cluster_device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& CreateWorkerSessionRequest::cluster_device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return _internal_cluster_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* CreateWorkerSessionRequest::_internal_add_cluster_device_attributes() {
  return _impl_.cluster_device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* CreateWorkerSessionRequest::add_cluster_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_cluster_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CreateWorkerSessionRequest::cluster_device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.CreateWorkerSessionRequest.cluster_device_attributes)
  return _impl_.cluster_device_attributes_;
}

// string master_task = 5;
inline void CreateWorkerSessionRequest::clear_master_task() {
  _impl_.master_task_.ClearToEmpty();
}
inline const std::string& CreateWorkerSessionRequest::master_task() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.master_task)
  return _internal_master_task();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CreateWorkerSessionRequest::set_master_task(ArgT0&& arg0, ArgT... args) {
 
 _impl_.master_task_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.master_task)
}
inline std::string* CreateWorkerSessionRequest::mutable_master_task() {
  std::string* _s = _internal_mutable_master_task();
  // @@protoc_insertion_point(field_mutable:tensorflow.CreateWorkerSessionRequest.master_task)
  return _s;
}
inline const std::string& CreateWorkerSessionRequest::_internal_master_task() const {
  return _impl_.master_task_.Get();
}
inline void CreateWorkerSessionRequest::_internal_set_master_task(const std::string& value) {
  
  _impl_.master_task_.Set(value, GetArenaForAllocation());
}
inline std::string* CreateWorkerSessionRequest::_internal_mutable_master_task() {
  
  return _impl_.master_task_.Mutable(GetArenaForAllocation());
}
inline std::string* CreateWorkerSessionRequest::release_master_task() {
  // @@protoc_insertion_point(field_release:tensorflow.CreateWorkerSessionRequest.master_task)
  return _impl_.master_task_.Release();
}
inline void CreateWorkerSessionRequest::set_allocated_master_task(std::string* master_task) {
  if (master_task != nullptr) {
    
  } else {
    
  }
  _impl_.master_task_.SetAllocated(master_task, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.master_task_.IsDefault()) {
    _impl_.master_task_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CreateWorkerSessionRequest.master_task)
}

// int64 master_incarnation = 6;
inline void CreateWorkerSessionRequest::clear_master_incarnation() {
  _impl_.master_incarnation_ = int64_t{0};
}
inline int64_t CreateWorkerSessionRequest::_internal_master_incarnation() const {
  return _impl_.master_incarnation_;
}
inline int64_t CreateWorkerSessionRequest::master_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.CreateWorkerSessionRequest.master_incarnation)
  return _internal_master_incarnation();
}
inline void CreateWorkerSessionRequest::_internal_set_master_incarnation(int64_t value) {
  
  _impl_.master_incarnation_ = value;
}
inline void CreateWorkerSessionRequest::set_master_incarnation(int64_t value) {
  _internal_set_master_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.CreateWorkerSessionRequest.master_incarnation)
}

// -------------------------------------------------------------------

// CreateWorkerSessionResponse

// -------------------------------------------------------------------

// DeleteWorkerSessionRequest

// string session_handle = 1;
inline void DeleteWorkerSessionRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& DeleteWorkerSessionRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteWorkerSessionRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeleteWorkerSessionRequest.session_handle)
}
inline std::string* DeleteWorkerSessionRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return _s;
}
inline const std::string& DeleteWorkerSessionRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void DeleteWorkerSessionRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* DeleteWorkerSessionRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* DeleteWorkerSessionRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeleteWorkerSessionRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void DeleteWorkerSessionRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeleteWorkerSessionRequest.session_handle)
}

// -------------------------------------------------------------------

// DeleteWorkerSessionResponse

// -------------------------------------------------------------------

// RegisterGraphRequest

// string session_handle = 1;
inline void RegisterGraphRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& RegisterGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RegisterGraphRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.session_handle)
}
inline std::string* RegisterGraphRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.session_handle)
  return _s;
}
inline const std::string& RegisterGraphRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void RegisterGraphRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RegisterGraphRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RegisterGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void RegisterGraphRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.session_handle)
}

// bool create_worker_session_called = 6;
inline void RegisterGraphRequest::clear_create_worker_session_called() {
  _impl_.create_worker_session_called_ = false;
}
inline bool RegisterGraphRequest::_internal_create_worker_session_called() const {
  return _impl_.create_worker_session_called_;
}
inline bool RegisterGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.create_worker_session_called)
  return _internal_create_worker_session_called();
}
inline void RegisterGraphRequest::_internal_set_create_worker_session_called(bool value) {
  
  _impl_.create_worker_session_called_ = value;
}
inline void RegisterGraphRequest::set_create_worker_session_called(bool value) {
  _internal_set_create_worker_session_called(value);
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.create_worker_session_called)
}

// .tensorflow.GraphDef graph_def = 2;
inline bool RegisterGraphRequest::_internal_has_graph_def() const {
  return this != internal_default_instance() && _impl_.graph_def_ != nullptr;
}
inline bool RegisterGraphRequest::has_graph_def() const {
  return _internal_has_graph_def();
}
inline const ::tensorflow::GraphDef& RegisterGraphRequest::_internal_graph_def() const {
  const ::tensorflow::GraphDef* p = _impl_.graph_def_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphDef&>(
      ::tensorflow::_GraphDef_default_instance_);
}
inline const ::tensorflow::GraphDef& RegisterGraphRequest::graph_def() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.graph_def)
  return _internal_graph_def();
}
inline void RegisterGraphRequest::unsafe_arena_set_allocated_graph_def(
    ::tensorflow::GraphDef* graph_def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  _impl_.graph_def_ = graph_def;
  if (graph_def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphRequest.graph_def)
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::release_graph_def() {
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::unsafe_arena_release_graph_def() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.graph_def)
  
  ::tensorflow::GraphDef* temp = _impl_.graph_def_;
  _impl_.graph_def_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::_internal_mutable_graph_def() {
  
  if (_impl_.graph_def_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphDef>(GetArenaForAllocation());
    _impl_.graph_def_ = p;
  }
  return _impl_.graph_def_;
}
inline ::tensorflow::GraphDef* RegisterGraphRequest::mutable_graph_def() {
  ::tensorflow::GraphDef* _msg = _internal_mutable_graph_def();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.graph_def)
  return _msg;
}
inline void RegisterGraphRequest::set_allocated_graph_def(::tensorflow::GraphDef* graph_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_def_);
  }
  if (graph_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_def));
    if (message_arena != submessage_arena) {
      graph_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_def, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_def_ = graph_def;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.graph_def)
}

// bool has_control_flow = 3 [deprecated = true];
inline void RegisterGraphRequest::clear_has_control_flow() {
  _impl_.has_control_flow_ = false;
}
inline bool RegisterGraphRequest::_internal_has_control_flow() const {
  return _impl_.has_control_flow_;
}
inline bool RegisterGraphRequest::has_control_flow() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.has_control_flow)
  return _internal_has_control_flow();
}
inline void RegisterGraphRequest::_internal_set_has_control_flow(bool value) {
  
  _impl_.has_control_flow_ = value;
}
inline void RegisterGraphRequest::set_has_control_flow(bool value) {
  _internal_set_has_control_flow(value);
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.has_control_flow)
}

// .tensorflow.GraphOptions graph_options = 4;
inline bool RegisterGraphRequest::_internal_has_graph_options() const {
  return this != internal_default_instance() && _impl_.graph_options_ != nullptr;
}
inline bool RegisterGraphRequest::has_graph_options() const {
  return _internal_has_graph_options();
}
inline const ::tensorflow::GraphOptions& RegisterGraphRequest::_internal_graph_options() const {
  const ::tensorflow::GraphOptions* p = _impl_.graph_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::GraphOptions&>(
      ::tensorflow::_GraphOptions_default_instance_);
}
inline const ::tensorflow::GraphOptions& RegisterGraphRequest::graph_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.graph_options)
  return _internal_graph_options();
}
inline void RegisterGraphRequest::unsafe_arena_set_allocated_graph_options(
    ::tensorflow::GraphOptions* graph_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_options_);
  }
  _impl_.graph_options_ = graph_options;
  if (graph_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphRequest.graph_options)
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::release_graph_options() {
  
  ::tensorflow::GraphOptions* temp = _impl_.graph_options_;
  _impl_.graph_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::unsafe_arena_release_graph_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.graph_options)
  
  ::tensorflow::GraphOptions* temp = _impl_.graph_options_;
  _impl_.graph_options_ = nullptr;
  return temp;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::_internal_mutable_graph_options() {
  
  if (_impl_.graph_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::GraphOptions>(GetArenaForAllocation());
    _impl_.graph_options_ = p;
  }
  return _impl_.graph_options_;
}
inline ::tensorflow::GraphOptions* RegisterGraphRequest::mutable_graph_options() {
  ::tensorflow::GraphOptions* _msg = _internal_mutable_graph_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.graph_options)
  return _msg;
}
inline void RegisterGraphRequest::set_allocated_graph_options(::tensorflow::GraphOptions* graph_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.graph_options_);
  }
  if (graph_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(graph_options));
    if (message_arena != submessage_arena) {
      graph_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, graph_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.graph_options_ = graph_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.graph_options)
}

// .tensorflow.DebugOptions debug_options = 5;
inline bool RegisterGraphRequest::_internal_has_debug_options() const {
  return this != internal_default_instance() && _impl_.debug_options_ != nullptr;
}
inline bool RegisterGraphRequest::has_debug_options() const {
  return _internal_has_debug_options();
}
inline const ::tensorflow::DebugOptions& RegisterGraphRequest::_internal_debug_options() const {
  const ::tensorflow::DebugOptions* p = _impl_.debug_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DebugOptions&>(
      ::tensorflow::_DebugOptions_default_instance_);
}
inline const ::tensorflow::DebugOptions& RegisterGraphRequest::debug_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.debug_options)
  return _internal_debug_options();
}
inline void RegisterGraphRequest::unsafe_arena_set_allocated_debug_options(
    ::tensorflow::DebugOptions* debug_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  _impl_.debug_options_ = debug_options;
  if (debug_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphRequest.debug_options)
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::release_debug_options() {
  
  ::tensorflow::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::unsafe_arena_release_debug_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.debug_options)
  
  ::tensorflow::DebugOptions* temp = _impl_.debug_options_;
  _impl_.debug_options_ = nullptr;
  return temp;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::_internal_mutable_debug_options() {
  
  if (_impl_.debug_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DebugOptions>(GetArenaForAllocation());
    _impl_.debug_options_ = p;
  }
  return _impl_.debug_options_;
}
inline ::tensorflow::DebugOptions* RegisterGraphRequest::mutable_debug_options() {
  ::tensorflow::DebugOptions* _msg = _internal_mutable_debug_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.debug_options)
  return _msg;
}
inline void RegisterGraphRequest::set_allocated_debug_options(::tensorflow::DebugOptions* debug_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.debug_options_);
  }
  if (debug_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(debug_options));
    if (message_arena != submessage_arena) {
      debug_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, debug_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.debug_options_ = debug_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.debug_options)
}

// int64 collective_graph_key = 7;
inline void RegisterGraphRequest::clear_collective_graph_key() {
  _impl_.collective_graph_key_ = int64_t{0};
}
inline int64_t RegisterGraphRequest::_internal_collective_graph_key() const {
  return _impl_.collective_graph_key_;
}
inline int64_t RegisterGraphRequest::collective_graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.collective_graph_key)
  return _internal_collective_graph_key();
}
inline void RegisterGraphRequest::_internal_set_collective_graph_key(int64_t value) {
  
  _impl_.collective_graph_key_ = value;
}
inline void RegisterGraphRequest::set_collective_graph_key(int64_t value) {
  _internal_set_collective_graph_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphRequest.collective_graph_key)
}

// .tensorflow.ConfigProto config_proto = 8;
inline bool RegisterGraphRequest::_internal_has_config_proto() const {
  return this != internal_default_instance() && _impl_.config_proto_ != nullptr;
}
inline bool RegisterGraphRequest::has_config_proto() const {
  return _internal_has_config_proto();
}
inline const ::tensorflow::ConfigProto& RegisterGraphRequest::_internal_config_proto() const {
  const ::tensorflow::ConfigProto* p = _impl_.config_proto_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ConfigProto&>(
      ::tensorflow::_ConfigProto_default_instance_);
}
inline const ::tensorflow::ConfigProto& RegisterGraphRequest::config_proto() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphRequest.config_proto)
  return _internal_config_proto();
}
inline void RegisterGraphRequest::unsafe_arena_set_allocated_config_proto(
    ::tensorflow::ConfigProto* config_proto) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.config_proto_);
  }
  _impl_.config_proto_ = config_proto;
  if (config_proto) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RegisterGraphRequest.config_proto)
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::release_config_proto() {
  
  ::tensorflow::ConfigProto* temp = _impl_.config_proto_;
  _impl_.config_proto_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::unsafe_arena_release_config_proto() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphRequest.config_proto)
  
  ::tensorflow::ConfigProto* temp = _impl_.config_proto_;
  _impl_.config_proto_ = nullptr;
  return temp;
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::_internal_mutable_config_proto() {
  
  if (_impl_.config_proto_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ConfigProto>(GetArenaForAllocation());
    _impl_.config_proto_ = p;
  }
  return _impl_.config_proto_;
}
inline ::tensorflow::ConfigProto* RegisterGraphRequest::mutable_config_proto() {
  ::tensorflow::ConfigProto* _msg = _internal_mutable_config_proto();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphRequest.config_proto)
  return _msg;
}
inline void RegisterGraphRequest::set_allocated_config_proto(::tensorflow::ConfigProto* config_proto) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.config_proto_);
  }
  if (config_proto) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_proto));
    if (message_arena != submessage_arena) {
      config_proto = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config_proto, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.config_proto_ = config_proto;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphRequest.config_proto)
}

// -------------------------------------------------------------------

// RegisterGraphResponse

// string graph_handle = 1;
inline void RegisterGraphResponse::clear_graph_handle() {
  _impl_.graph_handle_.ClearToEmpty();
}
inline const std::string& RegisterGraphResponse::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RegisterGraphResponse.graph_handle)
  return _internal_graph_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RegisterGraphResponse::set_graph_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RegisterGraphResponse.graph_handle)
}
inline std::string* RegisterGraphResponse::mutable_graph_handle() {
  std::string* _s = _internal_mutable_graph_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RegisterGraphResponse.graph_handle)
  return _s;
}
inline const std::string& RegisterGraphResponse::_internal_graph_handle() const {
  return _impl_.graph_handle_.Get();
}
inline void RegisterGraphResponse::_internal_set_graph_handle(const std::string& value) {
  
  _impl_.graph_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RegisterGraphResponse::_internal_mutable_graph_handle() {
  
  return _impl_.graph_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RegisterGraphResponse::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RegisterGraphResponse.graph_handle)
  return _impl_.graph_handle_.Release();
}
inline void RegisterGraphResponse::set_allocated_graph_handle(std::string* graph_handle) {
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  _impl_.graph_handle_.SetAllocated(graph_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_handle_.IsDefault()) {
    _impl_.graph_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RegisterGraphResponse.graph_handle)
}

// -------------------------------------------------------------------

// DeregisterGraphRequest

// string session_handle = 2;
inline void DeregisterGraphRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& DeregisterGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeregisterGraphRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.session_handle)
}
inline std::string* DeregisterGraphRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeregisterGraphRequest.session_handle)
  return _s;
}
inline const std::string& DeregisterGraphRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void DeregisterGraphRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* DeregisterGraphRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* DeregisterGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeregisterGraphRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void DeregisterGraphRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeregisterGraphRequest.session_handle)
}

// bool create_worker_session_called = 3;
inline void DeregisterGraphRequest::clear_create_worker_session_called() {
  _impl_.create_worker_session_called_ = false;
}
inline bool DeregisterGraphRequest::_internal_create_worker_session_called() const {
  return _impl_.create_worker_session_called_;
}
inline bool DeregisterGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.create_worker_session_called)
  return _internal_create_worker_session_called();
}
inline void DeregisterGraphRequest::_internal_set_create_worker_session_called(bool value) {
  
  _impl_.create_worker_session_called_ = value;
}
inline void DeregisterGraphRequest::set_create_worker_session_called(bool value) {
  _internal_set_create_worker_session_called(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.create_worker_session_called)
}

// string graph_handle = 1;
inline void DeregisterGraphRequest::clear_graph_handle() {
  _impl_.graph_handle_.ClearToEmpty();
}
inline const std::string& DeregisterGraphRequest::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeregisterGraphRequest.graph_handle)
  return _internal_graph_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeregisterGraphRequest::set_graph_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeregisterGraphRequest.graph_handle)
}
inline std::string* DeregisterGraphRequest::mutable_graph_handle() {
  std::string* _s = _internal_mutable_graph_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeregisterGraphRequest.graph_handle)
  return _s;
}
inline const std::string& DeregisterGraphRequest::_internal_graph_handle() const {
  return _impl_.graph_handle_.Get();
}
inline void DeregisterGraphRequest::_internal_set_graph_handle(const std::string& value) {
  
  _impl_.graph_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* DeregisterGraphRequest::_internal_mutable_graph_handle() {
  
  return _impl_.graph_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* DeregisterGraphRequest::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.DeregisterGraphRequest.graph_handle)
  return _impl_.graph_handle_.Release();
}
inline void DeregisterGraphRequest::set_allocated_graph_handle(std::string* graph_handle) {
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  _impl_.graph_handle_.SetAllocated(graph_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_handle_.IsDefault()) {
    _impl_.graph_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeregisterGraphRequest.graph_handle)
}

// -------------------------------------------------------------------

// DeregisterGraphResponse

// -------------------------------------------------------------------

// CleanupAllRequest

// repeated string container = 1;
inline int CleanupAllRequest::_internal_container_size() const {
  return _impl_.container_.size();
}
inline int CleanupAllRequest::container_size() const {
  return _internal_container_size();
}
inline void CleanupAllRequest::clear_container() {
  _impl_.container_.Clear();
}
inline std::string* CleanupAllRequest::add_container() {
  std::string* _s = _internal_add_container();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.CleanupAllRequest.container)
  return _s;
}
inline const std::string& CleanupAllRequest::_internal_container(int index) const {
  return _impl_.container_.Get(index);
}
inline const std::string& CleanupAllRequest::container(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CleanupAllRequest.container)
  return _internal_container(index);
}
inline std::string* CleanupAllRequest::mutable_container(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CleanupAllRequest.container)
  return _impl_.container_.Mutable(index);
}
inline void CleanupAllRequest::set_container(int index, const std::string& value) {
  _impl_.container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::set_container(int index, std::string&& value) {
  _impl_.container_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::set_container(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.container_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::set_container(int index, const char* value, size_t size) {
  _impl_.container_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.CleanupAllRequest.container)
}
inline std::string* CleanupAllRequest::_internal_add_container() {
  return _impl_.container_.Add();
}
inline void CleanupAllRequest::add_container(const std::string& value) {
  _impl_.container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(std::string&& value) {
  _impl_.container_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.container_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.CleanupAllRequest.container)
}
inline void CleanupAllRequest::add_container(const char* value, size_t size) {
  _impl_.container_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.CleanupAllRequest.container)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
CleanupAllRequest::container() const {
  // @@protoc_insertion_point(field_list:tensorflow.CleanupAllRequest.container)
  return _impl_.container_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
CleanupAllRequest::mutable_container() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CleanupAllRequest.container)
  return &_impl_.container_;
}

// -------------------------------------------------------------------

// CleanupAllResponse

// -------------------------------------------------------------------

// ExecutorOpts

// bool record_costs = 1;
inline void ExecutorOpts::clear_record_costs() {
  _impl_.record_costs_ = false;
}
inline bool ExecutorOpts::_internal_record_costs() const {
  return _impl_.record_costs_;
}
inline bool ExecutorOpts::record_costs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_costs)
  return _internal_record_costs();
}
inline void ExecutorOpts::_internal_set_record_costs(bool value) {
  
  _impl_.record_costs_ = value;
}
inline void ExecutorOpts::set_record_costs(bool value) {
  _internal_set_record_costs(value);
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_costs)
}

// bool record_timeline = 3;
inline void ExecutorOpts::clear_record_timeline() {
  _impl_.record_timeline_ = false;
}
inline bool ExecutorOpts::_internal_record_timeline() const {
  return _impl_.record_timeline_;
}
inline bool ExecutorOpts::record_timeline() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_timeline)
  return _internal_record_timeline();
}
inline void ExecutorOpts::_internal_set_record_timeline(bool value) {
  
  _impl_.record_timeline_ = value;
}
inline void ExecutorOpts::set_record_timeline(bool value) {
  _internal_set_record_timeline(value);
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_timeline)
}

// bool record_partition_graphs = 4;
inline void ExecutorOpts::clear_record_partition_graphs() {
  _impl_.record_partition_graphs_ = false;
}
inline bool ExecutorOpts::_internal_record_partition_graphs() const {
  return _impl_.record_partition_graphs_;
}
inline bool ExecutorOpts::record_partition_graphs() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.record_partition_graphs)
  return _internal_record_partition_graphs();
}
inline void ExecutorOpts::_internal_set_record_partition_graphs(bool value) {
  
  _impl_.record_partition_graphs_ = value;
}
inline void ExecutorOpts::set_record_partition_graphs(bool value) {
  _internal_set_record_partition_graphs(value);
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.record_partition_graphs)
}

// bool report_tensor_allocations_upon_oom = 5;
inline void ExecutorOpts::clear_report_tensor_allocations_upon_oom() {
  _impl_.report_tensor_allocations_upon_oom_ = false;
}
inline bool ExecutorOpts::_internal_report_tensor_allocations_upon_oom() const {
  return _impl_.report_tensor_allocations_upon_oom_;
}
inline bool ExecutorOpts::report_tensor_allocations_upon_oom() const {
  // @@protoc_insertion_point(field_get:tensorflow.ExecutorOpts.report_tensor_allocations_upon_oom)
  return _internal_report_tensor_allocations_upon_oom();
}
inline void ExecutorOpts::_internal_set_report_tensor_allocations_upon_oom(bool value) {
  
  _impl_.report_tensor_allocations_upon_oom_ = value;
}
inline void ExecutorOpts::set_report_tensor_allocations_upon_oom(bool value) {
  _internal_set_report_tensor_allocations_upon_oom(value);
  // @@protoc_insertion_point(field_set:tensorflow.ExecutorOpts.report_tensor_allocations_upon_oom)
}

// -------------------------------------------------------------------

// RunGraphRequest

// string session_handle = 8;
inline void RunGraphRequest::clear_session_handle() {
  _impl_.session_handle_.ClearToEmpty();
}
inline const std::string& RunGraphRequest::session_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.session_handle)
  return _internal_session_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunGraphRequest::set_session_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.session_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.session_handle)
}
inline std::string* RunGraphRequest::mutable_session_handle() {
  std::string* _s = _internal_mutable_session_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.session_handle)
  return _s;
}
inline const std::string& RunGraphRequest::_internal_session_handle() const {
  return _impl_.session_handle_.Get();
}
inline void RunGraphRequest::_internal_set_session_handle(const std::string& value) {
  
  _impl_.session_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RunGraphRequest::_internal_mutable_session_handle() {
  
  return _impl_.session_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RunGraphRequest::release_session_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.session_handle)
  return _impl_.session_handle_.Release();
}
inline void RunGraphRequest::set_allocated_session_handle(std::string* session_handle) {
  if (session_handle != nullptr) {
    
  } else {
    
  }
  _impl_.session_handle_.SetAllocated(session_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.session_handle_.IsDefault()) {
    _impl_.session_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.session_handle)
}

// bool create_worker_session_called = 10;
inline void RunGraphRequest::clear_create_worker_session_called() {
  _impl_.create_worker_session_called_ = false;
}
inline bool RunGraphRequest::_internal_create_worker_session_called() const {
  return _impl_.create_worker_session_called_;
}
inline bool RunGraphRequest::create_worker_session_called() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.create_worker_session_called)
  return _internal_create_worker_session_called();
}
inline void RunGraphRequest::_internal_set_create_worker_session_called(bool value) {
  
  _impl_.create_worker_session_called_ = value;
}
inline void RunGraphRequest::set_create_worker_session_called(bool value) {
  _internal_set_create_worker_session_called(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.create_worker_session_called)
}

// string graph_handle = 1;
inline void RunGraphRequest::clear_graph_handle() {
  _impl_.graph_handle_.ClearToEmpty();
}
inline const std::string& RunGraphRequest::graph_handle() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.graph_handle)
  return _internal_graph_handle();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunGraphRequest::set_graph_handle(ArgT0&& arg0, ArgT... args) {
 
 _impl_.graph_handle_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.graph_handle)
}
inline std::string* RunGraphRequest::mutable_graph_handle() {
  std::string* _s = _internal_mutable_graph_handle();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.graph_handle)
  return _s;
}
inline const std::string& RunGraphRequest::_internal_graph_handle() const {
  return _impl_.graph_handle_.Get();
}
inline void RunGraphRequest::_internal_set_graph_handle(const std::string& value) {
  
  _impl_.graph_handle_.Set(value, GetArenaForAllocation());
}
inline std::string* RunGraphRequest::_internal_mutable_graph_handle() {
  
  return _impl_.graph_handle_.Mutable(GetArenaForAllocation());
}
inline std::string* RunGraphRequest::release_graph_handle() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.graph_handle)
  return _impl_.graph_handle_.Release();
}
inline void RunGraphRequest::set_allocated_graph_handle(std::string* graph_handle) {
  if (graph_handle != nullptr) {
    
  } else {
    
  }
  _impl_.graph_handle_.SetAllocated(graph_handle, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.graph_handle_.IsDefault()) {
    _impl_.graph_handle_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.graph_handle)
}

// int64 step_id = 2;
inline void RunGraphRequest::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t RunGraphRequest::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t RunGraphRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.step_id)
  return _internal_step_id();
}
inline void RunGraphRequest::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void RunGraphRequest::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.step_id)
}

// .tensorflow.ExecutorOpts exec_opts = 5;
inline bool RunGraphRequest::_internal_has_exec_opts() const {
  return this != internal_default_instance() && _impl_.exec_opts_ != nullptr;
}
inline bool RunGraphRequest::has_exec_opts() const {
  return _internal_has_exec_opts();
}
inline void RunGraphRequest::clear_exec_opts() {
  if (GetArenaForAllocation() == nullptr && _impl_.exec_opts_ != nullptr) {
    delete _impl_.exec_opts_;
  }
  _impl_.exec_opts_ = nullptr;
}
inline const ::tensorflow::ExecutorOpts& RunGraphRequest::_internal_exec_opts() const {
  const ::tensorflow::ExecutorOpts* p = _impl_.exec_opts_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ExecutorOpts&>(
      ::tensorflow::_ExecutorOpts_default_instance_);
}
inline const ::tensorflow::ExecutorOpts& RunGraphRequest::exec_opts() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.exec_opts)
  return _internal_exec_opts();
}
inline void RunGraphRequest::unsafe_arena_set_allocated_exec_opts(
    ::tensorflow::ExecutorOpts* exec_opts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.exec_opts_);
  }
  _impl_.exec_opts_ = exec_opts;
  if (exec_opts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphRequest.exec_opts)
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::release_exec_opts() {
  
  ::tensorflow::ExecutorOpts* temp = _impl_.exec_opts_;
  _impl_.exec_opts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::unsafe_arena_release_exec_opts() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphRequest.exec_opts)
  
  ::tensorflow::ExecutorOpts* temp = _impl_.exec_opts_;
  _impl_.exec_opts_ = nullptr;
  return temp;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::_internal_mutable_exec_opts() {
  
  if (_impl_.exec_opts_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ExecutorOpts>(GetArenaForAllocation());
    _impl_.exec_opts_ = p;
  }
  return _impl_.exec_opts_;
}
inline ::tensorflow::ExecutorOpts* RunGraphRequest::mutable_exec_opts() {
  ::tensorflow::ExecutorOpts* _msg = _internal_mutable_exec_opts();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.exec_opts)
  return _msg;
}
inline void RunGraphRequest::set_allocated_exec_opts(::tensorflow::ExecutorOpts* exec_opts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.exec_opts_;
  }
  if (exec_opts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(exec_opts);
    if (message_arena != submessage_arena) {
      exec_opts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, exec_opts, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.exec_opts_ = exec_opts;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphRequest.exec_opts)
}

// repeated .tensorflow.NamedTensorProto send = 3;
inline int RunGraphRequest::_internal_send_size() const {
  return _impl_.send_.size();
}
inline int RunGraphRequest::send_size() const {
  return _internal_send_size();
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::mutable_send(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.send)
  return _impl_.send_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunGraphRequest::mutable_send() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphRequest.send)
  return &_impl_.send_;
}
inline const ::tensorflow::NamedTensorProto& RunGraphRequest::_internal_send(int index) const {
  return _impl_.send_.Get(index);
}
inline const ::tensorflow::NamedTensorProto& RunGraphRequest::send(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.send)
  return _internal_send(index);
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::_internal_add_send() {
  return _impl_.send_.Add();
}
inline ::tensorflow::NamedTensorProto* RunGraphRequest::add_send() {
  ::tensorflow::NamedTensorProto* _add = _internal_add_send();
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.send)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunGraphRequest::send() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphRequest.send)
  return _impl_.send_;
}

// repeated string recv_key = 4;
inline int RunGraphRequest::_internal_recv_key_size() const {
  return _impl_.recv_key_.size();
}
inline int RunGraphRequest::recv_key_size() const {
  return _internal_recv_key_size();
}
inline void RunGraphRequest::clear_recv_key() {
  _impl_.recv_key_.Clear();
}
inline std::string* RunGraphRequest::add_recv_key() {
  std::string* _s = _internal_add_recv_key();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RunGraphRequest.recv_key)
  return _s;
}
inline const std::string& RunGraphRequest::_internal_recv_key(int index) const {
  return _impl_.recv_key_.Get(index);
}
inline const std::string& RunGraphRequest::recv_key(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.recv_key)
  return _internal_recv_key(index);
}
inline std::string* RunGraphRequest::mutable_recv_key(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphRequest.recv_key)
  return _impl_.recv_key_.Mutable(index);
}
inline void RunGraphRequest::set_recv_key(int index, const std::string& value) {
  _impl_.recv_key_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::set_recv_key(int index, std::string&& value) {
  _impl_.recv_key_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::set_recv_key(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.recv_key_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::set_recv_key(int index, const char* value, size_t size) {
  _impl_.recv_key_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RunGraphRequest.recv_key)
}
inline std::string* RunGraphRequest::_internal_add_recv_key() {
  return _impl_.recv_key_.Add();
}
inline void RunGraphRequest::add_recv_key(const std::string& value) {
  _impl_.recv_key_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(std::string&& value) {
  _impl_.recv_key_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.recv_key_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RunGraphRequest.recv_key)
}
inline void RunGraphRequest::add_recv_key(const char* value, size_t size) {
  _impl_.recv_key_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RunGraphRequest.recv_key)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RunGraphRequest::recv_key() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphRequest.recv_key)
  return _impl_.recv_key_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RunGraphRequest::mutable_recv_key() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphRequest.recv_key)
  return &_impl_.recv_key_;
}

// bool is_partial = 6;
inline void RunGraphRequest::clear_is_partial() {
  _impl_.is_partial_ = false;
}
inline bool RunGraphRequest::_internal_is_partial() const {
  return _impl_.is_partial_;
}
inline bool RunGraphRequest::is_partial() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.is_partial)
  return _internal_is_partial();
}
inline void RunGraphRequest::_internal_set_is_partial(bool value) {
  
  _impl_.is_partial_ = value;
}
inline void RunGraphRequest::set_is_partial(bool value) {
  _internal_set_is_partial(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.is_partial)
}

// bool is_last_partial_run = 7;
inline void RunGraphRequest::clear_is_last_partial_run() {
  _impl_.is_last_partial_run_ = false;
}
inline bool RunGraphRequest::_internal_is_last_partial_run() const {
  return _impl_.is_last_partial_run_;
}
inline bool RunGraphRequest::is_last_partial_run() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.is_last_partial_run)
  return _internal_is_last_partial_run();
}
inline void RunGraphRequest::_internal_set_is_last_partial_run(bool value) {
  
  _impl_.is_last_partial_run_ = value;
}
inline void RunGraphRequest::set_is_last_partial_run(bool value) {
  _internal_set_is_last_partial_run(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.is_last_partial_run)
}

// bool store_errors_in_response_body = 9;
inline void RunGraphRequest::clear_store_errors_in_response_body() {
  _impl_.store_errors_in_response_body_ = false;
}
inline bool RunGraphRequest::_internal_store_errors_in_response_body() const {
  return _impl_.store_errors_in_response_body_;
}
inline bool RunGraphRequest::store_errors_in_response_body() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.store_errors_in_response_body)
  return _internal_store_errors_in_response_body();
}
inline void RunGraphRequest::_internal_set_store_errors_in_response_body(bool value) {
  
  _impl_.store_errors_in_response_body_ = value;
}
inline void RunGraphRequest::set_store_errors_in_response_body(bool value) {
  _internal_set_store_errors_in_response_body(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.store_errors_in_response_body)
}

// int64 request_id = 11;
inline void RunGraphRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t RunGraphRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t RunGraphRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphRequest.request_id)
  return _internal_request_id();
}
inline void RunGraphRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void RunGraphRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphRequest.request_id)
}

// -------------------------------------------------------------------

// RunGraphResponse

// repeated .tensorflow.NamedTensorProto recv = 1;
inline int RunGraphResponse::_internal_recv_size() const {
  return _impl_.recv_.size();
}
inline int RunGraphResponse::recv_size() const {
  return _internal_recv_size();
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::mutable_recv(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.recv)
  return _impl_.recv_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >*
RunGraphResponse::mutable_recv() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphResponse.recv)
  return &_impl_.recv_;
}
inline const ::tensorflow::NamedTensorProto& RunGraphResponse::_internal_recv(int index) const {
  return _impl_.recv_.Get(index);
}
inline const ::tensorflow::NamedTensorProto& RunGraphResponse::recv(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.recv)
  return _internal_recv(index);
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::_internal_add_recv() {
  return _impl_.recv_.Add();
}
inline ::tensorflow::NamedTensorProto* RunGraphResponse::add_recv() {
  ::tensorflow::NamedTensorProto* _add = _internal_add_recv();
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphResponse.recv)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::NamedTensorProto >&
RunGraphResponse::recv() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphResponse.recv)
  return _impl_.recv_;
}

// .tensorflow.StepStats step_stats = 2;
inline bool RunGraphResponse::_internal_has_step_stats() const {
  return this != internal_default_instance() && _impl_.step_stats_ != nullptr;
}
inline bool RunGraphResponse::has_step_stats() const {
  return _internal_has_step_stats();
}
inline const ::tensorflow::StepStats& RunGraphResponse::_internal_step_stats() const {
  const ::tensorflow::StepStats* p = _impl_.step_stats_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StepStats&>(
      ::tensorflow::_StepStats_default_instance_);
}
inline const ::tensorflow::StepStats& RunGraphResponse::step_stats() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.step_stats)
  return _internal_step_stats();
}
inline void RunGraphResponse::unsafe_arena_set_allocated_step_stats(
    ::tensorflow::StepStats* step_stats) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.step_stats_);
  }
  _impl_.step_stats_ = step_stats;
  if (step_stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphResponse.step_stats)
}
inline ::tensorflow::StepStats* RunGraphResponse::release_step_stats() {
  
  ::tensorflow::StepStats* temp = _impl_.step_stats_;
  _impl_.step_stats_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StepStats* RunGraphResponse::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.step_stats)
  
  ::tensorflow::StepStats* temp = _impl_.step_stats_;
  _impl_.step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* RunGraphResponse::_internal_mutable_step_stats() {
  
  if (_impl_.step_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaForAllocation());
    _impl_.step_stats_ = p;
  }
  return _impl_.step_stats_;
}
inline ::tensorflow::StepStats* RunGraphResponse::mutable_step_stats() {
  ::tensorflow::StepStats* _msg = _internal_mutable_step_stats();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.step_stats)
  return _msg;
}
inline void RunGraphResponse::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.step_stats_);
  }
  if (step_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats));
    if (message_arena != submessage_arena) {
      step_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.step_stats)
}

// .tensorflow.CostGraphDef cost_graph = 3;
inline bool RunGraphResponse::_internal_has_cost_graph() const {
  return this != internal_default_instance() && _impl_.cost_graph_ != nullptr;
}
inline bool RunGraphResponse::has_cost_graph() const {
  return _internal_has_cost_graph();
}
inline const ::tensorflow::CostGraphDef& RunGraphResponse::_internal_cost_graph() const {
  const ::tensorflow::CostGraphDef* p = _impl_.cost_graph_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::CostGraphDef&>(
      ::tensorflow::_CostGraphDef_default_instance_);
}
inline const ::tensorflow::CostGraphDef& RunGraphResponse::cost_graph() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.cost_graph)
  return _internal_cost_graph();
}
inline void RunGraphResponse::unsafe_arena_set_allocated_cost_graph(
    ::tensorflow::CostGraphDef* cost_graph) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cost_graph_);
  }
  _impl_.cost_graph_ = cost_graph;
  if (cost_graph) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RunGraphResponse.cost_graph)
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::release_cost_graph() {
  
  ::tensorflow::CostGraphDef* temp = _impl_.cost_graph_;
  _impl_.cost_graph_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::unsafe_arena_release_cost_graph() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.cost_graph)
  
  ::tensorflow::CostGraphDef* temp = _impl_.cost_graph_;
  _impl_.cost_graph_ = nullptr;
  return temp;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::_internal_mutable_cost_graph() {
  
  if (_impl_.cost_graph_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::CostGraphDef>(GetArenaForAllocation());
    _impl_.cost_graph_ = p;
  }
  return _impl_.cost_graph_;
}
inline ::tensorflow::CostGraphDef* RunGraphResponse::mutable_cost_graph() {
  ::tensorflow::CostGraphDef* _msg = _internal_mutable_cost_graph();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.cost_graph)
  return _msg;
}
inline void RunGraphResponse::set_allocated_cost_graph(::tensorflow::CostGraphDef* cost_graph) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.cost_graph_);
  }
  if (cost_graph) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(cost_graph));
    if (message_arena != submessage_arena) {
      cost_graph = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, cost_graph, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.cost_graph_ = cost_graph;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.cost_graph)
}

// repeated .tensorflow.GraphDef partition_graph = 4;
inline int RunGraphResponse::_internal_partition_graph_size() const {
  return _impl_.partition_graph_.size();
}
inline int RunGraphResponse::partition_graph_size() const {
  return _internal_partition_graph_size();
}
inline ::tensorflow::GraphDef* RunGraphResponse::mutable_partition_graph(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.partition_graph)
  return _impl_.partition_graph_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >*
RunGraphResponse::mutable_partition_graph() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RunGraphResponse.partition_graph)
  return &_impl_.partition_graph_;
}
inline const ::tensorflow::GraphDef& RunGraphResponse::_internal_partition_graph(int index) const {
  return _impl_.partition_graph_.Get(index);
}
inline const ::tensorflow::GraphDef& RunGraphResponse::partition_graph(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.partition_graph)
  return _internal_partition_graph(index);
}
inline ::tensorflow::GraphDef* RunGraphResponse::_internal_add_partition_graph() {
  return _impl_.partition_graph_.Add();
}
inline ::tensorflow::GraphDef* RunGraphResponse::add_partition_graph() {
  ::tensorflow::GraphDef* _add = _internal_add_partition_graph();
  // @@protoc_insertion_point(field_add:tensorflow.RunGraphResponse.partition_graph)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::GraphDef >&
RunGraphResponse::partition_graph() const {
  // @@protoc_insertion_point(field_list:tensorflow.RunGraphResponse.partition_graph)
  return _impl_.partition_graph_;
}

// .tensorflow.error.Code status_code = 5;
inline void RunGraphResponse::clear_status_code() {
  _impl_.status_code_ = 0;
}
inline ::tensorflow::error::Code RunGraphResponse::_internal_status_code() const {
  return static_cast< ::tensorflow::error::Code >(_impl_.status_code_);
}
inline ::tensorflow::error::Code RunGraphResponse::status_code() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.status_code)
  return _internal_status_code();
}
inline void RunGraphResponse::_internal_set_status_code(::tensorflow::error::Code value) {
  
  _impl_.status_code_ = value;
}
inline void RunGraphResponse::set_status_code(::tensorflow::error::Code value) {
  _internal_set_status_code(value);
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphResponse.status_code)
}

// string status_error_message = 6;
inline void RunGraphResponse::clear_status_error_message() {
  _impl_.status_error_message_.ClearToEmpty();
}
inline const std::string& RunGraphResponse::status_error_message() const {
  // @@protoc_insertion_point(field_get:tensorflow.RunGraphResponse.status_error_message)
  return _internal_status_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RunGraphResponse::set_status_error_message(ArgT0&& arg0, ArgT... args) {
 
 _impl_.status_error_message_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RunGraphResponse.status_error_message)
}
inline std::string* RunGraphResponse::mutable_status_error_message() {
  std::string* _s = _internal_mutable_status_error_message();
  // @@protoc_insertion_point(field_mutable:tensorflow.RunGraphResponse.status_error_message)
  return _s;
}
inline const std::string& RunGraphResponse::_internal_status_error_message() const {
  return _impl_.status_error_message_.Get();
}
inline void RunGraphResponse::_internal_set_status_error_message(const std::string& value) {
  
  _impl_.status_error_message_.Set(value, GetArenaForAllocation());
}
inline std::string* RunGraphResponse::_internal_mutable_status_error_message() {
  
  return _impl_.status_error_message_.Mutable(GetArenaForAllocation());
}
inline std::string* RunGraphResponse::release_status_error_message() {
  // @@protoc_insertion_point(field_release:tensorflow.RunGraphResponse.status_error_message)
  return _impl_.status_error_message_.Release();
}
inline void RunGraphResponse::set_allocated_status_error_message(std::string* status_error_message) {
  if (status_error_message != nullptr) {
    
  } else {
    
  }
  _impl_.status_error_message_.SetAllocated(status_error_message, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.status_error_message_.IsDefault()) {
    _impl_.status_error_message_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RunGraphResponse.status_error_message)
}

// -------------------------------------------------------------------

// CleanupGraphRequest

// int64 step_id = 1;
inline void CleanupGraphRequest::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t CleanupGraphRequest::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t CleanupGraphRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CleanupGraphRequest.step_id)
  return _internal_step_id();
}
inline void CleanupGraphRequest::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void CleanupGraphRequest::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.CleanupGraphRequest.step_id)
}

// -------------------------------------------------------------------

// CleanupGraphResponse

// -------------------------------------------------------------------

// RecvTensorRequest

// int64 step_id = 1;
inline void RecvTensorRequest::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t RecvTensorRequest::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t RecvTensorRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.step_id)
  return _internal_step_id();
}
inline void RecvTensorRequest::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void RecvTensorRequest::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.step_id)
}

// string rendezvous_key = 2;
inline void RecvTensorRequest::clear_rendezvous_key() {
  _impl_.rendezvous_key_.ClearToEmpty();
}
inline const std::string& RecvTensorRequest::rendezvous_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.rendezvous_key)
  return _internal_rendezvous_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RecvTensorRequest::set_rendezvous_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.rendezvous_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.rendezvous_key)
}
inline std::string* RecvTensorRequest::mutable_rendezvous_key() {
  std::string* _s = _internal_mutable_rendezvous_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.rendezvous_key)
  return _s;
}
inline const std::string& RecvTensorRequest::_internal_rendezvous_key() const {
  return _impl_.rendezvous_key_.Get();
}
inline void RecvTensorRequest::_internal_set_rendezvous_key(const std::string& value) {
  
  _impl_.rendezvous_key_.Set(value, GetArenaForAllocation());
}
inline std::string* RecvTensorRequest::_internal_mutable_rendezvous_key() {
  
  return _impl_.rendezvous_key_.Mutable(GetArenaForAllocation());
}
inline std::string* RecvTensorRequest::release_rendezvous_key() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.rendezvous_key)
  return _impl_.rendezvous_key_.Release();
}
inline void RecvTensorRequest::set_allocated_rendezvous_key(std::string* rendezvous_key) {
  if (rendezvous_key != nullptr) {
    
  } else {
    
  }
  _impl_.rendezvous_key_.SetAllocated(rendezvous_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.rendezvous_key_.IsDefault()) {
    _impl_.rendezvous_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.rendezvous_key)
}

// bool dma_ok = 3;
inline void RecvTensorRequest::clear_dma_ok() {
  _impl_.dma_ok_ = false;
}
inline bool RecvTensorRequest::_internal_dma_ok() const {
  return _impl_.dma_ok_;
}
inline bool RecvTensorRequest::dma_ok() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.dma_ok)
  return _internal_dma_ok();
}
inline void RecvTensorRequest::_internal_set_dma_ok(bool value) {
  
  _impl_.dma_ok_ = value;
}
inline void RecvTensorRequest::set_dma_ok(bool value) {
  _internal_set_dma_ok(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.dma_ok)
}

// .tensorflow.DeviceLocality client_locality = 4;
inline bool RecvTensorRequest::_internal_has_client_locality() const {
  return this != internal_default_instance() && _impl_.client_locality_ != nullptr;
}
inline bool RecvTensorRequest::has_client_locality() const {
  return _internal_has_client_locality();
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::_internal_client_locality() const {
  const ::tensorflow::DeviceLocality* p = _impl_.client_locality_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceLocality&>(
      ::tensorflow::_DeviceLocality_default_instance_);
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::client_locality() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.client_locality)
  return _internal_client_locality();
}
inline void RecvTensorRequest::unsafe_arena_set_allocated_client_locality(
    ::tensorflow::DeviceLocality* client_locality) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.client_locality_);
  }
  _impl_.client_locality_ = client_locality;
  if (client_locality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorRequest.client_locality)
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::release_client_locality() {
  
  ::tensorflow::DeviceLocality* temp = _impl_.client_locality_;
  _impl_.client_locality_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::unsafe_arena_release_client_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = _impl_.client_locality_;
  _impl_.client_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::_internal_mutable_client_locality() {
  
  if (_impl_.client_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaForAllocation());
    _impl_.client_locality_ = p;
  }
  return _impl_.client_locality_;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::mutable_client_locality() {
  ::tensorflow::DeviceLocality* _msg = _internal_mutable_client_locality();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.client_locality)
  return _msg;
}
inline void RecvTensorRequest::set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.client_locality_);
  }
  if (client_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(client_locality));
    if (message_arena != submessage_arena) {
      client_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, client_locality, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.client_locality_ = client_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.client_locality)
}

// .tensorflow.DeviceLocality server_locality = 5;
inline bool RecvTensorRequest::_internal_has_server_locality() const {
  return this != internal_default_instance() && _impl_.server_locality_ != nullptr;
}
inline bool RecvTensorRequest::has_server_locality() const {
  return _internal_has_server_locality();
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::_internal_server_locality() const {
  const ::tensorflow::DeviceLocality* p = _impl_.server_locality_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceLocality&>(
      ::tensorflow::_DeviceLocality_default_instance_);
}
inline const ::tensorflow::DeviceLocality& RecvTensorRequest::server_locality() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.server_locality)
  return _internal_server_locality();
}
inline void RecvTensorRequest::unsafe_arena_set_allocated_server_locality(
    ::tensorflow::DeviceLocality* server_locality) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_locality_);
  }
  _impl_.server_locality_ = server_locality;
  if (server_locality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorRequest.server_locality)
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::release_server_locality() {
  
  ::tensorflow::DeviceLocality* temp = _impl_.server_locality_;
  _impl_.server_locality_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::unsafe_arena_release_server_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = _impl_.server_locality_;
  _impl_.server_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::_internal_mutable_server_locality() {
  
  if (_impl_.server_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaForAllocation());
    _impl_.server_locality_ = p;
  }
  return _impl_.server_locality_;
}
inline ::tensorflow::DeviceLocality* RecvTensorRequest::mutable_server_locality() {
  ::tensorflow::DeviceLocality* _msg = _internal_mutable_server_locality();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.server_locality)
  return _msg;
}
inline void RecvTensorRequest::set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_locality_);
  }
  if (server_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_locality));
    if (message_arena != submessage_arena) {
      server_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_locality, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.server_locality_ = server_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.server_locality)
}

// .google.protobuf.Any transport_options = 6;
inline bool RecvTensorRequest::_internal_has_transport_options() const {
  return this != internal_default_instance() && _impl_.transport_options_ != nullptr;
}
inline bool RecvTensorRequest::has_transport_options() const {
  return _internal_has_transport_options();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvTensorRequest::_internal_transport_options() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.transport_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvTensorRequest::transport_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.transport_options)
  return _internal_transport_options();
}
inline void RecvTensorRequest::unsafe_arena_set_allocated_transport_options(
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  _impl_.transport_options_ = transport_options;
  if (transport_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorRequest.transport_options)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::release_transport_options() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorRequest.transport_options)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::_internal_mutable_transport_options() {
  
  if (_impl_.transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.transport_options_ = p;
  }
  return _impl_.transport_options_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorRequest::mutable_transport_options() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_transport_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorRequest.transport_options)
  return _msg;
}
inline void RecvTensorRequest::set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options));
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorRequest.transport_options)
}

// int64 request_id = 7;
inline void RecvTensorRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t RecvTensorRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t RecvTensorRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorRequest.request_id)
  return _internal_request_id();
}
inline void RecvTensorRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void RecvTensorRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorRequest.request_id)
}

// -------------------------------------------------------------------

// RecvTensorResponse

// .tensorflow.TensorProto tensor = 1;
inline bool RecvTensorResponse::_internal_has_tensor() const {
  return this != internal_default_instance() && _impl_.tensor_ != nullptr;
}
inline bool RecvTensorResponse::has_tensor() const {
  return _internal_has_tensor();
}
inline const ::tensorflow::TensorProto& RecvTensorResponse::_internal_tensor() const {
  const ::tensorflow::TensorProto* p = _impl_.tensor_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& RecvTensorResponse::tensor() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.tensor)
  return _internal_tensor();
}
inline void RecvTensorResponse::unsafe_arena_set_allocated_tensor(
    ::tensorflow::TensorProto* tensor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  _impl_.tensor_ = tensor;
  if (tensor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorResponse.tensor)
}
inline ::tensorflow::TensorProto* RecvTensorResponse::release_tensor() {
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::unsafe_arena_release_tensor() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorResponse.tensor)
  
  ::tensorflow::TensorProto* temp = _impl_.tensor_;
  _impl_.tensor_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::_internal_mutable_tensor() {
  
  if (_impl_.tensor_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.tensor_ = p;
  }
  return _impl_.tensor_;
}
inline ::tensorflow::TensorProto* RecvTensorResponse::mutable_tensor() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorResponse.tensor)
  return _msg;
}
inline void RecvTensorResponse::set_allocated_tensor(::tensorflow::TensorProto* tensor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tensor_);
  }
  if (tensor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tensor));
    if (message_arena != submessage_arena) {
      tensor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tensor, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tensor_ = tensor;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorResponse.tensor)
}

// bool is_dead = 2;
inline void RecvTensorResponse::clear_is_dead() {
  _impl_.is_dead_ = false;
}
inline bool RecvTensorResponse::_internal_is_dead() const {
  return _impl_.is_dead_;
}
inline bool RecvTensorResponse::is_dead() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.is_dead)
  return _internal_is_dead();
}
inline void RecvTensorResponse::_internal_set_is_dead(bool value) {
  
  _impl_.is_dead_ = value;
}
inline void RecvTensorResponse::set_is_dead(bool value) {
  _internal_set_is_dead(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.is_dead)
}

// int64 send_start_micros = 3;
inline void RecvTensorResponse::clear_send_start_micros() {
  _impl_.send_start_micros_ = int64_t{0};
}
inline int64_t RecvTensorResponse::_internal_send_start_micros() const {
  return _impl_.send_start_micros_;
}
inline int64_t RecvTensorResponse::send_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.send_start_micros)
  return _internal_send_start_micros();
}
inline void RecvTensorResponse::_internal_set_send_start_micros(int64_t value) {
  
  _impl_.send_start_micros_ = value;
}
inline void RecvTensorResponse::set_send_start_micros(int64_t value) {
  _internal_set_send_start_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.send_start_micros)
}

// .google.protobuf.Any transport_options = 4;
inline bool RecvTensorResponse::_internal_has_transport_options() const {
  return this != internal_default_instance() && _impl_.transport_options_ != nullptr;
}
inline bool RecvTensorResponse::has_transport_options() const {
  return _internal_has_transport_options();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvTensorResponse::_internal_transport_options() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.transport_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvTensorResponse::transport_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.transport_options)
  return _internal_transport_options();
}
inline void RecvTensorResponse::unsafe_arena_set_allocated_transport_options(
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  _impl_.transport_options_ = transport_options;
  if (transport_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvTensorResponse.transport_options)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::release_transport_options() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvTensorResponse.transport_options)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::_internal_mutable_transport_options() {
  
  if (_impl_.transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.transport_options_ = p;
  }
  return _impl_.transport_options_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvTensorResponse::mutable_transport_options() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_transport_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvTensorResponse.transport_options)
  return _msg;
}
inline void RecvTensorResponse::set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options));
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvTensorResponse.transport_options)
}

// bool require_ack = 5;
inline void RecvTensorResponse::clear_require_ack() {
  _impl_.require_ack_ = false;
}
inline bool RecvTensorResponse::_internal_require_ack() const {
  return _impl_.require_ack_;
}
inline bool RecvTensorResponse::require_ack() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvTensorResponse.require_ack)
  return _internal_require_ack();
}
inline void RecvTensorResponse::_internal_set_require_ack(bool value) {
  
  _impl_.require_ack_ = value;
}
inline void RecvTensorResponse::set_require_ack(bool value) {
  _internal_set_require_ack(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvTensorResponse.require_ack)
}

// -------------------------------------------------------------------

// MarkRecvFinishedRequest

// int64 request_id = 1;
inline void MarkRecvFinishedRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t MarkRecvFinishedRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t MarkRecvFinishedRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MarkRecvFinishedRequest.request_id)
  return _internal_request_id();
}
inline void MarkRecvFinishedRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void MarkRecvFinishedRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MarkRecvFinishedRequest.request_id)
}

// -------------------------------------------------------------------

// MarkRecvFinishedResponse

// -------------------------------------------------------------------

// LoggingRequest

// bool enable_rpc_logging = 1;
inline void LoggingRequest::clear_enable_rpc_logging() {
  _impl_.enable_rpc_logging_ = false;
}
inline bool LoggingRequest::_internal_enable_rpc_logging() const {
  return _impl_.enable_rpc_logging_;
}
inline bool LoggingRequest::enable_rpc_logging() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.enable_rpc_logging)
  return _internal_enable_rpc_logging();
}
inline void LoggingRequest::_internal_set_enable_rpc_logging(bool value) {
  
  _impl_.enable_rpc_logging_ = value;
}
inline void LoggingRequest::set_enable_rpc_logging(bool value) {
  _internal_set_enable_rpc_logging(value);
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.enable_rpc_logging)
}

// bool disable_rpc_logging = 4;
inline void LoggingRequest::clear_disable_rpc_logging() {
  _impl_.disable_rpc_logging_ = false;
}
inline bool LoggingRequest::_internal_disable_rpc_logging() const {
  return _impl_.disable_rpc_logging_;
}
inline bool LoggingRequest::disable_rpc_logging() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.disable_rpc_logging)
  return _internal_disable_rpc_logging();
}
inline void LoggingRequest::_internal_set_disable_rpc_logging(bool value) {
  
  _impl_.disable_rpc_logging_ = value;
}
inline void LoggingRequest::set_disable_rpc_logging(bool value) {
  _internal_set_disable_rpc_logging(value);
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.disable_rpc_logging)
}

// bool clear = 2;
inline void LoggingRequest::clear_clear() {
  _impl_.clear_ = false;
}
inline bool LoggingRequest::_internal_clear() const {
  return _impl_.clear_;
}
inline bool LoggingRequest::clear() const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.clear)
  return _internal_clear();
}
inline void LoggingRequest::_internal_set_clear(bool value) {
  
  _impl_.clear_ = value;
}
inline void LoggingRequest::set_clear(bool value) {
  _internal_set_clear(value);
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.clear)
}

// repeated int64 fetch_step_id = 3;
inline int LoggingRequest::_internal_fetch_step_id_size() const {
  return _impl_.fetch_step_id_.size();
}
inline int LoggingRequest::fetch_step_id_size() const {
  return _internal_fetch_step_id_size();
}
inline void LoggingRequest::clear_fetch_step_id() {
  _impl_.fetch_step_id_.Clear();
}
inline int64_t LoggingRequest::_internal_fetch_step_id(int index) const {
  return _impl_.fetch_step_id_.Get(index);
}
inline int64_t LoggingRequest::fetch_step_id(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingRequest.fetch_step_id)
  return _internal_fetch_step_id(index);
}
inline void LoggingRequest::set_fetch_step_id(int index, int64_t value) {
  _impl_.fetch_step_id_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.LoggingRequest.fetch_step_id)
}
inline void LoggingRequest::_internal_add_fetch_step_id(int64_t value) {
  _impl_.fetch_step_id_.Add(value);
}
inline void LoggingRequest::add_fetch_step_id(int64_t value) {
  _internal_add_fetch_step_id(value);
  // @@protoc_insertion_point(field_add:tensorflow.LoggingRequest.fetch_step_id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
LoggingRequest::_internal_fetch_step_id() const {
  return _impl_.fetch_step_id_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
LoggingRequest::fetch_step_id() const {
  // @@protoc_insertion_point(field_list:tensorflow.LoggingRequest.fetch_step_id)
  return _internal_fetch_step_id();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
LoggingRequest::_internal_mutable_fetch_step_id() {
  return &_impl_.fetch_step_id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
LoggingRequest::mutable_fetch_step_id() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LoggingRequest.fetch_step_id)
  return _internal_mutable_fetch_step_id();
}

// -------------------------------------------------------------------

// LabeledStepStats

// int64 step_id = 1;
inline void LabeledStepStats::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t LabeledStepStats::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t LabeledStepStats::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.LabeledStepStats.step_id)
  return _internal_step_id();
}
inline void LabeledStepStats::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void LabeledStepStats::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.LabeledStepStats.step_id)
}

// .tensorflow.StepStats step_stats = 2;
inline bool LabeledStepStats::_internal_has_step_stats() const {
  return this != internal_default_instance() && _impl_.step_stats_ != nullptr;
}
inline bool LabeledStepStats::has_step_stats() const {
  return _internal_has_step_stats();
}
inline const ::tensorflow::StepStats& LabeledStepStats::_internal_step_stats() const {
  const ::tensorflow::StepStats* p = _impl_.step_stats_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StepStats&>(
      ::tensorflow::_StepStats_default_instance_);
}
inline const ::tensorflow::StepStats& LabeledStepStats::step_stats() const {
  // @@protoc_insertion_point(field_get:tensorflow.LabeledStepStats.step_stats)
  return _internal_step_stats();
}
inline void LabeledStepStats::unsafe_arena_set_allocated_step_stats(
    ::tensorflow::StepStats* step_stats) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.step_stats_);
  }
  _impl_.step_stats_ = step_stats;
  if (step_stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.LabeledStepStats.step_stats)
}
inline ::tensorflow::StepStats* LabeledStepStats::release_step_stats() {
  
  ::tensorflow::StepStats* temp = _impl_.step_stats_;
  _impl_.step_stats_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StepStats* LabeledStepStats::unsafe_arena_release_step_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.LabeledStepStats.step_stats)
  
  ::tensorflow::StepStats* temp = _impl_.step_stats_;
  _impl_.step_stats_ = nullptr;
  return temp;
}
inline ::tensorflow::StepStats* LabeledStepStats::_internal_mutable_step_stats() {
  
  if (_impl_.step_stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StepStats>(GetArenaForAllocation());
    _impl_.step_stats_ = p;
  }
  return _impl_.step_stats_;
}
inline ::tensorflow::StepStats* LabeledStepStats::mutable_step_stats() {
  ::tensorflow::StepStats* _msg = _internal_mutable_step_stats();
  // @@protoc_insertion_point(field_mutable:tensorflow.LabeledStepStats.step_stats)
  return _msg;
}
inline void LabeledStepStats::set_allocated_step_stats(::tensorflow::StepStats* step_stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.step_stats_);
  }
  if (step_stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(step_stats));
    if (message_arena != submessage_arena) {
      step_stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, step_stats, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.step_stats_ = step_stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.LabeledStepStats.step_stats)
}

// -------------------------------------------------------------------

// LoggingResponse

// repeated .tensorflow.LabeledStepStats step = 1;
inline int LoggingResponse::_internal_step_size() const {
  return _impl_.step_.size();
}
inline int LoggingResponse::step_size() const {
  return _internal_step_size();
}
inline void LoggingResponse::clear_step() {
  _impl_.step_.Clear();
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::mutable_step(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.LoggingResponse.step)
  return _impl_.step_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >*
LoggingResponse::mutable_step() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.LoggingResponse.step)
  return &_impl_.step_;
}
inline const ::tensorflow::LabeledStepStats& LoggingResponse::_internal_step(int index) const {
  return _impl_.step_.Get(index);
}
inline const ::tensorflow::LabeledStepStats& LoggingResponse::step(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.LoggingResponse.step)
  return _internal_step(index);
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::_internal_add_step() {
  return _impl_.step_.Add();
}
inline ::tensorflow::LabeledStepStats* LoggingResponse::add_step() {
  ::tensorflow::LabeledStepStats* _add = _internal_add_step();
  // @@protoc_insertion_point(field_add:tensorflow.LoggingResponse.step)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::LabeledStepStats >&
LoggingResponse::step() const {
  // @@protoc_insertion_point(field_list:tensorflow.LoggingResponse.step)
  return _impl_.step_;
}

// -------------------------------------------------------------------

// TraceOpts

// double duration = 1;
inline void TraceOpts::clear_duration() {
  _impl_.duration_ = 0;
}
inline double TraceOpts::_internal_duration() const {
  return _impl_.duration_;
}
inline double TraceOpts::duration() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.duration)
  return _internal_duration();
}
inline void TraceOpts::_internal_set_duration(double value) {
  
  _impl_.duration_ = value;
}
inline void TraceOpts::set_duration(double value) {
  _internal_set_duration(value);
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.duration)
}

// bool use_step_profiler = 2;
inline void TraceOpts::clear_use_step_profiler() {
  _impl_.use_step_profiler_ = false;
}
inline bool TraceOpts::_internal_use_step_profiler() const {
  return _impl_.use_step_profiler_;
}
inline bool TraceOpts::use_step_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_step_profiler)
  return _internal_use_step_profiler();
}
inline void TraceOpts::_internal_set_use_step_profiler(bool value) {
  
  _impl_.use_step_profiler_ = value;
}
inline void TraceOpts::set_use_step_profiler(bool value) {
  _internal_set_use_step_profiler(value);
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_step_profiler)
}

// bool use_kernel_profiler = 3;
inline void TraceOpts::clear_use_kernel_profiler() {
  _impl_.use_kernel_profiler_ = false;
}
inline bool TraceOpts::_internal_use_kernel_profiler() const {
  return _impl_.use_kernel_profiler_;
}
inline bool TraceOpts::use_kernel_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_kernel_profiler)
  return _internal_use_kernel_profiler();
}
inline void TraceOpts::_internal_set_use_kernel_profiler(bool value) {
  
  _impl_.use_kernel_profiler_ = value;
}
inline void TraceOpts::set_use_kernel_profiler(bool value) {
  _internal_set_use_kernel_profiler(value);
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_kernel_profiler)
}

// bool use_extended_profiler = 4;
inline void TraceOpts::clear_use_extended_profiler() {
  _impl_.use_extended_profiler_ = false;
}
inline bool TraceOpts::_internal_use_extended_profiler() const {
  return _impl_.use_extended_profiler_;
}
inline bool TraceOpts::use_extended_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_extended_profiler)
  return _internal_use_extended_profiler();
}
inline void TraceOpts::_internal_set_use_extended_profiler(bool value) {
  
  _impl_.use_extended_profiler_ = value;
}
inline void TraceOpts::set_use_extended_profiler(bool value) {
  _internal_set_use_extended_profiler(value);
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_extended_profiler)
}

// bool use_gpu_profiler = 5;
inline void TraceOpts::clear_use_gpu_profiler() {
  _impl_.use_gpu_profiler_ = false;
}
inline bool TraceOpts::_internal_use_gpu_profiler() const {
  return _impl_.use_gpu_profiler_;
}
inline bool TraceOpts::use_gpu_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_gpu_profiler)
  return _internal_use_gpu_profiler();
}
inline void TraceOpts::_internal_set_use_gpu_profiler(bool value) {
  
  _impl_.use_gpu_profiler_ = value;
}
inline void TraceOpts::set_use_gpu_profiler(bool value) {
  _internal_set_use_gpu_profiler(value);
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_gpu_profiler)
}

// bool use_sample_profiler = 6;
inline void TraceOpts::clear_use_sample_profiler() {
  _impl_.use_sample_profiler_ = false;
}
inline bool TraceOpts::_internal_use_sample_profiler() const {
  return _impl_.use_sample_profiler_;
}
inline bool TraceOpts::use_sample_profiler() const {
  // @@protoc_insertion_point(field_get:tensorflow.TraceOpts.use_sample_profiler)
  return _internal_use_sample_profiler();
}
inline void TraceOpts::_internal_set_use_sample_profiler(bool value) {
  
  _impl_.use_sample_profiler_ = value;
}
inline void TraceOpts::set_use_sample_profiler(bool value) {
  _internal_set_use_sample_profiler(value);
  // @@protoc_insertion_point(field_set:tensorflow.TraceOpts.use_sample_profiler)
}

// -------------------------------------------------------------------

// TracingRequest

// .tensorflow.TraceOpts options = 1;
inline bool TracingRequest::_internal_has_options() const {
  return this != internal_default_instance() && _impl_.options_ != nullptr;
}
inline bool TracingRequest::has_options() const {
  return _internal_has_options();
}
inline void TracingRequest::clear_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.options_ != nullptr) {
    delete _impl_.options_;
  }
  _impl_.options_ = nullptr;
}
inline const ::tensorflow::TraceOpts& TracingRequest::_internal_options() const {
  const ::tensorflow::TraceOpts* p = _impl_.options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TraceOpts&>(
      ::tensorflow::_TraceOpts_default_instance_);
}
inline const ::tensorflow::TraceOpts& TracingRequest::options() const {
  // @@protoc_insertion_point(field_get:tensorflow.TracingRequest.options)
  return _internal_options();
}
inline void TracingRequest::unsafe_arena_set_allocated_options(
    ::tensorflow::TraceOpts* options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.options_);
  }
  _impl_.options_ = options;
  if (options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TracingRequest.options)
}
inline ::tensorflow::TraceOpts* TracingRequest::release_options() {
  
  ::tensorflow::TraceOpts* temp = _impl_.options_;
  _impl_.options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TraceOpts* TracingRequest::unsafe_arena_release_options() {
  // @@protoc_insertion_point(field_release:tensorflow.TracingRequest.options)
  
  ::tensorflow::TraceOpts* temp = _impl_.options_;
  _impl_.options_ = nullptr;
  return temp;
}
inline ::tensorflow::TraceOpts* TracingRequest::_internal_mutable_options() {
  
  if (_impl_.options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TraceOpts>(GetArenaForAllocation());
    _impl_.options_ = p;
  }
  return _impl_.options_;
}
inline ::tensorflow::TraceOpts* TracingRequest::mutable_options() {
  ::tensorflow::TraceOpts* _msg = _internal_mutable_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.TracingRequest.options)
  return _msg;
}
inline void TracingRequest::set_allocated_options(::tensorflow::TraceOpts* options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.options_;
  }
  if (options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(options);
    if (message_arena != submessage_arena) {
      options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.options_ = options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TracingRequest.options)
}

// -------------------------------------------------------------------

// TracingResponse

// -------------------------------------------------------------------

// RecvBufRequest

// int64 step_id = 1;
inline void RecvBufRequest::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t RecvBufRequest::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t RecvBufRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.step_id)
  return _internal_step_id();
}
inline void RecvBufRequest::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void RecvBufRequest::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.step_id)
}

// string buf_rendezvous_key = 2;
inline void RecvBufRequest::clear_buf_rendezvous_key() {
  _impl_.buf_rendezvous_key_.ClearToEmpty();
}
inline const std::string& RecvBufRequest::buf_rendezvous_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return _internal_buf_rendezvous_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RecvBufRequest::set_buf_rendezvous_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.buf_rendezvous_key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.buf_rendezvous_key)
}
inline std::string* RecvBufRequest::mutable_buf_rendezvous_key() {
  std::string* _s = _internal_mutable_buf_rendezvous_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return _s;
}
inline const std::string& RecvBufRequest::_internal_buf_rendezvous_key() const {
  return _impl_.buf_rendezvous_key_.Get();
}
inline void RecvBufRequest::_internal_set_buf_rendezvous_key(const std::string& value) {
  
  _impl_.buf_rendezvous_key_.Set(value, GetArenaForAllocation());
}
inline std::string* RecvBufRequest::_internal_mutable_buf_rendezvous_key() {
  
  return _impl_.buf_rendezvous_key_.Mutable(GetArenaForAllocation());
}
inline std::string* RecvBufRequest::release_buf_rendezvous_key() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.buf_rendezvous_key)
  return _impl_.buf_rendezvous_key_.Release();
}
inline void RecvBufRequest::set_allocated_buf_rendezvous_key(std::string* buf_rendezvous_key) {
  if (buf_rendezvous_key != nullptr) {
    
  } else {
    
  }
  _impl_.buf_rendezvous_key_.SetAllocated(buf_rendezvous_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.buf_rendezvous_key_.IsDefault()) {
    _impl_.buf_rendezvous_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.buf_rendezvous_key)
}

// int64 num_bytes = 3;
inline void RecvBufRequest::clear_num_bytes() {
  _impl_.num_bytes_ = int64_t{0};
}
inline int64_t RecvBufRequest::_internal_num_bytes() const {
  return _impl_.num_bytes_;
}
inline int64_t RecvBufRequest::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.num_bytes)
  return _internal_num_bytes();
}
inline void RecvBufRequest::_internal_set_num_bytes(int64_t value) {
  
  _impl_.num_bytes_ = value;
}
inline void RecvBufRequest::set_num_bytes(int64_t value) {
  _internal_set_num_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.num_bytes)
}

// fixed64 buf_ptr = 4;
inline void RecvBufRequest::clear_buf_ptr() {
  _impl_.buf_ptr_ = uint64_t{0u};
}
inline uint64_t RecvBufRequest::_internal_buf_ptr() const {
  return _impl_.buf_ptr_;
}
inline uint64_t RecvBufRequest::buf_ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.buf_ptr)
  return _internal_buf_ptr();
}
inline void RecvBufRequest::_internal_set_buf_ptr(uint64_t value) {
  
  _impl_.buf_ptr_ = value;
}
inline void RecvBufRequest::set_buf_ptr(uint64_t value) {
  _internal_set_buf_ptr(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.buf_ptr)
}

// .tensorflow.DeviceLocality client_locality = 5;
inline bool RecvBufRequest::_internal_has_client_locality() const {
  return this != internal_default_instance() && _impl_.client_locality_ != nullptr;
}
inline bool RecvBufRequest::has_client_locality() const {
  return _internal_has_client_locality();
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::_internal_client_locality() const {
  const ::tensorflow::DeviceLocality* p = _impl_.client_locality_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceLocality&>(
      ::tensorflow::_DeviceLocality_default_instance_);
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::client_locality() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.client_locality)
  return _internal_client_locality();
}
inline void RecvBufRequest::unsafe_arena_set_allocated_client_locality(
    ::tensorflow::DeviceLocality* client_locality) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.client_locality_);
  }
  _impl_.client_locality_ = client_locality;
  if (client_locality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.client_locality)
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::release_client_locality() {
  
  ::tensorflow::DeviceLocality* temp = _impl_.client_locality_;
  _impl_.client_locality_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::unsafe_arena_release_client_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.client_locality)
  
  ::tensorflow::DeviceLocality* temp = _impl_.client_locality_;
  _impl_.client_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::_internal_mutable_client_locality() {
  
  if (_impl_.client_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaForAllocation());
    _impl_.client_locality_ = p;
  }
  return _impl_.client_locality_;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::mutable_client_locality() {
  ::tensorflow::DeviceLocality* _msg = _internal_mutable_client_locality();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.client_locality)
  return _msg;
}
inline void RecvBufRequest::set_allocated_client_locality(::tensorflow::DeviceLocality* client_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.client_locality_);
  }
  if (client_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(client_locality));
    if (message_arena != submessage_arena) {
      client_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, client_locality, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.client_locality_ = client_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.client_locality)
}

// .tensorflow.DeviceLocality server_locality = 6;
inline bool RecvBufRequest::_internal_has_server_locality() const {
  return this != internal_default_instance() && _impl_.server_locality_ != nullptr;
}
inline bool RecvBufRequest::has_server_locality() const {
  return _internal_has_server_locality();
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::_internal_server_locality() const {
  const ::tensorflow::DeviceLocality* p = _impl_.server_locality_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceLocality&>(
      ::tensorflow::_DeviceLocality_default_instance_);
}
inline const ::tensorflow::DeviceLocality& RecvBufRequest::server_locality() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.server_locality)
  return _internal_server_locality();
}
inline void RecvBufRequest::unsafe_arena_set_allocated_server_locality(
    ::tensorflow::DeviceLocality* server_locality) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_locality_);
  }
  _impl_.server_locality_ = server_locality;
  if (server_locality) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.server_locality)
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::release_server_locality() {
  
  ::tensorflow::DeviceLocality* temp = _impl_.server_locality_;
  _impl_.server_locality_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::unsafe_arena_release_server_locality() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.server_locality)
  
  ::tensorflow::DeviceLocality* temp = _impl_.server_locality_;
  _impl_.server_locality_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::_internal_mutable_server_locality() {
  
  if (_impl_.server_locality_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceLocality>(GetArenaForAllocation());
    _impl_.server_locality_ = p;
  }
  return _impl_.server_locality_;
}
inline ::tensorflow::DeviceLocality* RecvBufRequest::mutable_server_locality() {
  ::tensorflow::DeviceLocality* _msg = _internal_mutable_server_locality();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.server_locality)
  return _msg;
}
inline void RecvBufRequest::set_allocated_server_locality(::tensorflow::DeviceLocality* server_locality) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.server_locality_);
  }
  if (server_locality) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(server_locality));
    if (message_arena != submessage_arena) {
      server_locality = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, server_locality, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.server_locality_ = server_locality;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.server_locality)
}

// .google.protobuf.Any transport_options = 7;
inline bool RecvBufRequest::_internal_has_transport_options() const {
  return this != internal_default_instance() && _impl_.transport_options_ != nullptr;
}
inline bool RecvBufRequest::has_transport_options() const {
  return _internal_has_transport_options();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvBufRequest::_internal_transport_options() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.transport_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvBufRequest::transport_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.transport_options)
  return _internal_transport_options();
}
inline void RecvBufRequest::unsafe_arena_set_allocated_transport_options(
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  _impl_.transport_options_ = transport_options;
  if (transport_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufRequest.transport_options)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::release_transport_options() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.transport_options)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::_internal_mutable_transport_options() {
  
  if (_impl_.transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.transport_options_ = p;
  }
  return _impl_.transport_options_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufRequest::mutable_transport_options() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_transport_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.transport_options)
  return _msg;
}
inline void RecvBufRequest::set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options));
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.transport_options)
}

// string src_device = 8;
inline void RecvBufRequest::clear_src_device() {
  _impl_.src_device_.ClearToEmpty();
}
inline const std::string& RecvBufRequest::src_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.src_device)
  return _internal_src_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RecvBufRequest::set_src_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.src_device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.src_device)
}
inline std::string* RecvBufRequest::mutable_src_device() {
  std::string* _s = _internal_mutable_src_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.src_device)
  return _s;
}
inline const std::string& RecvBufRequest::_internal_src_device() const {
  return _impl_.src_device_.Get();
}
inline void RecvBufRequest::_internal_set_src_device(const std::string& value) {
  
  _impl_.src_device_.Set(value, GetArenaForAllocation());
}
inline std::string* RecvBufRequest::_internal_mutable_src_device() {
  
  return _impl_.src_device_.Mutable(GetArenaForAllocation());
}
inline std::string* RecvBufRequest::release_src_device() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.src_device)
  return _impl_.src_device_.Release();
}
inline void RecvBufRequest::set_allocated_src_device(std::string* src_device) {
  if (src_device != nullptr) {
    
  } else {
    
  }
  _impl_.src_device_.SetAllocated(src_device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.src_device_.IsDefault()) {
    _impl_.src_device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.src_device)
}

// string dst_device = 9;
inline void RecvBufRequest::clear_dst_device() {
  _impl_.dst_device_.ClearToEmpty();
}
inline const std::string& RecvBufRequest::dst_device() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.dst_device)
  return _internal_dst_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RecvBufRequest::set_dst_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.dst_device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.dst_device)
}
inline std::string* RecvBufRequest::mutable_dst_device() {
  std::string* _s = _internal_mutable_dst_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRequest.dst_device)
  return _s;
}
inline const std::string& RecvBufRequest::_internal_dst_device() const {
  return _impl_.dst_device_.Get();
}
inline void RecvBufRequest::_internal_set_dst_device(const std::string& value) {
  
  _impl_.dst_device_.Set(value, GetArenaForAllocation());
}
inline std::string* RecvBufRequest::_internal_mutable_dst_device() {
  
  return _impl_.dst_device_.Mutable(GetArenaForAllocation());
}
inline std::string* RecvBufRequest::release_dst_device() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufRequest.dst_device)
  return _impl_.dst_device_.Release();
}
inline void RecvBufRequest::set_allocated_dst_device(std::string* dst_device) {
  if (dst_device != nullptr) {
    
  } else {
    
  }
  _impl_.dst_device_.SetAllocated(dst_device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.dst_device_.IsDefault()) {
    _impl_.dst_device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufRequest.dst_device)
}

// int64 request_id = 10;
inline void RecvBufRequest::clear_request_id() {
  _impl_.request_id_ = int64_t{0};
}
inline int64_t RecvBufRequest::_internal_request_id() const {
  return _impl_.request_id_;
}
inline int64_t RecvBufRequest::request_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.request_id)
  return _internal_request_id();
}
inline void RecvBufRequest::_internal_set_request_id(int64_t value) {
  
  _impl_.request_id_ = value;
}
inline void RecvBufRequest::set_request_id(int64_t value) {
  _internal_set_request_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.request_id)
}

// uint64 src_incarnation = 11;
inline void RecvBufRequest::clear_src_incarnation() {
  _impl_.src_incarnation_ = uint64_t{0u};
}
inline uint64_t RecvBufRequest::_internal_src_incarnation() const {
  return _impl_.src_incarnation_;
}
inline uint64_t RecvBufRequest::src_incarnation() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRequest.src_incarnation)
  return _internal_src_incarnation();
}
inline void RecvBufRequest::_internal_set_src_incarnation(uint64_t value) {
  
  _impl_.src_incarnation_ = value;
}
inline void RecvBufRequest::set_src_incarnation(uint64_t value) {
  _internal_set_src_incarnation(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRequest.src_incarnation)
}

// -------------------------------------------------------------------

// RecvBufResponse

// fixed64 buf_ptr = 1;
inline void RecvBufResponse::clear_buf_ptr() {
  _impl_.buf_ptr_ = uint64_t{0u};
}
inline uint64_t RecvBufResponse::_internal_buf_ptr() const {
  return _impl_.buf_ptr_;
}
inline uint64_t RecvBufResponse::buf_ptr() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.buf_ptr)
  return _internal_buf_ptr();
}
inline void RecvBufResponse::_internal_set_buf_ptr(uint64_t value) {
  
  _impl_.buf_ptr_ = value;
}
inline void RecvBufResponse::set_buf_ptr(uint64_t value) {
  _internal_set_buf_ptr(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.buf_ptr)
}

// int64 num_bytes = 2;
inline void RecvBufResponse::clear_num_bytes() {
  _impl_.num_bytes_ = int64_t{0};
}
inline int64_t RecvBufResponse::_internal_num_bytes() const {
  return _impl_.num_bytes_;
}
inline int64_t RecvBufResponse::num_bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.num_bytes)
  return _internal_num_bytes();
}
inline void RecvBufResponse::_internal_set_num_bytes(int64_t value) {
  
  _impl_.num_bytes_ = value;
}
inline void RecvBufResponse::set_num_bytes(int64_t value) {
  _internal_set_num_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.num_bytes)
}

// bool is_dead = 3;
inline void RecvBufResponse::clear_is_dead() {
  _impl_.is_dead_ = false;
}
inline bool RecvBufResponse::_internal_is_dead() const {
  return _impl_.is_dead_;
}
inline bool RecvBufResponse::is_dead() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.is_dead)
  return _internal_is_dead();
}
inline void RecvBufResponse::_internal_set_is_dead(bool value) {
  
  _impl_.is_dead_ = value;
}
inline void RecvBufResponse::set_is_dead(bool value) {
  _internal_set_is_dead(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.is_dead)
}

// .google.protobuf.Any transport_options = 4;
inline bool RecvBufResponse::_internal_has_transport_options() const {
  return this != internal_default_instance() && _impl_.transport_options_ != nullptr;
}
inline bool RecvBufResponse::has_transport_options() const {
  return _internal_has_transport_options();
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvBufResponse::_internal_transport_options() const {
  const ::PROTOBUF_NAMESPACE_ID::Any* p = _impl_.transport_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Any&>(
      ::PROTOBUF_NAMESPACE_ID::_Any_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::Any& RecvBufResponse::transport_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.transport_options)
  return _internal_transport_options();
}
inline void RecvBufResponse::unsafe_arena_set_allocated_transport_options(
    ::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  _impl_.transport_options_ = transport_options;
  if (transport_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RecvBufResponse.transport_options)
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::release_transport_options() {
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::unsafe_arena_release_transport_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RecvBufResponse.transport_options)
  
  ::PROTOBUF_NAMESPACE_ID::Any* temp = _impl_.transport_options_;
  _impl_.transport_options_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::_internal_mutable_transport_options() {
  
  if (_impl_.transport_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::Any>(GetArenaForAllocation());
    _impl_.transport_options_ = p;
  }
  return _impl_.transport_options_;
}
inline ::PROTOBUF_NAMESPACE_ID::Any* RecvBufResponse::mutable_transport_options() {
  ::PROTOBUF_NAMESPACE_ID::Any* _msg = _internal_mutable_transport_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufResponse.transport_options)
  return _msg;
}
inline void RecvBufResponse::set_allocated_transport_options(::PROTOBUF_NAMESPACE_ID::Any* transport_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.transport_options_);
  }
  if (transport_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(transport_options));
    if (message_arena != submessage_arena) {
      transport_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, transport_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.transport_options_ = transport_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RecvBufResponse.transport_options)
}

// int64 send_start_micros = 5;
inline void RecvBufResponse::clear_send_start_micros() {
  _impl_.send_start_micros_ = int64_t{0};
}
inline int64_t RecvBufResponse::_internal_send_start_micros() const {
  return _impl_.send_start_micros_;
}
inline int64_t RecvBufResponse::send_start_micros() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.send_start_micros)
  return _internal_send_start_micros();
}
inline void RecvBufResponse::_internal_set_send_start_micros(int64_t value) {
  
  _impl_.send_start_micros_ = value;
}
inline void RecvBufResponse::set_send_start_micros(int64_t value) {
  _internal_set_send_start_micros(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.send_start_micros)
}

// bool require_ack = 6;
inline void RecvBufResponse::clear_require_ack() {
  _impl_.require_ack_ = false;
}
inline bool RecvBufResponse::_internal_require_ack() const {
  return _impl_.require_ack_;
}
inline bool RecvBufResponse::require_ack() const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufResponse.require_ack)
  return _internal_require_ack();
}
inline void RecvBufResponse::_internal_set_require_ack(bool value) {
  
  _impl_.require_ack_ = value;
}
inline void RecvBufResponse::set_require_ack(bool value) {
  _internal_set_require_ack(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufResponse.require_ack)
}

// -------------------------------------------------------------------

// CompleteGroupRequest

// int32 group_key = 1;
inline void CompleteGroupRequest::clear_group_key() {
  _impl_.group_key_ = 0;
}
inline int32_t CompleteGroupRequest::_internal_group_key() const {
  return _impl_.group_key_;
}
inline int32_t CompleteGroupRequest::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.group_key)
  return _internal_group_key();
}
inline void CompleteGroupRequest::_internal_set_group_key(int32_t value) {
  
  _impl_.group_key_ = value;
}
inline void CompleteGroupRequest::set_group_key(int32_t value) {
  _internal_set_group_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.group_key)
}

// int32 group_size = 2;
inline void CompleteGroupRequest::clear_group_size() {
  _impl_.group_size_ = 0;
}
inline int32_t CompleteGroupRequest::_internal_group_size() const {
  return _impl_.group_size_;
}
inline int32_t CompleteGroupRequest::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.group_size)
  return _internal_group_size();
}
inline void CompleteGroupRequest::_internal_set_group_size(int32_t value) {
  
  _impl_.group_size_ = value;
}
inline void CompleteGroupRequest::set_group_size(int32_t value) {
  _internal_set_group_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.group_size)
}

// string device_type = 3;
inline void CompleteGroupRequest::clear_device_type() {
  _impl_.device_type_.ClearToEmpty();
}
inline const std::string& CompleteGroupRequest::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.device_type)
  return _internal_device_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompleteGroupRequest::set_device_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.device_type)
}
inline std::string* CompleteGroupRequest::mutable_device_type() {
  std::string* _s = _internal_mutable_device_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupRequest.device_type)
  return _s;
}
inline const std::string& CompleteGroupRequest::_internal_device_type() const {
  return _impl_.device_type_.Get();
}
inline void CompleteGroupRequest::_internal_set_device_type(const std::string& value) {
  
  _impl_.device_type_.Set(value, GetArenaForAllocation());
}
inline std::string* CompleteGroupRequest::_internal_mutable_device_type() {
  
  return _impl_.device_type_.Mutable(GetArenaForAllocation());
}
inline std::string* CompleteGroupRequest::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupRequest.device_type)
  return _impl_.device_type_.Release();
}
inline void CompleteGroupRequest::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  _impl_.device_type_.SetAllocated(device_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_type_.IsDefault()) {
    _impl_.device_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupRequest.device_type)
}

// int32 collective_type = 5;
inline void CompleteGroupRequest::clear_collective_type() {
  _impl_.collective_type_ = 0;
}
inline int32_t CompleteGroupRequest::_internal_collective_type() const {
  return _impl_.collective_type_;
}
inline int32_t CompleteGroupRequest::collective_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.collective_type)
  return _internal_collective_type();
}
inline void CompleteGroupRequest::_internal_set_collective_type(int32_t value) {
  
  _impl_.collective_type_ = value;
}
inline void CompleteGroupRequest::set_collective_type(int32_t value) {
  _internal_set_collective_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupRequest.collective_type)
}

// .tensorflow.DeviceAttributes device_attributes = 6;
inline bool CompleteGroupRequest::_internal_has_device_attributes() const {
  return this != internal_default_instance() && _impl_.device_attributes_ != nullptr;
}
inline bool CompleteGroupRequest::has_device_attributes() const {
  return _internal_has_device_attributes();
}
inline const ::tensorflow::DeviceAttributes& CompleteGroupRequest::_internal_device_attributes() const {
  const ::tensorflow::DeviceAttributes* p = _impl_.device_attributes_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceAttributes&>(
      ::tensorflow::_DeviceAttributes_default_instance_);
}
inline const ::tensorflow::DeviceAttributes& CompleteGroupRequest::device_attributes() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupRequest.device_attributes)
  return _internal_device_attributes();
}
inline void CompleteGroupRequest::unsafe_arena_set_allocated_device_attributes(
    ::tensorflow::DeviceAttributes* device_attributes) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_attributes_);
  }
  _impl_.device_attributes_ = device_attributes;
  if (device_attributes) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteGroupRequest.device_attributes)
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::release_device_attributes() {
  
  ::tensorflow::DeviceAttributes* temp = _impl_.device_attributes_;
  _impl_.device_attributes_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::unsafe_arena_release_device_attributes() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupRequest.device_attributes)
  
  ::tensorflow::DeviceAttributes* temp = _impl_.device_attributes_;
  _impl_.device_attributes_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::_internal_mutable_device_attributes() {
  
  if (_impl_.device_attributes_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceAttributes>(GetArenaForAllocation());
    _impl_.device_attributes_ = p;
  }
  return _impl_.device_attributes_;
}
inline ::tensorflow::DeviceAttributes* CompleteGroupRequest::mutable_device_attributes() {
  ::tensorflow::DeviceAttributes* _msg = _internal_mutable_device_attributes();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupRequest.device_attributes)
  return _msg;
}
inline void CompleteGroupRequest::set_allocated_device_attributes(::tensorflow::DeviceAttributes* device_attributes) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.device_attributes_);
  }
  if (device_attributes) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(device_attributes));
    if (message_arena != submessage_arena) {
      device_attributes = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, device_attributes, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.device_attributes_ = device_attributes;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupRequest.device_attributes)
}

// -------------------------------------------------------------------

// CompleteGroupResponse

// int32 group_key = 1;
inline void CompleteGroupResponse::clear_group_key() {
  _impl_.group_key_ = 0;
}
inline int32_t CompleteGroupResponse::_internal_group_key() const {
  return _impl_.group_key_;
}
inline int32_t CompleteGroupResponse::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.group_key)
  return _internal_group_key();
}
inline void CompleteGroupResponse::_internal_set_group_key(int32_t value) {
  
  _impl_.group_key_ = value;
}
inline void CompleteGroupResponse::set_group_key(int32_t value) {
  _internal_set_group_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.group_key)
}

// int32 group_size = 2;
inline void CompleteGroupResponse::clear_group_size() {
  _impl_.group_size_ = 0;
}
inline int32_t CompleteGroupResponse::_internal_group_size() const {
  return _impl_.group_size_;
}
inline int32_t CompleteGroupResponse::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.group_size)
  return _internal_group_size();
}
inline void CompleteGroupResponse::_internal_set_group_size(int32_t value) {
  
  _impl_.group_size_ = value;
}
inline void CompleteGroupResponse::set_group_size(int32_t value) {
  _internal_set_group_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.group_size)
}

// string device_type = 3;
inline void CompleteGroupResponse::clear_device_type() {
  _impl_.device_type_.ClearToEmpty();
}
inline const std::string& CompleteGroupResponse::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.device_type)
  return _internal_device_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompleteGroupResponse::set_device_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.device_type)
}
inline std::string* CompleteGroupResponse::mutable_device_type() {
  std::string* _s = _internal_mutable_device_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.device_type)
  return _s;
}
inline const std::string& CompleteGroupResponse::_internal_device_type() const {
  return _impl_.device_type_.Get();
}
inline void CompleteGroupResponse::_internal_set_device_type(const std::string& value) {
  
  _impl_.device_type_.Set(value, GetArenaForAllocation());
}
inline std::string* CompleteGroupResponse::_internal_mutable_device_type() {
  
  return _impl_.device_type_.Mutable(GetArenaForAllocation());
}
inline std::string* CompleteGroupResponse::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupResponse.device_type)
  return _impl_.device_type_.Release();
}
inline void CompleteGroupResponse::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  _impl_.device_type_.SetAllocated(device_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_type_.IsDefault()) {
    _impl_.device_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupResponse.device_type)
}

// int32 num_tasks = 4;
inline void CompleteGroupResponse::clear_num_tasks() {
  _impl_.num_tasks_ = 0;
}
inline int32_t CompleteGroupResponse::_internal_num_tasks() const {
  return _impl_.num_tasks_;
}
inline int32_t CompleteGroupResponse::num_tasks() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.num_tasks)
  return _internal_num_tasks();
}
inline void CompleteGroupResponse::_internal_set_num_tasks(int32_t value) {
  
  _impl_.num_tasks_ = value;
}
inline void CompleteGroupResponse::set_num_tasks(int32_t value) {
  _internal_set_num_tasks(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.num_tasks)
}

// bytes communicator_key = 7;
inline void CompleteGroupResponse::clear_communicator_key() {
  _impl_.communicator_key_.ClearToEmpty();
}
inline const std::string& CompleteGroupResponse::communicator_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.communicator_key)
  return _internal_communicator_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompleteGroupResponse::set_communicator_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.communicator_key_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteGroupResponse.communicator_key)
}
inline std::string* CompleteGroupResponse::mutable_communicator_key() {
  std::string* _s = _internal_mutable_communicator_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.communicator_key)
  return _s;
}
inline const std::string& CompleteGroupResponse::_internal_communicator_key() const {
  return _impl_.communicator_key_.Get();
}
inline void CompleteGroupResponse::_internal_set_communicator_key(const std::string& value) {
  
  _impl_.communicator_key_.Set(value, GetArenaForAllocation());
}
inline std::string* CompleteGroupResponse::_internal_mutable_communicator_key() {
  
  return _impl_.communicator_key_.Mutable(GetArenaForAllocation());
}
inline std::string* CompleteGroupResponse::release_communicator_key() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteGroupResponse.communicator_key)
  return _impl_.communicator_key_.Release();
}
inline void CompleteGroupResponse::set_allocated_communicator_key(std::string* communicator_key) {
  if (communicator_key != nullptr) {
    
  } else {
    
  }
  _impl_.communicator_key_.SetAllocated(communicator_key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.communicator_key_.IsDefault()) {
    _impl_.communicator_key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteGroupResponse.communicator_key)
}

// repeated .tensorflow.DeviceAttributes device_attributes = 8;
inline int CompleteGroupResponse::_internal_device_attributes_size() const {
  return _impl_.device_attributes_.size();
}
inline int CompleteGroupResponse::device_attributes_size() const {
  return _internal_device_attributes_size();
}
inline ::tensorflow::DeviceAttributes* CompleteGroupResponse::mutable_device_attributes(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteGroupResponse.device_attributes)
  return _impl_.device_attributes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >*
CompleteGroupResponse::mutable_device_attributes() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteGroupResponse.device_attributes)
  return &_impl_.device_attributes_;
}
inline const ::tensorflow::DeviceAttributes& CompleteGroupResponse::_internal_device_attributes(int index) const {
  return _impl_.device_attributes_.Get(index);
}
inline const ::tensorflow::DeviceAttributes& CompleteGroupResponse::device_attributes(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteGroupResponse.device_attributes)
  return _internal_device_attributes(index);
}
inline ::tensorflow::DeviceAttributes* CompleteGroupResponse::_internal_add_device_attributes() {
  return _impl_.device_attributes_.Add();
}
inline ::tensorflow::DeviceAttributes* CompleteGroupResponse::add_device_attributes() {
  ::tensorflow::DeviceAttributes* _add = _internal_add_device_attributes();
  // @@protoc_insertion_point(field_add:tensorflow.CompleteGroupResponse.device_attributes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DeviceAttributes >&
CompleteGroupResponse::device_attributes() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteGroupResponse.device_attributes)
  return _impl_.device_attributes_;
}

// -------------------------------------------------------------------

// CompleteInstanceRequest

// string name = 1;
inline void CompleteInstanceRequest::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& CompleteInstanceRequest::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompleteInstanceRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.name)
}
inline std::string* CompleteInstanceRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.name)
  return _s;
}
inline const std::string& CompleteInstanceRequest::_internal_name() const {
  return _impl_.name_.Get();
}
inline void CompleteInstanceRequest::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* CompleteInstanceRequest::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* CompleteInstanceRequest::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.name)
  return _impl_.name_.Release();
}
inline void CompleteInstanceRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.name)
}

// int32 type = 2;
inline void CompleteInstanceRequest::clear_type() {
  _impl_.type_ = 0;
}
inline int32_t CompleteInstanceRequest::_internal_type() const {
  return _impl_.type_;
}
inline int32_t CompleteInstanceRequest::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.type)
  return _internal_type();
}
inline void CompleteInstanceRequest::_internal_set_type(int32_t value) {
  
  _impl_.type_ = value;
}
inline void CompleteInstanceRequest::set_type(int32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.type)
}

// .tensorflow.DataType data_type = 3;
inline void CompleteInstanceRequest::clear_data_type() {
  _impl_.data_type_ = 0;
}
inline ::tensorflow::DataType CompleteInstanceRequest::_internal_data_type() const {
  return static_cast< ::tensorflow::DataType >(_impl_.data_type_);
}
inline ::tensorflow::DataType CompleteInstanceRequest::data_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.data_type)
  return _internal_data_type();
}
inline void CompleteInstanceRequest::_internal_set_data_type(::tensorflow::DataType value) {
  
  _impl_.data_type_ = value;
}
inline void CompleteInstanceRequest::set_data_type(::tensorflow::DataType value) {
  _internal_set_data_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.data_type)
}

// .tensorflow.TensorShapeProto shape = 4;
inline bool CompleteInstanceRequest::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool CompleteInstanceRequest::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& CompleteInstanceRequest::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& CompleteInstanceRequest::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.shape)
  return _internal_shape();
}
inline void CompleteInstanceRequest::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.CompleteInstanceRequest.shape)
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* CompleteInstanceRequest::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.shape)
  return _msg;
}
inline void CompleteInstanceRequest::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.shape)
}

// int32 group_key = 5;
inline void CompleteInstanceRequest::clear_group_key() {
  _impl_.group_key_ = 0;
}
inline int32_t CompleteInstanceRequest::_internal_group_key() const {
  return _impl_.group_key_;
}
inline int32_t CompleteInstanceRequest::group_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.group_key)
  return _internal_group_key();
}
inline void CompleteInstanceRequest::_internal_set_group_key(int32_t value) {
  
  _impl_.group_key_ = value;
}
inline void CompleteInstanceRequest::set_group_key(int32_t value) {
  _internal_set_group_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.group_key)
}

// int32 group_size = 6;
inline void CompleteInstanceRequest::clear_group_size() {
  _impl_.group_size_ = 0;
}
inline int32_t CompleteInstanceRequest::_internal_group_size() const {
  return _impl_.group_size_;
}
inline int32_t CompleteInstanceRequest::group_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.group_size)
  return _internal_group_size();
}
inline void CompleteInstanceRequest::_internal_set_group_size(int32_t value) {
  
  _impl_.group_size_ = value;
}
inline void CompleteInstanceRequest::set_group_size(int32_t value) {
  _internal_set_group_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.group_size)
}

// int32 instance_key = 7;
inline void CompleteInstanceRequest::clear_instance_key() {
  _impl_.instance_key_ = 0;
}
inline int32_t CompleteInstanceRequest::_internal_instance_key() const {
  return _impl_.instance_key_;
}
inline int32_t CompleteInstanceRequest::instance_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.instance_key)
  return _internal_instance_key();
}
inline void CompleteInstanceRequest::_internal_set_instance_key(int32_t value) {
  
  _impl_.instance_key_ = value;
}
inline void CompleteInstanceRequest::set_instance_key(int32_t value) {
  _internal_set_instance_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.instance_key)
}

// string device_type = 8;
inline void CompleteInstanceRequest::clear_device_type() {
  _impl_.device_type_.ClearToEmpty();
}
inline const std::string& CompleteInstanceRequest::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.device_type)
  return _internal_device_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompleteInstanceRequest::set_device_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.device_type)
}
inline std::string* CompleteInstanceRequest::mutable_device_type() {
  std::string* _s = _internal_mutable_device_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.device_type)
  return _s;
}
inline const std::string& CompleteInstanceRequest::_internal_device_type() const {
  return _impl_.device_type_.Get();
}
inline void CompleteInstanceRequest::_internal_set_device_type(const std::string& value) {
  
  _impl_.device_type_.Set(value, GetArenaForAllocation());
}
inline std::string* CompleteInstanceRequest::_internal_mutable_device_type() {
  
  return _impl_.device_type_.Mutable(GetArenaForAllocation());
}
inline std::string* CompleteInstanceRequest::release_device_type() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.device_type)
  return _impl_.device_type_.Release();
}
inline void CompleteInstanceRequest::set_allocated_device_type(std::string* device_type) {
  if (device_type != nullptr) {
    
  } else {
    
  }
  _impl_.device_type_.SetAllocated(device_type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_type_.IsDefault()) {
    _impl_.device_type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.device_type)
}

// repeated int32 subdiv_offset = 9;
inline int CompleteInstanceRequest::_internal_subdiv_offset_size() const {
  return _impl_.subdiv_offset_.size();
}
inline int CompleteInstanceRequest::subdiv_offset_size() const {
  return _internal_subdiv_offset_size();
}
inline void CompleteInstanceRequest::clear_subdiv_offset() {
  _impl_.subdiv_offset_.Clear();
}
inline int32_t CompleteInstanceRequest::_internal_subdiv_offset(int index) const {
  return _impl_.subdiv_offset_.Get(index);
}
inline int32_t CompleteInstanceRequest::subdiv_offset(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return _internal_subdiv_offset(index);
}
inline void CompleteInstanceRequest::set_subdiv_offset(int index, int32_t value) {
  _impl_.subdiv_offset_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.subdiv_offset)
}
inline void CompleteInstanceRequest::_internal_add_subdiv_offset(int32_t value) {
  _impl_.subdiv_offset_.Add(value);
}
inline void CompleteInstanceRequest::add_subdiv_offset(int32_t value) {
  _internal_add_subdiv_offset(value);
  // @@protoc_insertion_point(field_add:tensorflow.CompleteInstanceRequest.subdiv_offset)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CompleteInstanceRequest::_internal_subdiv_offset() const {
  return _impl_.subdiv_offset_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
CompleteInstanceRequest::subdiv_offset() const {
  // @@protoc_insertion_point(field_list:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return _internal_subdiv_offset();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CompleteInstanceRequest::_internal_mutable_subdiv_offset() {
  return &_impl_.subdiv_offset_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
CompleteInstanceRequest::mutable_subdiv_offset() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.CompleteInstanceRequest.subdiv_offset)
  return _internal_mutable_subdiv_offset();
}

// string device = 10;
inline void CompleteInstanceRequest::clear_device() {
  _impl_.device_.ClearToEmpty();
}
inline const std::string& CompleteInstanceRequest::device() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.device)
  return _internal_device();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CompleteInstanceRequest::set_device(ArgT0&& arg0, ArgT... args) {
 
 _impl_.device_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.device)
}
inline std::string* CompleteInstanceRequest::mutable_device() {
  std::string* _s = _internal_mutable_device();
  // @@protoc_insertion_point(field_mutable:tensorflow.CompleteInstanceRequest.device)
  return _s;
}
inline const std::string& CompleteInstanceRequest::_internal_device() const {
  return _impl_.device_.Get();
}
inline void CompleteInstanceRequest::_internal_set_device(const std::string& value) {
  
  _impl_.device_.Set(value, GetArenaForAllocation());
}
inline std::string* CompleteInstanceRequest::_internal_mutable_device() {
  
  return _impl_.device_.Mutable(GetArenaForAllocation());
}
inline std::string* CompleteInstanceRequest::release_device() {
  // @@protoc_insertion_point(field_release:tensorflow.CompleteInstanceRequest.device)
  return _impl_.device_.Release();
}
inline void CompleteInstanceRequest::set_allocated_device(std::string* device) {
  if (device != nullptr) {
    
  } else {
    
  }
  _impl_.device_.SetAllocated(device, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.device_.IsDefault()) {
    _impl_.device_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.CompleteInstanceRequest.device)
}

// bool is_source = 11;
inline void CompleteInstanceRequest::clear_is_source() {
  _impl_.is_source_ = false;
}
inline bool CompleteInstanceRequest::_internal_is_source() const {
  return _impl_.is_source_;
}
inline bool CompleteInstanceRequest::is_source() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.is_source)
  return _internal_is_source();
}
inline void CompleteInstanceRequest::_internal_set_is_source(bool value) {
  
  _impl_.is_source_ = value;
}
inline void CompleteInstanceRequest::set_is_source(bool value) {
  _internal_set_is_source(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.is_source)
}

// int64 step_id = 12;
inline void CompleteInstanceRequest::clear_step_id() {
  _impl_.step_id_ = int64_t{0};
}
inline int64_t CompleteInstanceRequest::_internal_step_id() const {
  return _impl_.step_id_;
}
inline int64_t CompleteInstanceRequest::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceRequest.step_id)
  return _internal_step_id();
}
inline void CompleteInstanceRequest::_internal_set_step_id(int64_t value) {
  
  _impl_.step_id_ = value;
}
inline void CompleteInstanceRequest::set_step_id(int64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceRequest.step_id)
}

// -------------------------------------------------------------------

// CompleteInstanceResponse

// int32 instance_key = 1;
inline void CompleteInstanceResponse::clear_instance_key() {
  _impl_.instance_key_ = 0;
}
inline int32_t CompleteInstanceResponse::_internal_instance_key() const {
  return _impl_.instance_key_;
}
inline int32_t CompleteInstanceResponse::instance_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.instance_key)
  return _internal_instance_key();
}
inline void CompleteInstanceResponse::_internal_set_instance_key(int32_t value) {
  
  _impl_.instance_key_ = value;
}
inline void CompleteInstanceResponse::set_instance_key(int32_t value) {
  _internal_set_instance_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.instance_key)
}

// int32 source_rank = 2;
inline void CompleteInstanceResponse::clear_source_rank() {
  _impl_.source_rank_ = 0;
}
inline int32_t CompleteInstanceResponse::_internal_source_rank() const {
  return _impl_.source_rank_;
}
inline int32_t CompleteInstanceResponse::source_rank() const {
  // @@protoc_insertion_point(field_get:tensorflow.CompleteInstanceResponse.source_rank)
  return _internal_source_rank();
}
inline void CompleteInstanceResponse::_internal_set_source_rank(int32_t value) {
  
  _impl_.source_rank_ = value;
}
inline void CompleteInstanceResponse::set_source_rank(int32_t value) {
  _internal_set_source_rank(value);
  // @@protoc_insertion_point(field_set:tensorflow.CompleteInstanceResponse.source_rank)
}

// -------------------------------------------------------------------

// GetStepSequenceRequest

// repeated int64 graph_key = 1;
inline int GetStepSequenceRequest::_internal_graph_key_size() const {
  return _impl_.graph_key_.size();
}
inline int GetStepSequenceRequest::graph_key_size() const {
  return _internal_graph_key_size();
}
inline void GetStepSequenceRequest::clear_graph_key() {
  _impl_.graph_key_.Clear();
}
inline int64_t GetStepSequenceRequest::_internal_graph_key(int index) const {
  return _impl_.graph_key_.Get(index);
}
inline int64_t GetStepSequenceRequest::graph_key(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStepSequenceRequest.graph_key)
  return _internal_graph_key(index);
}
inline void GetStepSequenceRequest::set_graph_key(int index, int64_t value) {
  _impl_.graph_key_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.GetStepSequenceRequest.graph_key)
}
inline void GetStepSequenceRequest::_internal_add_graph_key(int64_t value) {
  _impl_.graph_key_.Add(value);
}
inline void GetStepSequenceRequest::add_graph_key(int64_t value) {
  _internal_add_graph_key(value);
  // @@protoc_insertion_point(field_add:tensorflow.GetStepSequenceRequest.graph_key)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GetStepSequenceRequest::_internal_graph_key() const {
  return _impl_.graph_key_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
GetStepSequenceRequest::graph_key() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStepSequenceRequest.graph_key)
  return _internal_graph_key();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GetStepSequenceRequest::_internal_mutable_graph_key() {
  return &_impl_.graph_key_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
GetStepSequenceRequest::mutable_graph_key() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStepSequenceRequest.graph_key)
  return _internal_mutable_graph_key();
}

// -------------------------------------------------------------------

// StepSequence

// int64 graph_key = 1;
inline void StepSequence::clear_graph_key() {
  _impl_.graph_key_ = int64_t{0};
}
inline int64_t StepSequence::_internal_graph_key() const {
  return _impl_.graph_key_;
}
inline int64_t StepSequence::graph_key() const {
  // @@protoc_insertion_point(field_get:tensorflow.StepSequence.graph_key)
  return _internal_graph_key();
}
inline void StepSequence::_internal_set_graph_key(int64_t value) {
  
  _impl_.graph_key_ = value;
}
inline void StepSequence::set_graph_key(int64_t value) {
  _internal_set_graph_key(value);
  // @@protoc_insertion_point(field_set:tensorflow.StepSequence.graph_key)
}

// int64 next_step_id = 2;
inline void StepSequence::clear_next_step_id() {
  _impl_.next_step_id_ = int64_t{0};
}
inline int64_t StepSequence::_internal_next_step_id() const {
  return _impl_.next_step_id_;
}
inline int64_t StepSequence::next_step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.StepSequence.next_step_id)
  return _internal_next_step_id();
}
inline void StepSequence::_internal_set_next_step_id(int64_t value) {
  
  _impl_.next_step_id_ = value;
}
inline void StepSequence::set_next_step_id(int64_t value) {
  _internal_set_next_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.StepSequence.next_step_id)
}

// -------------------------------------------------------------------

// GetStepSequenceResponse

// repeated .tensorflow.StepSequence step_sequence = 1;
inline int GetStepSequenceResponse::_internal_step_sequence_size() const {
  return _impl_.step_sequence_.size();
}
inline int GetStepSequenceResponse::step_sequence_size() const {
  return _internal_step_sequence_size();
}
inline void GetStepSequenceResponse::clear_step_sequence() {
  _impl_.step_sequence_.Clear();
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::mutable_step_sequence(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GetStepSequenceResponse.step_sequence)
  return _impl_.step_sequence_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >*
GetStepSequenceResponse::mutable_step_sequence() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GetStepSequenceResponse.step_sequence)
  return &_impl_.step_sequence_;
}
inline const ::tensorflow::StepSequence& GetStepSequenceResponse::_internal_step_sequence(int index) const {
  return _impl_.step_sequence_.Get(index);
}
inline const ::tensorflow::StepSequence& GetStepSequenceResponse::step_sequence(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GetStepSequenceResponse.step_sequence)
  return _internal_step_sequence(index);
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::_internal_add_step_sequence() {
  return _impl_.step_sequence_.Add();
}
inline ::tensorflow::StepSequence* GetStepSequenceResponse::add_step_sequence() {
  ::tensorflow::StepSequence* _add = _internal_add_step_sequence();
  // @@protoc_insertion_point(field_add:tensorflow.GetStepSequenceResponse.step_sequence)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StepSequence >&
GetStepSequenceResponse::step_sequence() const {
  // @@protoc_insertion_point(field_list:tensorflow.GetStepSequenceResponse.step_sequence)
  return _impl_.step_sequence_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fworker_2eproto
