#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
皮膚分析命令行工具
支持拖拽圖片文件進行皮膚分析，自動生成分析報告
"""

import sys
import os
import cv2
import mediapipe as mp
import numpy as np
from PIL import Image
from skimage import feature
import argparse
from datetime import datetime
import json
from tensorflow.keras.models import load_model
from tensorflow.keras.preprocessing import image as tf_image

class SkinAnalyzer:
    def __init__(self, model_path='more_data(3).h5'):
        """初始化皮膚分析器"""
        print("🔄 正在初始化皮膚分析器...")

        # 初始化 MediaPipe
        print("📦 載入 MediaPipe...")
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils

        # 載入深度學習模型
        print("🧠 載入深度學習模型...")
        try:
            # 設置 TensorFlow 日誌級別
            os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
            self.model = load_model(model_path, compile=False)
            print("✅ 模型載入成功!")
        except Exception as e:
            print(f"❌ 模型載入失敗: {e}")
            print("⚠️  將繼續進行其他分析，但無法預測年齡")
            self.model = None

        # 創建輸出資料夾
        self.output_dir = "output"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            print(f"📁 創建輸出資料夾: {self.output_dir}")

    def compute_lbp_texture(self, image):
        """計算 LBP 紋理特徵"""
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        radius = 2
        n_points = 24
        lbp = feature.local_binary_pattern(gray, n_points, radius, method="uniform")
        lbp_hist, _ = np.histogram(lbp.ravel(), bins=np.arange(0, n_points + 3), range=(0, n_points + 2))
        lbp_hist = lbp_hist.astype('float')
        return np.sum(lbp_hist)

    def count_wrinkles_and_spots(self, roi):
        """檢測皺紋和斑點"""
        gray_roi = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
        bilateral = cv2.bilateralFilter(gray_roi, 9, 80, 80)
        edges = cv2.Canny(bilateral, 50, 150)
        
        wrinkles = np.sum(edges > 0)
        
        # 使用自適應閾值
        thresh1 = cv2.adaptiveThreshold(bilateral, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY_INV, 11, 2)
        
        # 形態學操作
        kernel = np.ones((3,3), np.uint8)
        thresh1 = cv2.morphologyEx(thresh1, cv2.MORPH_OPEN, kernel, iterations=2)
        thresh1 = cv2.morphologyEx(thresh1, cv2.MORPH_CLOSE, kernel, iterations=2)
        
        contours, _ = cv2.findContours(thresh1, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # 過濾小的輪廓
        min_spot_area = 4
        spots = len([cnt for cnt in contours if cv2.contourArea(cnt) > min_spot_area])
        
        return wrinkles, spots

    def draw_landmarks(self, image):
        """繪製面部關鍵點"""
        results = self.face_mesh.process(image)
        landmarks_image = np.zeros_like(image, dtype=np.uint8)
        
        if results.multi_face_landmarks:
            for landmarks in results.multi_face_landmarks:
                for connection in self.mp_face_mesh.FACEMESH_TESSELATION:
                    start_idx = connection[0]
                    end_idx = connection[1]

                    start_point = (int(landmarks.landmark[start_idx].x * image.shape[1]),
                                   int(landmarks.landmark[start_idx].y * image.shape[0]))
                    end_point = (int(landmarks.landmark[end_idx].x * image.shape[1]),
                                 int(landmarks.landmark[end_idx].y * image.shape[0]))

                    cv2.line(landmarks_image, start_point, end_point, (220, 220, 220), 1, lineType=cv2.LINE_AA)
                    cv2.circle(landmarks_image, start_point, 1, (127, 127, 127), -1)

        # 應用模糊效果
        landmarks_image = cv2.GaussianBlur(landmarks_image, (3, 3), 0)
        
        # 混合原圖和關鍵點圖像
        alpha = 0.35
        blended_image = cv2.addWeighted(image, 1 - alpha, landmarks_image, alpha, 0)
        
        return blended_image

    def predict_age(self, image_path):
        """預測皮膚年齡"""
        if self.model is None:
            return "模型未載入"
        
        try:
            # 載入並預處理圖像
            test_img = tf_image.load_img(image_path, target_size=(180, 180))
            test_img = tf_image.img_to_array(test_img)
            test_img = np.expand_dims(test_img, axis=0)
            test_img /= 255
            
            # 預測
            age_pred = self.model.predict(test_img, verbose=0)
            x = age_pred[0][0]
            rounded_age_value = round(x)
            return f"約 {rounded_age_value} 歲"
        except Exception as e:
            return f"年齡預測失敗: {e}"

    def min_max_scale(self, value, min_value, max_value):
        """Min-Max 標準化"""
        return (value - min_value) / (max_value - min_value) * 100

    def analyze_image(self, image_path):
        """分析圖像"""
        print(f"🔍 正在分析圖像: {os.path.basename(image_path)}")
        
        try:
            # 載入圖像
            image = Image.open(image_path).convert("RGB")
            frame = np.array(image)
            if frame.shape[2] == 4:
                frame = frame[:, :, :3]

            # 特徵分析
            wrinkles, spots = self.count_wrinkles_and_spots(frame)
            texture = self.compute_lbp_texture(frame)
            
            # 繪製關鍵點
            landmarks_frame = self.draw_landmarks(frame.copy())
            
            # 年齡預測
            predicted_age = self.predict_age(image_path)
            
            # 標準化分數
            MAX_WRINKLES = 100000
            MAX_SPOTS = 100000  
            MAX_TEXTURE = 100000
            
            scaled_wrinkles = self.min_max_scale(wrinkles, 0, MAX_WRINKLES)
            scaled_spots = self.min_max_scale(spots, 0, MAX_SPOTS)
            scaled_texture = self.min_max_scale(texture, 0, MAX_TEXTURE)
            
            # 生成分析結果
            analysis_result = {
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path,
                'raw_features': {
                    'wrinkles': int(wrinkles),
                    'spots': int(spots),
                    'texture': float(texture)
                },
                'scaled_scores': {
                    'wrinkles_score': round(scaled_wrinkles, 2),
                    'spots_score': round(scaled_spots, 2),
                    'texture_score': round(scaled_texture, 2)
                },
                'predicted_age': predicted_age
            }
            
            return analysis_result, landmarks_frame
            
        except Exception as e:
            print(f"❌ 分析失敗: {e}")
            return None, None

    def generate_report(self, analysis_result, output_filename):
        """生成分析報告"""
        if analysis_result is None:
            return None
            
        report_path = os.path.join(self.output_dir, f"{output_filename}_report.json")
        
        # 保存 JSON 報告
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, ensure_ascii=False, indent=2)
        
        # 生成文字報告
        text_report_path = os.path.join(self.output_dir, f"{output_filename}_report.txt")
        with open(text_report_path, 'w', encoding='utf-8') as f:
            f.write("=" * 50 + "\n")
            f.write("           皮膚分析報告\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"分析時間: {analysis_result['timestamp']}\n")
            f.write(f"圖像文件: {os.path.basename(analysis_result['image_path'])}\n\n")
            
            f.write("🎯 分析結果:\n")
            f.write("-" * 30 + "\n")
            f.write(f"預測年齡: {analysis_result['predicted_age']}\n\n")
            
            f.write("📊 皮膚特徵評分 (0-100):\n")
            f.write("-" * 30 + "\n")
            f.write(f"皺紋評分: {analysis_result['scaled_scores']['wrinkles_score']:.2f}\n")
            f.write(f"斑點評分: {analysis_result['scaled_scores']['spots_score']:.2f}\n")
            f.write(f"紋理評分: {analysis_result['scaled_scores']['texture_score']:.2f}\n\n")
            
            f.write("🔬 原始數據:\n")
            f.write("-" * 30 + "\n")
            f.write(f"皺紋檢測數量: {analysis_result['raw_features']['wrinkles']}\n")
            f.write(f"斑點檢測數量: {analysis_result['raw_features']['spots']}\n")
            f.write(f"紋理複雜度: {analysis_result['raw_features']['texture']:.2f}\n\n")
            
            f.write("📝 評分說明:\n")
            f.write("-" * 30 + "\n")
            f.write("• 分數越高表示該特徵越明顯\n")
            f.write("• 皺紋評分: 反映皮膚皺紋程度\n")
            f.write("• 斑點評分: 反映皮膚斑點密度\n")
            f.write("• 紋理評分: 反映皮膚紋理複雜度\n")
        
        return report_path, text_report_path

    def save_processed_image(self, landmarks_frame, output_filename):
        """保存處理後的圖像"""
        image_path = os.path.join(self.output_dir, f"{output_filename}_analyzed.jpg")
        cv2.imwrite(image_path, cv2.cvtColor(landmarks_frame, cv2.COLOR_RGB2BGR))
        return image_path

def main():
    print("🎨 MySkin.ai 皮膚分析工具 - 命令行版本")
    print("=" * 50)
    
    # 處理命令行參數
    if len(sys.argv) > 1:
        image_path = sys.argv[1].strip('"')  # 移除可能的引號
    else:
        print("📝 使用方法:")
        print("1. 直接拖拽圖片文件到終端窗口")
        print("2. 或者輸入圖片路徑:")
        image_path = input("請輸入圖片路徑: ").strip('"')
    
    # 檢查文件是否存在
    if not os.path.exists(image_path):
        print(f"❌ 文件不存在: {image_path}")
        return
    
    # 檢查文件格式
    valid_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    if not any(image_path.lower().endswith(ext) for ext in valid_extensions):
        print(f"❌ 不支持的文件格式。支持的格式: {', '.join(valid_extensions)}")
        return
    
    # 初始化分析器
    analyzer = SkinAnalyzer()
    
    # 執行分析
    analysis_result, landmarks_frame = analyzer.analyze_image(image_path)
    
    if analysis_result is None:
        print("❌ 分析失敗")
        return
    
    # 生成輸出文件名
    base_filename = os.path.splitext(os.path.basename(image_path))[0]
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"{base_filename}_{timestamp}"
    
    # 保存結果
    print("💾 正在保存分析結果...")
    
    # 保存處理後的圖像
    processed_image_path = analyzer.save_processed_image(landmarks_frame, output_filename)
    
    # 生成報告
    json_report_path, text_report_path = analyzer.generate_report(analysis_result, output_filename)
    
    # 顯示結果
    print("\n✅ 分析完成!")
    print("=" * 50)
    print(f"🎯 預測年齡: {analysis_result['predicted_age']}")
    print(f"📊 皺紋評分: {analysis_result['scaled_scores']['wrinkles_score']:.2f}/100")
    print(f"📊 斑點評分: {analysis_result['scaled_scores']['spots_score']:.2f}/100") 
    print(f"📊 紋理評分: {analysis_result['scaled_scores']['texture_score']:.2f}/100")
    print("\n📁 輸出文件:")
    print(f"• 分析圖像: {processed_image_path}")
    print(f"• 詳細報告: {text_report_path}")
    print(f"• JSON數據: {json_report_path}")
    print(f"\n所有文件已保存到 '{analyzer.output_dir}' 資料夾")
    
    input("\n按 Enter 鍵退出...")

if __name__ == "__main__":
    main()
