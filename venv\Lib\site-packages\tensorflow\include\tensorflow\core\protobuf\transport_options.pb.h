// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/transport_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto;
namespace tensorflow {
class RecvBufRespExtra;
struct RecvBufRespExtraDefaultTypeInternal;
extern RecvBufRespExtraDefaultTypeInternal _RecvBufRespExtra_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::RecvBufRespExtra* Arena::CreateMaybeMessage<::tensorflow::RecvBufRespExtra>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class RecvBufRespExtra final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RecvBufRespExtra) */ {
 public:
  inline RecvBufRespExtra() : RecvBufRespExtra(nullptr) {}
  ~RecvBufRespExtra() override;
  explicit PROTOBUF_CONSTEXPR RecvBufRespExtra(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RecvBufRespExtra(const RecvBufRespExtra& from);
  RecvBufRespExtra(RecvBufRespExtra&& from) noexcept
    : RecvBufRespExtra() {
    *this = ::std::move(from);
  }

  inline RecvBufRespExtra& operator=(const RecvBufRespExtra& from) {
    CopyFrom(from);
    return *this;
  }
  inline RecvBufRespExtra& operator=(RecvBufRespExtra&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RecvBufRespExtra& default_instance() {
    return *internal_default_instance();
  }
  static inline const RecvBufRespExtra* internal_default_instance() {
    return reinterpret_cast<const RecvBufRespExtra*>(
               &_RecvBufRespExtra_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RecvBufRespExtra& a, RecvBufRespExtra& b) {
    a.Swap(&b);
  }
  inline void Swap(RecvBufRespExtra* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RecvBufRespExtra* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RecvBufRespExtra* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RecvBufRespExtra>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RecvBufRespExtra& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RecvBufRespExtra& from) {
    RecvBufRespExtra::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RecvBufRespExtra* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RecvBufRespExtra";
  }
  protected:
  explicit RecvBufRespExtra(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTensorContentFieldNumber = 1,
  };
  // repeated bytes tensor_content = 1;
  int tensor_content_size() const;
  private:
  int _internal_tensor_content_size() const;
  public:
  void clear_tensor_content();
  const std::string& tensor_content(int index) const;
  std::string* mutable_tensor_content(int index);
  void set_tensor_content(int index, const std::string& value);
  void set_tensor_content(int index, std::string&& value);
  void set_tensor_content(int index, const char* value);
  void set_tensor_content(int index, const void* value, size_t size);
  std::string* add_tensor_content();
  void add_tensor_content(const std::string& value);
  void add_tensor_content(std::string&& value);
  void add_tensor_content(const char* value);
  void add_tensor_content(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& tensor_content() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_tensor_content();
  private:
  const std::string& _internal_tensor_content(int index) const;
  std::string* _internal_add_tensor_content();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RecvBufRespExtra)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> tensor_content_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RecvBufRespExtra

// repeated bytes tensor_content = 1;
inline int RecvBufRespExtra::_internal_tensor_content_size() const {
  return _impl_.tensor_content_.size();
}
inline int RecvBufRespExtra::tensor_content_size() const {
  return _internal_tensor_content_size();
}
inline void RecvBufRespExtra::clear_tensor_content() {
  _impl_.tensor_content_.Clear();
}
inline std::string* RecvBufRespExtra::add_tensor_content() {
  std::string* _s = _internal_add_tensor_content();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RecvBufRespExtra.tensor_content)
  return _s;
}
inline const std::string& RecvBufRespExtra::_internal_tensor_content(int index) const {
  return _impl_.tensor_content_.Get(index);
}
inline const std::string& RecvBufRespExtra::tensor_content(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RecvBufRespExtra.tensor_content)
  return _internal_tensor_content(index);
}
inline std::string* RecvBufRespExtra::mutable_tensor_content(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RecvBufRespExtra.tensor_content)
  return _impl_.tensor_content_.Mutable(index);
}
inline void RecvBufRespExtra::set_tensor_content(int index, const std::string& value) {
  _impl_.tensor_content_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRespExtra.tensor_content)
}
inline void RecvBufRespExtra::set_tensor_content(int index, std::string&& value) {
  _impl_.tensor_content_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RecvBufRespExtra.tensor_content)
}
inline void RecvBufRespExtra::set_tensor_content(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tensor_content_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RecvBufRespExtra.tensor_content)
}
inline void RecvBufRespExtra::set_tensor_content(int index, const void* value, size_t size) {
  _impl_.tensor_content_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RecvBufRespExtra.tensor_content)
}
inline std::string* RecvBufRespExtra::_internal_add_tensor_content() {
  return _impl_.tensor_content_.Add();
}
inline void RecvBufRespExtra::add_tensor_content(const std::string& value) {
  _impl_.tensor_content_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RecvBufRespExtra.tensor_content)
}
inline void RecvBufRespExtra::add_tensor_content(std::string&& value) {
  _impl_.tensor_content_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RecvBufRespExtra.tensor_content)
}
inline void RecvBufRespExtra::add_tensor_content(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.tensor_content_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RecvBufRespExtra.tensor_content)
}
inline void RecvBufRespExtra::add_tensor_content(const void* value, size_t size) {
  _impl_.tensor_content_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RecvBufRespExtra.tensor_content)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RecvBufRespExtra::tensor_content() const {
  // @@protoc_insertion_point(field_list:tensorflow.RecvBufRespExtra.tensor_content)
  return _impl_.tensor_content_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RecvBufRespExtra::mutable_tensor_content() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RecvBufRespExtra.tensor_content)
  return &_impl_.tensor_content_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftransport_5foptions_2eproto
