// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/data_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto;
namespace tensorflow {
namespace data {
class CrossTrainerCacheOptions;
struct CrossTrainerCacheOptionsDefaultTypeInternal;
extern CrossTrainerCacheOptionsDefaultTypeInternal _CrossTrainerCacheOptions_default_instance_;
class DataServiceConfig;
struct DataServiceConfigDefaultTypeInternal;
extern DataServiceConfigDefaultTypeInternal _DataServiceConfig_default_instance_;
class DataServiceMetadata;
struct DataServiceMetadataDefaultTypeInternal;
extern DataServiceMetadataDefaultTypeInternal _DataServiceMetadata_default_instance_;
class ProcessingModeDef;
struct ProcessingModeDefDefaultTypeInternal;
extern ProcessingModeDefDefaultTypeInternal _ProcessingModeDef_default_instance_;
}  // namespace data
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::data::CrossTrainerCacheOptions* Arena::CreateMaybeMessage<::tensorflow::data::CrossTrainerCacheOptions>(Arena*);
template<> ::tensorflow::data::DataServiceConfig* Arena::CreateMaybeMessage<::tensorflow::data::DataServiceConfig>(Arena*);
template<> ::tensorflow::data::DataServiceMetadata* Arena::CreateMaybeMessage<::tensorflow::data::DataServiceMetadata>(Arena*);
template<> ::tensorflow::data::ProcessingModeDef* Arena::CreateMaybeMessage<::tensorflow::data::ProcessingModeDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace data {

enum ProcessingModeDef_ShardingPolicy : int {
  ProcessingModeDef_ShardingPolicy_OFF = 0,
  ProcessingModeDef_ShardingPolicy_DYNAMIC = 1,
  ProcessingModeDef_ShardingPolicy_FILE = 2,
  ProcessingModeDef_ShardingPolicy_DATA = 3,
  ProcessingModeDef_ShardingPolicy_FILE_OR_DATA = 4,
  ProcessingModeDef_ShardingPolicy_HINT = 5,
  ProcessingModeDef_ShardingPolicy_ProcessingModeDef_ShardingPolicy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ProcessingModeDef_ShardingPolicy_ProcessingModeDef_ShardingPolicy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ProcessingModeDef_ShardingPolicy_IsValid(int value);
constexpr ProcessingModeDef_ShardingPolicy ProcessingModeDef_ShardingPolicy_ShardingPolicy_MIN = ProcessingModeDef_ShardingPolicy_OFF;
constexpr ProcessingModeDef_ShardingPolicy ProcessingModeDef_ShardingPolicy_ShardingPolicy_MAX = ProcessingModeDef_ShardingPolicy_HINT;
constexpr int ProcessingModeDef_ShardingPolicy_ShardingPolicy_ARRAYSIZE = ProcessingModeDef_ShardingPolicy_ShardingPolicy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProcessingModeDef_ShardingPolicy_descriptor();
template<typename T>
inline const std::string& ProcessingModeDef_ShardingPolicy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ProcessingModeDef_ShardingPolicy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ProcessingModeDef_ShardingPolicy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ProcessingModeDef_ShardingPolicy_descriptor(), enum_t_value);
}
inline bool ProcessingModeDef_ShardingPolicy_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ProcessingModeDef_ShardingPolicy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ProcessingModeDef_ShardingPolicy>(
    ProcessingModeDef_ShardingPolicy_descriptor(), name, value);
}
enum DataServiceMetadata_Compression : int {
  DataServiceMetadata_Compression_COMPRESSION_UNSPECIFIED = 0,
  DataServiceMetadata_Compression_COMPRESSION_OFF = 1,
  DataServiceMetadata_Compression_COMPRESSION_SNAPPY = 2,
  DataServiceMetadata_Compression_DataServiceMetadata_Compression_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataServiceMetadata_Compression_DataServiceMetadata_Compression_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataServiceMetadata_Compression_IsValid(int value);
constexpr DataServiceMetadata_Compression DataServiceMetadata_Compression_Compression_MIN = DataServiceMetadata_Compression_COMPRESSION_UNSPECIFIED;
constexpr DataServiceMetadata_Compression DataServiceMetadata_Compression_Compression_MAX = DataServiceMetadata_Compression_COMPRESSION_SNAPPY;
constexpr int DataServiceMetadata_Compression_Compression_ARRAYSIZE = DataServiceMetadata_Compression_Compression_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataServiceMetadata_Compression_descriptor();
template<typename T>
inline const std::string& DataServiceMetadata_Compression_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataServiceMetadata_Compression>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataServiceMetadata_Compression_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataServiceMetadata_Compression_descriptor(), enum_t_value);
}
inline bool DataServiceMetadata_Compression_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataServiceMetadata_Compression* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataServiceMetadata_Compression>(
    DataServiceMetadata_Compression_descriptor(), name, value);
}
enum DeploymentMode : int {
  DEPLOYMENT_MODE_UNSPECIFIED = 0,
  DEPLOYMENT_MODE_COLOCATED = 1,
  DEPLOYMENT_MODE_REMOTE = 2,
  DEPLOYMENT_MODE_HYBRID = 3,
  DeploymentMode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DeploymentMode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DeploymentMode_IsValid(int value);
constexpr DeploymentMode DeploymentMode_MIN = DEPLOYMENT_MODE_UNSPECIFIED;
constexpr DeploymentMode DeploymentMode_MAX = DEPLOYMENT_MODE_HYBRID;
constexpr int DeploymentMode_ARRAYSIZE = DeploymentMode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DeploymentMode_descriptor();
template<typename T>
inline const std::string& DeploymentMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DeploymentMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DeploymentMode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DeploymentMode_descriptor(), enum_t_value);
}
inline bool DeploymentMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DeploymentMode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DeploymentMode>(
    DeploymentMode_descriptor(), name, value);
}
// ===================================================================

class ProcessingModeDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.ProcessingModeDef) */ {
 public:
  inline ProcessingModeDef() : ProcessingModeDef(nullptr) {}
  ~ProcessingModeDef() override;
  explicit PROTOBUF_CONSTEXPR ProcessingModeDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProcessingModeDef(const ProcessingModeDef& from);
  ProcessingModeDef(ProcessingModeDef&& from) noexcept
    : ProcessingModeDef() {
    *this = ::std::move(from);
  }

  inline ProcessingModeDef& operator=(const ProcessingModeDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProcessingModeDef& operator=(ProcessingModeDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProcessingModeDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProcessingModeDef* internal_default_instance() {
    return reinterpret_cast<const ProcessingModeDef*>(
               &_ProcessingModeDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProcessingModeDef& a, ProcessingModeDef& b) {
    a.Swap(&b);
  }
  inline void Swap(ProcessingModeDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProcessingModeDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProcessingModeDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProcessingModeDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProcessingModeDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProcessingModeDef& from) {
    ProcessingModeDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProcessingModeDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.ProcessingModeDef";
  }
  protected:
  explicit ProcessingModeDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ProcessingModeDef_ShardingPolicy ShardingPolicy;
  static constexpr ShardingPolicy OFF =
    ProcessingModeDef_ShardingPolicy_OFF;
  static constexpr ShardingPolicy DYNAMIC =
    ProcessingModeDef_ShardingPolicy_DYNAMIC;
  static constexpr ShardingPolicy FILE =
    ProcessingModeDef_ShardingPolicy_FILE;
  static constexpr ShardingPolicy DATA =
    ProcessingModeDef_ShardingPolicy_DATA;
  static constexpr ShardingPolicy FILE_OR_DATA =
    ProcessingModeDef_ShardingPolicy_FILE_OR_DATA;
  static constexpr ShardingPolicy HINT =
    ProcessingModeDef_ShardingPolicy_HINT;
  static inline bool ShardingPolicy_IsValid(int value) {
    return ProcessingModeDef_ShardingPolicy_IsValid(value);
  }
  static constexpr ShardingPolicy ShardingPolicy_MIN =
    ProcessingModeDef_ShardingPolicy_ShardingPolicy_MIN;
  static constexpr ShardingPolicy ShardingPolicy_MAX =
    ProcessingModeDef_ShardingPolicy_ShardingPolicy_MAX;
  static constexpr int ShardingPolicy_ARRAYSIZE =
    ProcessingModeDef_ShardingPolicy_ShardingPolicy_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ShardingPolicy_descriptor() {
    return ProcessingModeDef_ShardingPolicy_descriptor();
  }
  template<typename T>
  static inline const std::string& ShardingPolicy_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ShardingPolicy>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ShardingPolicy_Name.");
    return ProcessingModeDef_ShardingPolicy_Name(enum_t_value);
  }
  static inline bool ShardingPolicy_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ShardingPolicy* value) {
    return ProcessingModeDef_ShardingPolicy_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kShardingPolicyFieldNumber = 1,
  };
  // .tensorflow.data.ProcessingModeDef.ShardingPolicy sharding_policy = 1;
  void clear_sharding_policy();
  ::tensorflow::data::ProcessingModeDef_ShardingPolicy sharding_policy() const;
  void set_sharding_policy(::tensorflow::data::ProcessingModeDef_ShardingPolicy value);
  private:
  ::tensorflow::data::ProcessingModeDef_ShardingPolicy _internal_sharding_policy() const;
  void _internal_set_sharding_policy(::tensorflow::data::ProcessingModeDef_ShardingPolicy value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.ProcessingModeDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int sharding_policy_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto;
};
// -------------------------------------------------------------------

class DataServiceMetadata final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.DataServiceMetadata) */ {
 public:
  inline DataServiceMetadata() : DataServiceMetadata(nullptr) {}
  ~DataServiceMetadata() override;
  explicit PROTOBUF_CONSTEXPR DataServiceMetadata(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataServiceMetadata(const DataServiceMetadata& from);
  DataServiceMetadata(DataServiceMetadata&& from) noexcept
    : DataServiceMetadata() {
    *this = ::std::move(from);
  }

  inline DataServiceMetadata& operator=(const DataServiceMetadata& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataServiceMetadata& operator=(DataServiceMetadata&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataServiceMetadata& default_instance() {
    return *internal_default_instance();
  }
  enum OptionalElementSpecCase {
    kElementSpec = 1,
    OPTIONAL_ELEMENT_SPEC_NOT_SET = 0,
  };

  static inline const DataServiceMetadata* internal_default_instance() {
    return reinterpret_cast<const DataServiceMetadata*>(
               &_DataServiceMetadata_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DataServiceMetadata& a, DataServiceMetadata& b) {
    a.Swap(&b);
  }
  inline void Swap(DataServiceMetadata* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataServiceMetadata* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataServiceMetadata* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataServiceMetadata>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataServiceMetadata& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataServiceMetadata& from) {
    DataServiceMetadata::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataServiceMetadata* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.DataServiceMetadata";
  }
  protected:
  explicit DataServiceMetadata(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef DataServiceMetadata_Compression Compression;
  static constexpr Compression COMPRESSION_UNSPECIFIED =
    DataServiceMetadata_Compression_COMPRESSION_UNSPECIFIED;
  static constexpr Compression COMPRESSION_OFF =
    DataServiceMetadata_Compression_COMPRESSION_OFF;
  static constexpr Compression COMPRESSION_SNAPPY =
    DataServiceMetadata_Compression_COMPRESSION_SNAPPY;
  static inline bool Compression_IsValid(int value) {
    return DataServiceMetadata_Compression_IsValid(value);
  }
  static constexpr Compression Compression_MIN =
    DataServiceMetadata_Compression_Compression_MIN;
  static constexpr Compression Compression_MAX =
    DataServiceMetadata_Compression_Compression_MAX;
  static constexpr int Compression_ARRAYSIZE =
    DataServiceMetadata_Compression_Compression_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Compression_descriptor() {
    return DataServiceMetadata_Compression_descriptor();
  }
  template<typename T>
  static inline const std::string& Compression_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Compression>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Compression_Name.");
    return DataServiceMetadata_Compression_Name(enum_t_value);
  }
  static inline bool Compression_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Compression* value) {
    return DataServiceMetadata_Compression_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kCardinalityFieldNumber = 3,
    kCompressionFieldNumber = 2,
    kElementSpecFieldNumber = 1,
  };
  // int64 cardinality = 3;
  void clear_cardinality();
  int64_t cardinality() const;
  void set_cardinality(int64_t value);
  private:
  int64_t _internal_cardinality() const;
  void _internal_set_cardinality(int64_t value);
  public:

  // .tensorflow.data.DataServiceMetadata.Compression compression = 2;
  void clear_compression();
  ::tensorflow::data::DataServiceMetadata_Compression compression() const;
  void set_compression(::tensorflow::data::DataServiceMetadata_Compression value);
  private:
  ::tensorflow::data::DataServiceMetadata_Compression _internal_compression() const;
  void _internal_set_compression(::tensorflow::data::DataServiceMetadata_Compression value);
  public:

  // bytes element_spec = 1;
  bool has_element_spec() const;
  private:
  bool _internal_has_element_spec() const;
  public:
  void clear_element_spec();
  const std::string& element_spec() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_element_spec(ArgT0&& arg0, ArgT... args);
  std::string* mutable_element_spec();
  PROTOBUF_NODISCARD std::string* release_element_spec();
  void set_allocated_element_spec(std::string* element_spec);
  private:
  const std::string& _internal_element_spec() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_element_spec(const std::string& value);
  std::string* _internal_mutable_element_spec();
  public:

  void clear_optional_element_spec();
  OptionalElementSpecCase optional_element_spec_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.data.DataServiceMetadata)
 private:
  class _Internal;
  void set_has_element_spec();

  inline bool has_optional_element_spec() const;
  inline void clear_has_optional_element_spec();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t cardinality_;
    int compression_;
    union OptionalElementSpecUnion {
      constexpr OptionalElementSpecUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr element_spec_;
    } optional_element_spec_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto;
};
// -------------------------------------------------------------------

class CrossTrainerCacheOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.CrossTrainerCacheOptions) */ {
 public:
  inline CrossTrainerCacheOptions() : CrossTrainerCacheOptions(nullptr) {}
  ~CrossTrainerCacheOptions() override;
  explicit PROTOBUF_CONSTEXPR CrossTrainerCacheOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CrossTrainerCacheOptions(const CrossTrainerCacheOptions& from);
  CrossTrainerCacheOptions(CrossTrainerCacheOptions&& from) noexcept
    : CrossTrainerCacheOptions() {
    *this = ::std::move(from);
  }

  inline CrossTrainerCacheOptions& operator=(const CrossTrainerCacheOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CrossTrainerCacheOptions& operator=(CrossTrainerCacheOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CrossTrainerCacheOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const CrossTrainerCacheOptions* internal_default_instance() {
    return reinterpret_cast<const CrossTrainerCacheOptions*>(
               &_CrossTrainerCacheOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CrossTrainerCacheOptions& a, CrossTrainerCacheOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CrossTrainerCacheOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CrossTrainerCacheOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CrossTrainerCacheOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CrossTrainerCacheOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CrossTrainerCacheOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CrossTrainerCacheOptions& from) {
    CrossTrainerCacheOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CrossTrainerCacheOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.CrossTrainerCacheOptions";
  }
  protected:
  explicit CrossTrainerCacheOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTrainerIdFieldNumber = 1,
  };
  // string trainer_id = 1;
  void clear_trainer_id();
  const std::string& trainer_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_trainer_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_trainer_id();
  PROTOBUF_NODISCARD std::string* release_trainer_id();
  void set_allocated_trainer_id(std::string* trainer_id);
  private:
  const std::string& _internal_trainer_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_trainer_id(const std::string& value);
  std::string* _internal_mutable_trainer_id();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.CrossTrainerCacheOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr trainer_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto;
};
// -------------------------------------------------------------------

class DataServiceConfig final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.data.DataServiceConfig) */ {
 public:
  inline DataServiceConfig() : DataServiceConfig(nullptr) {}
  ~DataServiceConfig() override;
  explicit PROTOBUF_CONSTEXPR DataServiceConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataServiceConfig(const DataServiceConfig& from);
  DataServiceConfig(DataServiceConfig&& from) noexcept
    : DataServiceConfig() {
    *this = ::std::move(from);
  }

  inline DataServiceConfig& operator=(const DataServiceConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataServiceConfig& operator=(DataServiceConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataServiceConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataServiceConfig* internal_default_instance() {
    return reinterpret_cast<const DataServiceConfig*>(
               &_DataServiceConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DataServiceConfig& a, DataServiceConfig& b) {
    a.Swap(&b);
  }
  inline void Swap(DataServiceConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataServiceConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataServiceConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataServiceConfig>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataServiceConfig& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DataServiceConfig& from) {
    DataServiceConfig::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataServiceConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.data.DataServiceConfig";
  }
  protected:
  explicit DataServiceConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeploymentModeFieldNumber = 1,
  };
  // .tensorflow.data.DeploymentMode deployment_mode = 1;
  void clear_deployment_mode();
  ::tensorflow::data::DeploymentMode deployment_mode() const;
  void set_deployment_mode(::tensorflow::data::DeploymentMode value);
  private:
  ::tensorflow::data::DeploymentMode _internal_deployment_mode() const;
  void _internal_set_deployment_mode(::tensorflow::data::DeploymentMode value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.data.DataServiceConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int deployment_mode_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProcessingModeDef

// .tensorflow.data.ProcessingModeDef.ShardingPolicy sharding_policy = 1;
inline void ProcessingModeDef::clear_sharding_policy() {
  _impl_.sharding_policy_ = 0;
}
inline ::tensorflow::data::ProcessingModeDef_ShardingPolicy ProcessingModeDef::_internal_sharding_policy() const {
  return static_cast< ::tensorflow::data::ProcessingModeDef_ShardingPolicy >(_impl_.sharding_policy_);
}
inline ::tensorflow::data::ProcessingModeDef_ShardingPolicy ProcessingModeDef::sharding_policy() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.ProcessingModeDef.sharding_policy)
  return _internal_sharding_policy();
}
inline void ProcessingModeDef::_internal_set_sharding_policy(::tensorflow::data::ProcessingModeDef_ShardingPolicy value) {
  
  _impl_.sharding_policy_ = value;
}
inline void ProcessingModeDef::set_sharding_policy(::tensorflow::data::ProcessingModeDef_ShardingPolicy value) {
  _internal_set_sharding_policy(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.ProcessingModeDef.sharding_policy)
}

// -------------------------------------------------------------------

// DataServiceMetadata

// bytes element_spec = 1;
inline bool DataServiceMetadata::_internal_has_element_spec() const {
  return optional_element_spec_case() == kElementSpec;
}
inline bool DataServiceMetadata::has_element_spec() const {
  return _internal_has_element_spec();
}
inline void DataServiceMetadata::set_has_element_spec() {
  _impl_._oneof_case_[0] = kElementSpec;
}
inline void DataServiceMetadata::clear_element_spec() {
  if (_internal_has_element_spec()) {
    _impl_.optional_element_spec_.element_spec_.Destroy();
    clear_has_optional_element_spec();
  }
}
inline const std::string& DataServiceMetadata::element_spec() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DataServiceMetadata.element_spec)
  return _internal_element_spec();
}
template <typename ArgT0, typename... ArgT>
inline void DataServiceMetadata::set_element_spec(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_element_spec()) {
    clear_optional_element_spec();
    set_has_element_spec();
    _impl_.optional_element_spec_.element_spec_.InitDefault();
  }
  _impl_.optional_element_spec_.element_spec_.SetBytes( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.DataServiceMetadata.element_spec)
}
inline std::string* DataServiceMetadata::mutable_element_spec() {
  std::string* _s = _internal_mutable_element_spec();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.DataServiceMetadata.element_spec)
  return _s;
}
inline const std::string& DataServiceMetadata::_internal_element_spec() const {
  if (_internal_has_element_spec()) {
    return _impl_.optional_element_spec_.element_spec_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void DataServiceMetadata::_internal_set_element_spec(const std::string& value) {
  if (!_internal_has_element_spec()) {
    clear_optional_element_spec();
    set_has_element_spec();
    _impl_.optional_element_spec_.element_spec_.InitDefault();
  }
  _impl_.optional_element_spec_.element_spec_.Set(value, GetArenaForAllocation());
}
inline std::string* DataServiceMetadata::_internal_mutable_element_spec() {
  if (!_internal_has_element_spec()) {
    clear_optional_element_spec();
    set_has_element_spec();
    _impl_.optional_element_spec_.element_spec_.InitDefault();
  }
  return _impl_.optional_element_spec_.element_spec_.Mutable(      GetArenaForAllocation());
}
inline std::string* DataServiceMetadata::release_element_spec() {
  // @@protoc_insertion_point(field_release:tensorflow.data.DataServiceMetadata.element_spec)
  if (_internal_has_element_spec()) {
    clear_has_optional_element_spec();
    return _impl_.optional_element_spec_.element_spec_.Release();
  } else {
    return nullptr;
  }
}
inline void DataServiceMetadata::set_allocated_element_spec(std::string* element_spec) {
  if (has_optional_element_spec()) {
    clear_optional_element_spec();
  }
  if (element_spec != nullptr) {
    set_has_element_spec();
    _impl_.optional_element_spec_.element_spec_.InitAllocated(element_spec, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.DataServiceMetadata.element_spec)
}

// .tensorflow.data.DataServiceMetadata.Compression compression = 2;
inline void DataServiceMetadata::clear_compression() {
  _impl_.compression_ = 0;
}
inline ::tensorflow::data::DataServiceMetadata_Compression DataServiceMetadata::_internal_compression() const {
  return static_cast< ::tensorflow::data::DataServiceMetadata_Compression >(_impl_.compression_);
}
inline ::tensorflow::data::DataServiceMetadata_Compression DataServiceMetadata::compression() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DataServiceMetadata.compression)
  return _internal_compression();
}
inline void DataServiceMetadata::_internal_set_compression(::tensorflow::data::DataServiceMetadata_Compression value) {
  
  _impl_.compression_ = value;
}
inline void DataServiceMetadata::set_compression(::tensorflow::data::DataServiceMetadata_Compression value) {
  _internal_set_compression(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.DataServiceMetadata.compression)
}

// int64 cardinality = 3;
inline void DataServiceMetadata::clear_cardinality() {
  _impl_.cardinality_ = int64_t{0};
}
inline int64_t DataServiceMetadata::_internal_cardinality() const {
  return _impl_.cardinality_;
}
inline int64_t DataServiceMetadata::cardinality() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DataServiceMetadata.cardinality)
  return _internal_cardinality();
}
inline void DataServiceMetadata::_internal_set_cardinality(int64_t value) {
  
  _impl_.cardinality_ = value;
}
inline void DataServiceMetadata::set_cardinality(int64_t value) {
  _internal_set_cardinality(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.DataServiceMetadata.cardinality)
}

inline bool DataServiceMetadata::has_optional_element_spec() const {
  return optional_element_spec_case() != OPTIONAL_ELEMENT_SPEC_NOT_SET;
}
inline void DataServiceMetadata::clear_has_optional_element_spec() {
  _impl_._oneof_case_[0] = OPTIONAL_ELEMENT_SPEC_NOT_SET;
}
inline DataServiceMetadata::OptionalElementSpecCase DataServiceMetadata::optional_element_spec_case() const {
  return DataServiceMetadata::OptionalElementSpecCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// CrossTrainerCacheOptions

// string trainer_id = 1;
inline void CrossTrainerCacheOptions::clear_trainer_id() {
  _impl_.trainer_id_.ClearToEmpty();
}
inline const std::string& CrossTrainerCacheOptions::trainer_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.CrossTrainerCacheOptions.trainer_id)
  return _internal_trainer_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CrossTrainerCacheOptions::set_trainer_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.trainer_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.data.CrossTrainerCacheOptions.trainer_id)
}
inline std::string* CrossTrainerCacheOptions::mutable_trainer_id() {
  std::string* _s = _internal_mutable_trainer_id();
  // @@protoc_insertion_point(field_mutable:tensorflow.data.CrossTrainerCacheOptions.trainer_id)
  return _s;
}
inline const std::string& CrossTrainerCacheOptions::_internal_trainer_id() const {
  return _impl_.trainer_id_.Get();
}
inline void CrossTrainerCacheOptions::_internal_set_trainer_id(const std::string& value) {
  
  _impl_.trainer_id_.Set(value, GetArenaForAllocation());
}
inline std::string* CrossTrainerCacheOptions::_internal_mutable_trainer_id() {
  
  return _impl_.trainer_id_.Mutable(GetArenaForAllocation());
}
inline std::string* CrossTrainerCacheOptions::release_trainer_id() {
  // @@protoc_insertion_point(field_release:tensorflow.data.CrossTrainerCacheOptions.trainer_id)
  return _impl_.trainer_id_.Release();
}
inline void CrossTrainerCacheOptions::set_allocated_trainer_id(std::string* trainer_id) {
  if (trainer_id != nullptr) {
    
  } else {
    
  }
  _impl_.trainer_id_.SetAllocated(trainer_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.trainer_id_.IsDefault()) {
    _impl_.trainer_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.data.CrossTrainerCacheOptions.trainer_id)
}

// -------------------------------------------------------------------

// DataServiceConfig

// .tensorflow.data.DeploymentMode deployment_mode = 1;
inline void DataServiceConfig::clear_deployment_mode() {
  _impl_.deployment_mode_ = 0;
}
inline ::tensorflow::data::DeploymentMode DataServiceConfig::_internal_deployment_mode() const {
  return static_cast< ::tensorflow::data::DeploymentMode >(_impl_.deployment_mode_);
}
inline ::tensorflow::data::DeploymentMode DataServiceConfig::deployment_mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.data.DataServiceConfig.deployment_mode)
  return _internal_deployment_mode();
}
inline void DataServiceConfig::_internal_set_deployment_mode(::tensorflow::data::DeploymentMode value) {
  
  _impl_.deployment_mode_ = value;
}
inline void DataServiceConfig::set_deployment_mode(::tensorflow::data::DeploymentMode value) {
  _internal_set_deployment_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.data.DataServiceConfig.deployment_mode)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace data
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::data::ProcessingModeDef_ShardingPolicy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::ProcessingModeDef_ShardingPolicy>() {
  return ::tensorflow::data::ProcessingModeDef_ShardingPolicy_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::DataServiceMetadata_Compression> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::DataServiceMetadata_Compression>() {
  return ::tensorflow::data::DataServiceMetadata_Compression_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::data::DeploymentMode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::data::DeploymentMode>() {
  return ::tensorflow::data::DeploymentMode_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdata_5fservice_2eproto
