../../Scripts/deepface.exe,sha256=8xSqMHzrVEPyHPxtWfs1GN1oIVeVPI8_ewaWAKJQl1U,108419
deepface-0.0.79.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
deepface-0.0.79.dist-info/LICENSE,sha256=tHGofNLmdaY7uuHTIK4uhEB99Lr7WxuHNbOytI3qZ5Q,1076
deepface-0.0.79.dist-info/METADATA,sha256=rnxQNc3DSiogHaZJcGQVnfU-Hm0NYo8U138tyLUg5Ek,26592
deepface-0.0.79.dist-info/RECORD,,
deepface-0.0.79.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface-0.0.79.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
deepface-0.0.79.dist-info/entry_points.txt,sha256=FtXy6R6kxpM46UZyI7o2xIOQwIyZT3_X7Rxiz02PBO0,51
deepface-0.0.79.dist-info/top_level.txt,sha256=AUkc4BVMe9KB4tfumnhBHQNANROa-is9C6k6hB6Ga0c,9
deepface/DeepFace.py,sha256=j1ec3o1uXq_XetWYq_U-6I1xdByv8kD1ZQ5RiJfcSXQ,29034
deepface/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/__pycache__/DeepFace.cpython-311.pyc,,
deepface/__pycache__/__init__.cpython-311.pyc,,
deepface/basemodels/ArcFace.py,sha256=4vwX2e1ZIRx35s8lkzFLgrYVZ4HrOHyE5tleuHse2DQ,4428
deepface/basemodels/DeepID.py,sha256=zlFyW_vNgScFyPeH6gVECrKoUPuNIgzcz84lEI1lOLA,2134
deepface/basemodels/DlibResNet.py,sha256=jaUXa8GuwWavzHZlBKCUjLIeaNaGfej3yvOwlZ4kpP0,2254
deepface/basemodels/DlibWrapper.py,sha256=HV-NhVvFU9r-p6b2DestWw7nvFRWYJXkqJt2qiKMHdU,97
deepface/basemodels/Facenet.py,sha256=cOTru4nKrDCrUy4THvbVPyCQ4Aus478YbS_c2QAZgyw,59157
deepface/basemodels/Facenet512.py,sha256=M2R-m-kt2NHnBGhP-9ypsdh6k3_EkoCGnorF-53yny0,771
deepface/basemodels/FbDeepFace.py,sha256=VOxJQL_be9zAtpuSqfhYbPAeej-HBaap9_Uliiw8JW4,2553
deepface/basemodels/OpenFace.py,sha256=UMRmH-iewJcbkFT-ZA3PrS8ADhMSbjLHXbXZ73e72C4,16727
deepface/basemodels/SFace.py,sha256=RVTgAsEtY5XKR67pZKVpVXCCj7NmK7yxaAdWF2kFg04,1237
deepface/basemodels/VGGFace.py,sha256=9VZ3zg7PzTLDLaZawPCVFxW2UBE5M3ihbzEzYplQmsU,3382
deepface/basemodels/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/basemodels/__pycache__/ArcFace.cpython-311.pyc,,
deepface/basemodels/__pycache__/DeepID.cpython-311.pyc,,
deepface/basemodels/__pycache__/DlibResNet.cpython-311.pyc,,
deepface/basemodels/__pycache__/DlibWrapper.cpython-311.pyc,,
deepface/basemodels/__pycache__/Facenet.cpython-311.pyc,,
deepface/basemodels/__pycache__/Facenet512.cpython-311.pyc,,
deepface/basemodels/__pycache__/FbDeepFace.cpython-311.pyc,,
deepface/basemodels/__pycache__/OpenFace.cpython-311.pyc,,
deepface/basemodels/__pycache__/SFace.cpython-311.pyc,,
deepface/basemodels/__pycache__/VGGFace.cpython-311.pyc,,
deepface/basemodels/__pycache__/__init__.cpython-311.pyc,,
deepface/commons/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/commons/__pycache__/__init__.cpython-311.pyc,,
deepface/commons/__pycache__/distance.cpython-311.pyc,,
deepface/commons/__pycache__/functions.cpython-311.pyc,,
deepface/commons/__pycache__/realtime.cpython-311.pyc,,
deepface/commons/distance.py,sha256=H3t-gy1vxgnskSR_QJOcvVZNtupQGQG1lOFKzkxinMY,1923
deepface/commons/functions.py,sha256=zH3yMGKslBZGCePQNyW3DO2KpEUA3Z493vXxrC-h1gg,8679
deepface/commons/realtime.py,sha256=D4NCifUAo5uga_lfFqnDUWYDZAdgT3FEjC56ulqEt4s,33864
deepface/detectors/DlibWrapper.py,sha256=04iINq_3tunlBLjTltBFZ6R7AujEhUfLlCvvZc73Jw4,2299
deepface/detectors/FaceDetector.py,sha256=n6obQYY4W0qHWkic98U-xoRSuRZwcLHHn-2kEVdvdpY,3656
deepface/detectors/MediapipeWrapper.py,sha256=YbBcSyHXc-E_PNAjL20CaaVr7ltKjSaayvs4tVCQhEQ,2033
deepface/detectors/MtcnnWrapper.py,sha256=_I7wnX_13Cv4dQlorlwY3HTdO3ehNojDvdv6Rr_qZaY,1076
deepface/detectors/OpenCvWrapper.py,sha256=TZXk0ArVMHt_bZ6oQ6p5o_23W6924XOc4ygrCVW_btw,3878
deepface/detectors/RetinaFaceWrapper.py,sha256=rHVQP4sHGKfWdMOVGLyKM6qnThnCAdJZMnXxcU4K3dU,1594
deepface/detectors/SsdWrapper.py,sha256=CLM6EExnTGG1y5aGLgeubpNm-CQD4Z2MoVeva9KRTjc,3640
deepface/detectors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/detectors/__pycache__/DlibWrapper.cpython-311.pyc,,
deepface/detectors/__pycache__/FaceDetector.cpython-311.pyc,,
deepface/detectors/__pycache__/MediapipeWrapper.cpython-311.pyc,,
deepface/detectors/__pycache__/MtcnnWrapper.cpython-311.pyc,,
deepface/detectors/__pycache__/OpenCvWrapper.cpython-311.pyc,,
deepface/detectors/__pycache__/RetinaFaceWrapper.cpython-311.pyc,,
deepface/detectors/__pycache__/SsdWrapper.cpython-311.pyc,,
deepface/detectors/__pycache__/__init__.cpython-311.pyc,,
deepface/extendedmodels/Age.py,sha256=MLupasCZ5-FIbqdgGAtxHxP_xkROcYlKJb86qDElSjw,1835
deepface/extendedmodels/Emotion.py,sha256=MdjUna9KtwVqRF9ZpLu7zZEuhs9Kjj73FDTkO-llMK4,2377
deepface/extendedmodels/Gender.py,sha256=2E35j_Cc7Hugsdts83Egf589ADM5GyH2CGLmwrTDWJM,1781
deepface/extendedmodels/Race.py,sha256=iWBN7KDiKaog_rD6Zg_vPboNnkoSOrWk6aWBr86aeS8,1823
deepface/extendedmodels/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/extendedmodels/__pycache__/Age.cpython-311.pyc,,
deepface/extendedmodels/__pycache__/Emotion.cpython-311.pyc,,
deepface/extendedmodels/__pycache__/Gender.cpython-311.pyc,,
deepface/extendedmodels/__pycache__/Race.cpython-311.pyc,,
deepface/extendedmodels/__pycache__/__init__.cpython-311.pyc,,
deepface/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
deepface/models/__pycache__/__init__.cpython-311.pyc,,
