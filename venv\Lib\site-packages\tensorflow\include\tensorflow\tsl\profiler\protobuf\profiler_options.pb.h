// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/profiler/protobuf/profiler_options.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto;
namespace tensorflow {
class ProfileOptions;
struct ProfileOptionsDefaultTypeInternal;
extern ProfileOptionsDefaultTypeInternal _ProfileOptions_default_instance_;
class RemoteProfilerSessionManagerOptions;
struct RemoteProfilerSessionManagerOptionsDefaultTypeInternal;
extern RemoteProfilerSessionManagerOptionsDefaultTypeInternal _RemoteProfilerSessionManagerOptions_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ProfileOptions* Arena::CreateMaybeMessage<::tensorflow::ProfileOptions>(Arena*);
template<> ::tensorflow::RemoteProfilerSessionManagerOptions* Arena::CreateMaybeMessage<::tensorflow::RemoteProfilerSessionManagerOptions>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum ProfileOptions_DeviceType : int {
  ProfileOptions_DeviceType_UNSPECIFIED = 0,
  ProfileOptions_DeviceType_CPU = 1,
  ProfileOptions_DeviceType_GPU = 2,
  ProfileOptions_DeviceType_TPU = 3,
  ProfileOptions_DeviceType_PLUGGABLE_DEVICE = 4,
  ProfileOptions_DeviceType_ProfileOptions_DeviceType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ProfileOptions_DeviceType_ProfileOptions_DeviceType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ProfileOptions_DeviceType_IsValid(int value);
constexpr ProfileOptions_DeviceType ProfileOptions_DeviceType_DeviceType_MIN = ProfileOptions_DeviceType_UNSPECIFIED;
constexpr ProfileOptions_DeviceType ProfileOptions_DeviceType_DeviceType_MAX = ProfileOptions_DeviceType_PLUGGABLE_DEVICE;
constexpr int ProfileOptions_DeviceType_DeviceType_ARRAYSIZE = ProfileOptions_DeviceType_DeviceType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProfileOptions_DeviceType_descriptor();
template<typename T>
inline const std::string& ProfileOptions_DeviceType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ProfileOptions_DeviceType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ProfileOptions_DeviceType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ProfileOptions_DeviceType_descriptor(), enum_t_value);
}
inline bool ProfileOptions_DeviceType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ProfileOptions_DeviceType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ProfileOptions_DeviceType>(
    ProfileOptions_DeviceType_descriptor(), name, value);
}
// ===================================================================

class ProfileOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ProfileOptions) */ {
 public:
  inline ProfileOptions() : ProfileOptions(nullptr) {}
  ~ProfileOptions() override;
  explicit PROTOBUF_CONSTEXPR ProfileOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileOptions(const ProfileOptions& from);
  ProfileOptions(ProfileOptions&& from) noexcept
    : ProfileOptions() {
    *this = ::std::move(from);
  }

  inline ProfileOptions& operator=(const ProfileOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileOptions& operator=(ProfileOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileOptions* internal_default_instance() {
    return reinterpret_cast<const ProfileOptions*>(
               &_ProfileOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProfileOptions& a, ProfileOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProfileOptions& from) {
    ProfileOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ProfileOptions";
  }
  protected:
  explicit ProfileOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef ProfileOptions_DeviceType DeviceType;
  static constexpr DeviceType UNSPECIFIED =
    ProfileOptions_DeviceType_UNSPECIFIED;
  static constexpr DeviceType CPU =
    ProfileOptions_DeviceType_CPU;
  static constexpr DeviceType GPU =
    ProfileOptions_DeviceType_GPU;
  static constexpr DeviceType TPU =
    ProfileOptions_DeviceType_TPU;
  static constexpr DeviceType PLUGGABLE_DEVICE =
    ProfileOptions_DeviceType_PLUGGABLE_DEVICE;
  static inline bool DeviceType_IsValid(int value) {
    return ProfileOptions_DeviceType_IsValid(value);
  }
  static constexpr DeviceType DeviceType_MIN =
    ProfileOptions_DeviceType_DeviceType_MIN;
  static constexpr DeviceType DeviceType_MAX =
    ProfileOptions_DeviceType_DeviceType_MAX;
  static constexpr int DeviceType_ARRAYSIZE =
    ProfileOptions_DeviceType_DeviceType_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  DeviceType_descriptor() {
    return ProfileOptions_DeviceType_descriptor();
  }
  template<typename T>
  static inline const std::string& DeviceType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, DeviceType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function DeviceType_Name.");
    return ProfileOptions_DeviceType_Name(enum_t_value);
  }
  static inline bool DeviceType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      DeviceType* value) {
    return ProfileOptions_DeviceType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kRepositoryPathFieldNumber = 10,
    kHostTracerLevelFieldNumber = 2,
    kDeviceTracerLevelFieldNumber = 3,
    kPythonTracerLevelFieldNumber = 4,
    kIncludeDatasetOpsFieldNumber = 1,
    kEnableHloProtoFieldNumber = 7,
    kVersionFieldNumber = 5,
    kDeviceTypeFieldNumber = 6,
    kStartTimestampNsFieldNumber = 8,
    kDurationMsFieldNumber = 9,
  };
  // string repository_path = 10;
  void clear_repository_path();
  const std::string& repository_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_repository_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_repository_path();
  PROTOBUF_NODISCARD std::string* release_repository_path();
  void set_allocated_repository_path(std::string* repository_path);
  private:
  const std::string& _internal_repository_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_repository_path(const std::string& value);
  std::string* _internal_mutable_repository_path();
  public:

  // uint32 host_tracer_level = 2;
  void clear_host_tracer_level();
  uint32_t host_tracer_level() const;
  void set_host_tracer_level(uint32_t value);
  private:
  uint32_t _internal_host_tracer_level() const;
  void _internal_set_host_tracer_level(uint32_t value);
  public:

  // uint32 device_tracer_level = 3;
  void clear_device_tracer_level();
  uint32_t device_tracer_level() const;
  void set_device_tracer_level(uint32_t value);
  private:
  uint32_t _internal_device_tracer_level() const;
  void _internal_set_device_tracer_level(uint32_t value);
  public:

  // uint32 python_tracer_level = 4;
  void clear_python_tracer_level();
  uint32_t python_tracer_level() const;
  void set_python_tracer_level(uint32_t value);
  private:
  uint32_t _internal_python_tracer_level() const;
  void _internal_set_python_tracer_level(uint32_t value);
  public:

  // bool include_dataset_ops = 1;
  void clear_include_dataset_ops();
  bool include_dataset_ops() const;
  void set_include_dataset_ops(bool value);
  private:
  bool _internal_include_dataset_ops() const;
  void _internal_set_include_dataset_ops(bool value);
  public:

  // bool enable_hlo_proto = 7;
  void clear_enable_hlo_proto();
  bool enable_hlo_proto() const;
  void set_enable_hlo_proto(bool value);
  private:
  bool _internal_enable_hlo_proto() const;
  void _internal_set_enable_hlo_proto(bool value);
  public:

  // uint32 version = 5;
  void clear_version();
  uint32_t version() const;
  void set_version(uint32_t value);
  private:
  uint32_t _internal_version() const;
  void _internal_set_version(uint32_t value);
  public:

  // .tensorflow.ProfileOptions.DeviceType device_type = 6;
  void clear_device_type();
  ::tensorflow::ProfileOptions_DeviceType device_type() const;
  void set_device_type(::tensorflow::ProfileOptions_DeviceType value);
  private:
  ::tensorflow::ProfileOptions_DeviceType _internal_device_type() const;
  void _internal_set_device_type(::tensorflow::ProfileOptions_DeviceType value);
  public:

  // uint64 start_timestamp_ns = 8;
  void clear_start_timestamp_ns();
  uint64_t start_timestamp_ns() const;
  void set_start_timestamp_ns(uint64_t value);
  private:
  uint64_t _internal_start_timestamp_ns() const;
  void _internal_set_start_timestamp_ns(uint64_t value);
  public:

  // uint64 duration_ms = 9;
  void clear_duration_ms();
  uint64_t duration_ms() const;
  void set_duration_ms(uint64_t value);
  private:
  uint64_t _internal_duration_ms() const;
  void _internal_set_duration_ms(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.ProfileOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr repository_path_;
    uint32_t host_tracer_level_;
    uint32_t device_tracer_level_;
    uint32_t python_tracer_level_;
    bool include_dataset_ops_;
    bool enable_hlo_proto_;
    uint32_t version_;
    int device_type_;
    uint64_t start_timestamp_ns_;
    uint64_t duration_ms_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto;
};
// -------------------------------------------------------------------

class RemoteProfilerSessionManagerOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.RemoteProfilerSessionManagerOptions) */ {
 public:
  inline RemoteProfilerSessionManagerOptions() : RemoteProfilerSessionManagerOptions(nullptr) {}
  ~RemoteProfilerSessionManagerOptions() override;
  explicit PROTOBUF_CONSTEXPR RemoteProfilerSessionManagerOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RemoteProfilerSessionManagerOptions(const RemoteProfilerSessionManagerOptions& from);
  RemoteProfilerSessionManagerOptions(RemoteProfilerSessionManagerOptions&& from) noexcept
    : RemoteProfilerSessionManagerOptions() {
    *this = ::std::move(from);
  }

  inline RemoteProfilerSessionManagerOptions& operator=(const RemoteProfilerSessionManagerOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline RemoteProfilerSessionManagerOptions& operator=(RemoteProfilerSessionManagerOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RemoteProfilerSessionManagerOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const RemoteProfilerSessionManagerOptions* internal_default_instance() {
    return reinterpret_cast<const RemoteProfilerSessionManagerOptions*>(
               &_RemoteProfilerSessionManagerOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(RemoteProfilerSessionManagerOptions& a, RemoteProfilerSessionManagerOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(RemoteProfilerSessionManagerOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RemoteProfilerSessionManagerOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RemoteProfilerSessionManagerOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RemoteProfilerSessionManagerOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RemoteProfilerSessionManagerOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RemoteProfilerSessionManagerOptions& from) {
    RemoteProfilerSessionManagerOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoteProfilerSessionManagerOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.RemoteProfilerSessionManagerOptions";
  }
  protected:
  explicit RemoteProfilerSessionManagerOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServiceAddressesFieldNumber = 2,
    kProfilerOptionsFieldNumber = 1,
    kSessionCreationTimestampNsFieldNumber = 3,
    kMaxSessionDurationMsFieldNumber = 4,
    kDelayMsFieldNumber = 5,
  };
  // repeated string service_addresses = 2;
  int service_addresses_size() const;
  private:
  int _internal_service_addresses_size() const;
  public:
  void clear_service_addresses();
  const std::string& service_addresses(int index) const;
  std::string* mutable_service_addresses(int index);
  void set_service_addresses(int index, const std::string& value);
  void set_service_addresses(int index, std::string&& value);
  void set_service_addresses(int index, const char* value);
  void set_service_addresses(int index, const char* value, size_t size);
  std::string* add_service_addresses();
  void add_service_addresses(const std::string& value);
  void add_service_addresses(std::string&& value);
  void add_service_addresses(const char* value);
  void add_service_addresses(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& service_addresses() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_service_addresses();
  private:
  const std::string& _internal_service_addresses(int index) const;
  std::string* _internal_add_service_addresses();
  public:

  // .tensorflow.ProfileOptions profiler_options = 1;
  bool has_profiler_options() const;
  private:
  bool _internal_has_profiler_options() const;
  public:
  void clear_profiler_options();
  const ::tensorflow::ProfileOptions& profiler_options() const;
  PROTOBUF_NODISCARD ::tensorflow::ProfileOptions* release_profiler_options();
  ::tensorflow::ProfileOptions* mutable_profiler_options();
  void set_allocated_profiler_options(::tensorflow::ProfileOptions* profiler_options);
  private:
  const ::tensorflow::ProfileOptions& _internal_profiler_options() const;
  ::tensorflow::ProfileOptions* _internal_mutable_profiler_options();
  public:
  void unsafe_arena_set_allocated_profiler_options(
      ::tensorflow::ProfileOptions* profiler_options);
  ::tensorflow::ProfileOptions* unsafe_arena_release_profiler_options();

  // uint64 session_creation_timestamp_ns = 3;
  void clear_session_creation_timestamp_ns();
  uint64_t session_creation_timestamp_ns() const;
  void set_session_creation_timestamp_ns(uint64_t value);
  private:
  uint64_t _internal_session_creation_timestamp_ns() const;
  void _internal_set_session_creation_timestamp_ns(uint64_t value);
  public:

  // uint64 max_session_duration_ms = 4;
  void clear_max_session_duration_ms();
  uint64_t max_session_duration_ms() const;
  void set_max_session_duration_ms(uint64_t value);
  private:
  uint64_t _internal_max_session_duration_ms() const;
  void _internal_set_max_session_duration_ms(uint64_t value);
  public:

  // uint64 delay_ms = 5;
  void clear_delay_ms();
  uint64_t delay_ms() const;
  void set_delay_ms(uint64_t value);
  private:
  uint64_t _internal_delay_ms() const;
  void _internal_set_delay_ms(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.RemoteProfilerSessionManagerOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> service_addresses_;
    ::tensorflow::ProfileOptions* profiler_options_;
    uint64_t session_creation_timestamp_ns_;
    uint64_t max_session_duration_ms_;
    uint64_t delay_ms_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProfileOptions

// uint32 version = 5;
inline void ProfileOptions::clear_version() {
  _impl_.version_ = 0u;
}
inline uint32_t ProfileOptions::_internal_version() const {
  return _impl_.version_;
}
inline uint32_t ProfileOptions::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.version)
  return _internal_version();
}
inline void ProfileOptions::_internal_set_version(uint32_t value) {
  
  _impl_.version_ = value;
}
inline void ProfileOptions::set_version(uint32_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.version)
}

// .tensorflow.ProfileOptions.DeviceType device_type = 6;
inline void ProfileOptions::clear_device_type() {
  _impl_.device_type_ = 0;
}
inline ::tensorflow::ProfileOptions_DeviceType ProfileOptions::_internal_device_type() const {
  return static_cast< ::tensorflow::ProfileOptions_DeviceType >(_impl_.device_type_);
}
inline ::tensorflow::ProfileOptions_DeviceType ProfileOptions::device_type() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.device_type)
  return _internal_device_type();
}
inline void ProfileOptions::_internal_set_device_type(::tensorflow::ProfileOptions_DeviceType value) {
  
  _impl_.device_type_ = value;
}
inline void ProfileOptions::set_device_type(::tensorflow::ProfileOptions_DeviceType value) {
  _internal_set_device_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.device_type)
}

// bool include_dataset_ops = 1;
inline void ProfileOptions::clear_include_dataset_ops() {
  _impl_.include_dataset_ops_ = false;
}
inline bool ProfileOptions::_internal_include_dataset_ops() const {
  return _impl_.include_dataset_ops_;
}
inline bool ProfileOptions::include_dataset_ops() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.include_dataset_ops)
  return _internal_include_dataset_ops();
}
inline void ProfileOptions::_internal_set_include_dataset_ops(bool value) {
  
  _impl_.include_dataset_ops_ = value;
}
inline void ProfileOptions::set_include_dataset_ops(bool value) {
  _internal_set_include_dataset_ops(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.include_dataset_ops)
}

// uint32 host_tracer_level = 2;
inline void ProfileOptions::clear_host_tracer_level() {
  _impl_.host_tracer_level_ = 0u;
}
inline uint32_t ProfileOptions::_internal_host_tracer_level() const {
  return _impl_.host_tracer_level_;
}
inline uint32_t ProfileOptions::host_tracer_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.host_tracer_level)
  return _internal_host_tracer_level();
}
inline void ProfileOptions::_internal_set_host_tracer_level(uint32_t value) {
  
  _impl_.host_tracer_level_ = value;
}
inline void ProfileOptions::set_host_tracer_level(uint32_t value) {
  _internal_set_host_tracer_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.host_tracer_level)
}

// uint32 device_tracer_level = 3;
inline void ProfileOptions::clear_device_tracer_level() {
  _impl_.device_tracer_level_ = 0u;
}
inline uint32_t ProfileOptions::_internal_device_tracer_level() const {
  return _impl_.device_tracer_level_;
}
inline uint32_t ProfileOptions::device_tracer_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.device_tracer_level)
  return _internal_device_tracer_level();
}
inline void ProfileOptions::_internal_set_device_tracer_level(uint32_t value) {
  
  _impl_.device_tracer_level_ = value;
}
inline void ProfileOptions::set_device_tracer_level(uint32_t value) {
  _internal_set_device_tracer_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.device_tracer_level)
}

// uint32 python_tracer_level = 4;
inline void ProfileOptions::clear_python_tracer_level() {
  _impl_.python_tracer_level_ = 0u;
}
inline uint32_t ProfileOptions::_internal_python_tracer_level() const {
  return _impl_.python_tracer_level_;
}
inline uint32_t ProfileOptions::python_tracer_level() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.python_tracer_level)
  return _internal_python_tracer_level();
}
inline void ProfileOptions::_internal_set_python_tracer_level(uint32_t value) {
  
  _impl_.python_tracer_level_ = value;
}
inline void ProfileOptions::set_python_tracer_level(uint32_t value) {
  _internal_set_python_tracer_level(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.python_tracer_level)
}

// bool enable_hlo_proto = 7;
inline void ProfileOptions::clear_enable_hlo_proto() {
  _impl_.enable_hlo_proto_ = false;
}
inline bool ProfileOptions::_internal_enable_hlo_proto() const {
  return _impl_.enable_hlo_proto_;
}
inline bool ProfileOptions::enable_hlo_proto() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.enable_hlo_proto)
  return _internal_enable_hlo_proto();
}
inline void ProfileOptions::_internal_set_enable_hlo_proto(bool value) {
  
  _impl_.enable_hlo_proto_ = value;
}
inline void ProfileOptions::set_enable_hlo_proto(bool value) {
  _internal_set_enable_hlo_proto(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.enable_hlo_proto)
}

// uint64 start_timestamp_ns = 8;
inline void ProfileOptions::clear_start_timestamp_ns() {
  _impl_.start_timestamp_ns_ = uint64_t{0u};
}
inline uint64_t ProfileOptions::_internal_start_timestamp_ns() const {
  return _impl_.start_timestamp_ns_;
}
inline uint64_t ProfileOptions::start_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.start_timestamp_ns)
  return _internal_start_timestamp_ns();
}
inline void ProfileOptions::_internal_set_start_timestamp_ns(uint64_t value) {
  
  _impl_.start_timestamp_ns_ = value;
}
inline void ProfileOptions::set_start_timestamp_ns(uint64_t value) {
  _internal_set_start_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.start_timestamp_ns)
}

// uint64 duration_ms = 9;
inline void ProfileOptions::clear_duration_ms() {
  _impl_.duration_ms_ = uint64_t{0u};
}
inline uint64_t ProfileOptions::_internal_duration_ms() const {
  return _impl_.duration_ms_;
}
inline uint64_t ProfileOptions::duration_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.duration_ms)
  return _internal_duration_ms();
}
inline void ProfileOptions::_internal_set_duration_ms(uint64_t value) {
  
  _impl_.duration_ms_ = value;
}
inline void ProfileOptions::set_duration_ms(uint64_t value) {
  _internal_set_duration_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.duration_ms)
}

// string repository_path = 10;
inline void ProfileOptions::clear_repository_path() {
  _impl_.repository_path_.ClearToEmpty();
}
inline const std::string& ProfileOptions::repository_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.ProfileOptions.repository_path)
  return _internal_repository_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ProfileOptions::set_repository_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.repository_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.ProfileOptions.repository_path)
}
inline std::string* ProfileOptions::mutable_repository_path() {
  std::string* _s = _internal_mutable_repository_path();
  // @@protoc_insertion_point(field_mutable:tensorflow.ProfileOptions.repository_path)
  return _s;
}
inline const std::string& ProfileOptions::_internal_repository_path() const {
  return _impl_.repository_path_.Get();
}
inline void ProfileOptions::_internal_set_repository_path(const std::string& value) {
  
  _impl_.repository_path_.Set(value, GetArenaForAllocation());
}
inline std::string* ProfileOptions::_internal_mutable_repository_path() {
  
  return _impl_.repository_path_.Mutable(GetArenaForAllocation());
}
inline std::string* ProfileOptions::release_repository_path() {
  // @@protoc_insertion_point(field_release:tensorflow.ProfileOptions.repository_path)
  return _impl_.repository_path_.Release();
}
inline void ProfileOptions::set_allocated_repository_path(std::string* repository_path) {
  if (repository_path != nullptr) {
    
  } else {
    
  }
  _impl_.repository_path_.SetAllocated(repository_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.repository_path_.IsDefault()) {
    _impl_.repository_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.ProfileOptions.repository_path)
}

// -------------------------------------------------------------------

// RemoteProfilerSessionManagerOptions

// .tensorflow.ProfileOptions profiler_options = 1;
inline bool RemoteProfilerSessionManagerOptions::_internal_has_profiler_options() const {
  return this != internal_default_instance() && _impl_.profiler_options_ != nullptr;
}
inline bool RemoteProfilerSessionManagerOptions::has_profiler_options() const {
  return _internal_has_profiler_options();
}
inline void RemoteProfilerSessionManagerOptions::clear_profiler_options() {
  if (GetArenaForAllocation() == nullptr && _impl_.profiler_options_ != nullptr) {
    delete _impl_.profiler_options_;
  }
  _impl_.profiler_options_ = nullptr;
}
inline const ::tensorflow::ProfileOptions& RemoteProfilerSessionManagerOptions::_internal_profiler_options() const {
  const ::tensorflow::ProfileOptions* p = _impl_.profiler_options_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::ProfileOptions&>(
      ::tensorflow::_ProfileOptions_default_instance_);
}
inline const ::tensorflow::ProfileOptions& RemoteProfilerSessionManagerOptions::profiler_options() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteProfilerSessionManagerOptions.profiler_options)
  return _internal_profiler_options();
}
inline void RemoteProfilerSessionManagerOptions::unsafe_arena_set_allocated_profiler_options(
    ::tensorflow::ProfileOptions* profiler_options) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.profiler_options_);
  }
  _impl_.profiler_options_ = profiler_options;
  if (profiler_options) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.RemoteProfilerSessionManagerOptions.profiler_options)
}
inline ::tensorflow::ProfileOptions* RemoteProfilerSessionManagerOptions::release_profiler_options() {
  
  ::tensorflow::ProfileOptions* temp = _impl_.profiler_options_;
  _impl_.profiler_options_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::ProfileOptions* RemoteProfilerSessionManagerOptions::unsafe_arena_release_profiler_options() {
  // @@protoc_insertion_point(field_release:tensorflow.RemoteProfilerSessionManagerOptions.profiler_options)
  
  ::tensorflow::ProfileOptions* temp = _impl_.profiler_options_;
  _impl_.profiler_options_ = nullptr;
  return temp;
}
inline ::tensorflow::ProfileOptions* RemoteProfilerSessionManagerOptions::_internal_mutable_profiler_options() {
  
  if (_impl_.profiler_options_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::ProfileOptions>(GetArenaForAllocation());
    _impl_.profiler_options_ = p;
  }
  return _impl_.profiler_options_;
}
inline ::tensorflow::ProfileOptions* RemoteProfilerSessionManagerOptions::mutable_profiler_options() {
  ::tensorflow::ProfileOptions* _msg = _internal_mutable_profiler_options();
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteProfilerSessionManagerOptions.profiler_options)
  return _msg;
}
inline void RemoteProfilerSessionManagerOptions::set_allocated_profiler_options(::tensorflow::ProfileOptions* profiler_options) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.profiler_options_;
  }
  if (profiler_options) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(profiler_options);
    if (message_arena != submessage_arena) {
      profiler_options = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, profiler_options, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.profiler_options_ = profiler_options;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.RemoteProfilerSessionManagerOptions.profiler_options)
}

// repeated string service_addresses = 2;
inline int RemoteProfilerSessionManagerOptions::_internal_service_addresses_size() const {
  return _impl_.service_addresses_.size();
}
inline int RemoteProfilerSessionManagerOptions::service_addresses_size() const {
  return _internal_service_addresses_size();
}
inline void RemoteProfilerSessionManagerOptions::clear_service_addresses() {
  _impl_.service_addresses_.Clear();
}
inline std::string* RemoteProfilerSessionManagerOptions::add_service_addresses() {
  std::string* _s = _internal_add_service_addresses();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
  return _s;
}
inline const std::string& RemoteProfilerSessionManagerOptions::_internal_service_addresses(int index) const {
  return _impl_.service_addresses_.Get(index);
}
inline const std::string& RemoteProfilerSessionManagerOptions::service_addresses(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
  return _internal_service_addresses(index);
}
inline std::string* RemoteProfilerSessionManagerOptions::mutable_service_addresses(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
  return _impl_.service_addresses_.Mutable(index);
}
inline void RemoteProfilerSessionManagerOptions::set_service_addresses(int index, const std::string& value) {
  _impl_.service_addresses_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline void RemoteProfilerSessionManagerOptions::set_service_addresses(int index, std::string&& value) {
  _impl_.service_addresses_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline void RemoteProfilerSessionManagerOptions::set_service_addresses(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.service_addresses_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline void RemoteProfilerSessionManagerOptions::set_service_addresses(int index, const char* value, size_t size) {
  _impl_.service_addresses_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline std::string* RemoteProfilerSessionManagerOptions::_internal_add_service_addresses() {
  return _impl_.service_addresses_.Add();
}
inline void RemoteProfilerSessionManagerOptions::add_service_addresses(const std::string& value) {
  _impl_.service_addresses_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline void RemoteProfilerSessionManagerOptions::add_service_addresses(std::string&& value) {
  _impl_.service_addresses_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline void RemoteProfilerSessionManagerOptions::add_service_addresses(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.service_addresses_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline void RemoteProfilerSessionManagerOptions::add_service_addresses(const char* value, size_t size) {
  _impl_.service_addresses_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
RemoteProfilerSessionManagerOptions::service_addresses() const {
  // @@protoc_insertion_point(field_list:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
  return _impl_.service_addresses_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
RemoteProfilerSessionManagerOptions::mutable_service_addresses() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.RemoteProfilerSessionManagerOptions.service_addresses)
  return &_impl_.service_addresses_;
}

// uint64 session_creation_timestamp_ns = 3;
inline void RemoteProfilerSessionManagerOptions::clear_session_creation_timestamp_ns() {
  _impl_.session_creation_timestamp_ns_ = uint64_t{0u};
}
inline uint64_t RemoteProfilerSessionManagerOptions::_internal_session_creation_timestamp_ns() const {
  return _impl_.session_creation_timestamp_ns_;
}
inline uint64_t RemoteProfilerSessionManagerOptions::session_creation_timestamp_ns() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteProfilerSessionManagerOptions.session_creation_timestamp_ns)
  return _internal_session_creation_timestamp_ns();
}
inline void RemoteProfilerSessionManagerOptions::_internal_set_session_creation_timestamp_ns(uint64_t value) {
  
  _impl_.session_creation_timestamp_ns_ = value;
}
inline void RemoteProfilerSessionManagerOptions::set_session_creation_timestamp_ns(uint64_t value) {
  _internal_set_session_creation_timestamp_ns(value);
  // @@protoc_insertion_point(field_set:tensorflow.RemoteProfilerSessionManagerOptions.session_creation_timestamp_ns)
}

// uint64 max_session_duration_ms = 4;
inline void RemoteProfilerSessionManagerOptions::clear_max_session_duration_ms() {
  _impl_.max_session_duration_ms_ = uint64_t{0u};
}
inline uint64_t RemoteProfilerSessionManagerOptions::_internal_max_session_duration_ms() const {
  return _impl_.max_session_duration_ms_;
}
inline uint64_t RemoteProfilerSessionManagerOptions::max_session_duration_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteProfilerSessionManagerOptions.max_session_duration_ms)
  return _internal_max_session_duration_ms();
}
inline void RemoteProfilerSessionManagerOptions::_internal_set_max_session_duration_ms(uint64_t value) {
  
  _impl_.max_session_duration_ms_ = value;
}
inline void RemoteProfilerSessionManagerOptions::set_max_session_duration_ms(uint64_t value) {
  _internal_set_max_session_duration_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.RemoteProfilerSessionManagerOptions.max_session_duration_ms)
}

// uint64 delay_ms = 5;
inline void RemoteProfilerSessionManagerOptions::clear_delay_ms() {
  _impl_.delay_ms_ = uint64_t{0u};
}
inline uint64_t RemoteProfilerSessionManagerOptions::_internal_delay_ms() const {
  return _impl_.delay_ms_;
}
inline uint64_t RemoteProfilerSessionManagerOptions::delay_ms() const {
  // @@protoc_insertion_point(field_get:tensorflow.RemoteProfilerSessionManagerOptions.delay_ms)
  return _internal_delay_ms();
}
inline void RemoteProfilerSessionManagerOptions::_internal_set_delay_ms(uint64_t value) {
  
  _impl_.delay_ms_ = value;
}
inline void RemoteProfilerSessionManagerOptions::set_delay_ms(uint64_t value) {
  _internal_set_delay_ms(value);
  // @@protoc_insertion_point(field_set:tensorflow.RemoteProfilerSessionManagerOptions.delay_ms)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::ProfileOptions_DeviceType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::ProfileOptions_DeviceType>() {
  return ::tensorflow::ProfileOptions_DeviceType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprofiler_2fprotobuf_2fprofiler_5foptions_2eproto
