syntax = "proto3";

package tensorflow.eager;

import "tensorflow/core/framework/tensor_shape.proto";
import "tensorflow/core/framework/types.proto";

option cc_enable_arenas = true;
option java_outer_classname = "RemoteTensorHandleProtos";
option java_multiple_files = true;
option java_package = "org.tensorflow.framework";
option go_package = "github.com/tensorflow/tensorflow/tensorflow/go/core/protobuf/for_core_protos_go_proto";

message ResourceDtypeAndShape {
  DataType dtype = 1;
  TensorShapeProto shape = 2;
}

message RemoteTensorHandle {
  // The ID of the operation that produced this tensor.
  int64 op_id = 1;
  // The index into the outputs of the operation that produced this tensor.
  int32 output_num = 2;
  // Device where the tensor is located. Cannot be empty.
  // For multi-device functions, it's the default device passed to placer.
  string device = 3;
  // Device of the operation producing this tensor. Can be empty if the
  // operation producing this tensor is a multi-device function.
  string op_device = 4;
  // Tensor type.
  DataType dtype = 5;
  // Optional data types and shapes of a remote resource variable.
  repeated ResourceDtypeAndShape resource_dtypes_and_shapes = 6;
}
