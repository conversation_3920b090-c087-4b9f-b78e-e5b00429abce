// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/python/framework/kythe_metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto;
namespace tensorflow {
class GeneratedCodeInfo;
struct GeneratedCodeInfoDefaultTypeInternal;
extern GeneratedCodeInfoDefaultTypeInternal _GeneratedCodeInfo_default_instance_;
class MappingRule;
struct MappingRuleDefaultTypeInternal;
extern MappingRuleDefaultTypeInternal _MappingRule_default_instance_;
class VName;
struct VNameDefaultTypeInternal;
extern VNameDefaultTypeInternal _VName_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::GeneratedCodeInfo* Arena::CreateMaybeMessage<::tensorflow::GeneratedCodeInfo>(Arena*);
template<> ::tensorflow::MappingRule* Arena::CreateMaybeMessage<::tensorflow::MappingRule>(Arena*);
template<> ::tensorflow::VName* Arena::CreateMaybeMessage<::tensorflow::VName>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum GeneratedCodeInfo_Type : int {
  GeneratedCodeInfo_Type_NONE = 0,
  GeneratedCodeInfo_Type_KYTHE0 = 1,
  GeneratedCodeInfo_Type_GeneratedCodeInfo_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  GeneratedCodeInfo_Type_GeneratedCodeInfo_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool GeneratedCodeInfo_Type_IsValid(int value);
constexpr GeneratedCodeInfo_Type GeneratedCodeInfo_Type_Type_MIN = GeneratedCodeInfo_Type_NONE;
constexpr GeneratedCodeInfo_Type GeneratedCodeInfo_Type_Type_MAX = GeneratedCodeInfo_Type_KYTHE0;
constexpr int GeneratedCodeInfo_Type_Type_ARRAYSIZE = GeneratedCodeInfo_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GeneratedCodeInfo_Type_descriptor();
template<typename T>
inline const std::string& GeneratedCodeInfo_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GeneratedCodeInfo_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GeneratedCodeInfo_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GeneratedCodeInfo_Type_descriptor(), enum_t_value);
}
inline bool GeneratedCodeInfo_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GeneratedCodeInfo_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GeneratedCodeInfo_Type>(
    GeneratedCodeInfo_Type_descriptor(), name, value);
}
enum MappingRule_Type : int {
  MappingRule_Type_NONE = 0,
  MappingRule_Type_NOP = 1,
  MappingRule_Type_ANCHOR_DEFINES = 2,
  MappingRule_Type_ANCHOR_ANCHOR = 3,
  MappingRule_Type_MappingRule_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  MappingRule_Type_MappingRule_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool MappingRule_Type_IsValid(int value);
constexpr MappingRule_Type MappingRule_Type_Type_MIN = MappingRule_Type_NONE;
constexpr MappingRule_Type MappingRule_Type_Type_MAX = MappingRule_Type_ANCHOR_ANCHOR;
constexpr int MappingRule_Type_Type_ARRAYSIZE = MappingRule_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MappingRule_Type_descriptor();
template<typename T>
inline const std::string& MappingRule_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MappingRule_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MappingRule_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    MappingRule_Type_descriptor(), enum_t_value);
}
inline bool MappingRule_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MappingRule_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<MappingRule_Type>(
    MappingRule_Type_descriptor(), name, value);
}
enum MappingRule_Semantic : int {
  MappingRule_Semantic_SEMA_NONE = 0,
  MappingRule_Semantic_SEMA_WRITE = 1,
  MappingRule_Semantic_SEMA_READ_WRITE = 2,
  MappingRule_Semantic_MappingRule_Semantic_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  MappingRule_Semantic_MappingRule_Semantic_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool MappingRule_Semantic_IsValid(int value);
constexpr MappingRule_Semantic MappingRule_Semantic_Semantic_MIN = MappingRule_Semantic_SEMA_NONE;
constexpr MappingRule_Semantic MappingRule_Semantic_Semantic_MAX = MappingRule_Semantic_SEMA_READ_WRITE;
constexpr int MappingRule_Semantic_Semantic_ARRAYSIZE = MappingRule_Semantic_Semantic_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* MappingRule_Semantic_descriptor();
template<typename T>
inline const std::string& MappingRule_Semantic_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MappingRule_Semantic>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MappingRule_Semantic_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    MappingRule_Semantic_descriptor(), enum_t_value);
}
inline bool MappingRule_Semantic_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MappingRule_Semantic* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<MappingRule_Semantic>(
    MappingRule_Semantic_descriptor(), name, value);
}
// ===================================================================

class GeneratedCodeInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.GeneratedCodeInfo) */ {
 public:
  inline GeneratedCodeInfo() : GeneratedCodeInfo(nullptr) {}
  ~GeneratedCodeInfo() override;
  explicit PROTOBUF_CONSTEXPR GeneratedCodeInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GeneratedCodeInfo(const GeneratedCodeInfo& from);
  GeneratedCodeInfo(GeneratedCodeInfo&& from) noexcept
    : GeneratedCodeInfo() {
    *this = ::std::move(from);
  }

  inline GeneratedCodeInfo& operator=(const GeneratedCodeInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline GeneratedCodeInfo& operator=(GeneratedCodeInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GeneratedCodeInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const GeneratedCodeInfo* internal_default_instance() {
    return reinterpret_cast<const GeneratedCodeInfo*>(
               &_GeneratedCodeInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GeneratedCodeInfo& a, GeneratedCodeInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(GeneratedCodeInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GeneratedCodeInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GeneratedCodeInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GeneratedCodeInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GeneratedCodeInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const GeneratedCodeInfo& from) {
    GeneratedCodeInfo::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GeneratedCodeInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.GeneratedCodeInfo";
  }
  protected:
  explicit GeneratedCodeInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GeneratedCodeInfo_Type Type;
  static constexpr Type NONE =
    GeneratedCodeInfo_Type_NONE;
  static constexpr Type KYTHE0 =
    GeneratedCodeInfo_Type_KYTHE0;
  static inline bool Type_IsValid(int value) {
    return GeneratedCodeInfo_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    GeneratedCodeInfo_Type_Type_MIN;
  static constexpr Type Type_MAX =
    GeneratedCodeInfo_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    GeneratedCodeInfo_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return GeneratedCodeInfo_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return GeneratedCodeInfo_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return GeneratedCodeInfo_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kMetaFieldNumber = 2,
    kTypeFieldNumber = 1,
  };
  // repeated .tensorflow.MappingRule meta = 2;
  int meta_size() const;
  private:
  int _internal_meta_size() const;
  public:
  void clear_meta();
  ::tensorflow::MappingRule* mutable_meta(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MappingRule >*
      mutable_meta();
  private:
  const ::tensorflow::MappingRule& _internal_meta(int index) const;
  ::tensorflow::MappingRule* _internal_add_meta();
  public:
  const ::tensorflow::MappingRule& meta(int index) const;
  ::tensorflow::MappingRule* add_meta();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MappingRule >&
      meta() const;

  // .tensorflow.GeneratedCodeInfo.Type type = 1;
  void clear_type();
  ::tensorflow::GeneratedCodeInfo_Type type() const;
  void set_type(::tensorflow::GeneratedCodeInfo_Type value);
  private:
  ::tensorflow::GeneratedCodeInfo_Type _internal_type() const;
  void _internal_set_type(::tensorflow::GeneratedCodeInfo_Type value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.GeneratedCodeInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MappingRule > meta_;
    int type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class MappingRule final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MappingRule) */ {
 public:
  inline MappingRule() : MappingRule(nullptr) {}
  ~MappingRule() override;
  explicit PROTOBUF_CONSTEXPR MappingRule(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MappingRule(const MappingRule& from);
  MappingRule(MappingRule&& from) noexcept
    : MappingRule() {
    *this = ::std::move(from);
  }

  inline MappingRule& operator=(const MappingRule& from) {
    CopyFrom(from);
    return *this;
  }
  inline MappingRule& operator=(MappingRule&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MappingRule& default_instance() {
    return *internal_default_instance();
  }
  static inline const MappingRule* internal_default_instance() {
    return reinterpret_cast<const MappingRule*>(
               &_MappingRule_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MappingRule& a, MappingRule& b) {
    a.Swap(&b);
  }
  inline void Swap(MappingRule* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MappingRule* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MappingRule* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MappingRule>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MappingRule& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MappingRule& from) {
    MappingRule::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MappingRule* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MappingRule";
  }
  protected:
  explicit MappingRule(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef MappingRule_Type Type;
  static constexpr Type NONE =
    MappingRule_Type_NONE;
  static constexpr Type NOP =
    MappingRule_Type_NOP;
  static constexpr Type ANCHOR_DEFINES =
    MappingRule_Type_ANCHOR_DEFINES;
  static constexpr Type ANCHOR_ANCHOR =
    MappingRule_Type_ANCHOR_ANCHOR;
  static inline bool Type_IsValid(int value) {
    return MappingRule_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    MappingRule_Type_Type_MIN;
  static constexpr Type Type_MAX =
    MappingRule_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    MappingRule_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return MappingRule_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return MappingRule_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return MappingRule_Type_Parse(name, value);
  }

  typedef MappingRule_Semantic Semantic;
  static constexpr Semantic SEMA_NONE =
    MappingRule_Semantic_SEMA_NONE;
  static constexpr Semantic SEMA_WRITE =
    MappingRule_Semantic_SEMA_WRITE;
  static constexpr Semantic SEMA_READ_WRITE =
    MappingRule_Semantic_SEMA_READ_WRITE;
  static inline bool Semantic_IsValid(int value) {
    return MappingRule_Semantic_IsValid(value);
  }
  static constexpr Semantic Semantic_MIN =
    MappingRule_Semantic_Semantic_MIN;
  static constexpr Semantic Semantic_MAX =
    MappingRule_Semantic_Semantic_MAX;
  static constexpr int Semantic_ARRAYSIZE =
    MappingRule_Semantic_Semantic_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Semantic_descriptor() {
    return MappingRule_Semantic_descriptor();
  }
  template<typename T>
  static inline const std::string& Semantic_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Semantic>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Semantic_Name.");
    return MappingRule_Semantic_Name(enum_t_value);
  }
  static inline bool Semantic_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Semantic* value) {
    return MappingRule_Semantic_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kEdgeFieldNumber = 2,
    kVnameFieldNumber = 3,
    kSourceVnameFieldNumber = 6,
    kTypeFieldNumber = 1,
    kBeginFieldNumber = 4,
    kEndFieldNumber = 5,
    kSourceBeginFieldNumber = 7,
    kSourceEndFieldNumber = 8,
    kTargetBeginFieldNumber = 9,
    kTargetEndFieldNumber = 10,
    kSemanticFieldNumber = 11,
  };
  // string edge = 2;
  void clear_edge();
  const std::string& edge() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_edge(ArgT0&& arg0, ArgT... args);
  std::string* mutable_edge();
  PROTOBUF_NODISCARD std::string* release_edge();
  void set_allocated_edge(std::string* edge);
  private:
  const std::string& _internal_edge() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_edge(const std::string& value);
  std::string* _internal_mutable_edge();
  public:

  // .tensorflow.VName vname = 3;
  bool has_vname() const;
  private:
  bool _internal_has_vname() const;
  public:
  void clear_vname();
  const ::tensorflow::VName& vname() const;
  PROTOBUF_NODISCARD ::tensorflow::VName* release_vname();
  ::tensorflow::VName* mutable_vname();
  void set_allocated_vname(::tensorflow::VName* vname);
  private:
  const ::tensorflow::VName& _internal_vname() const;
  ::tensorflow::VName* _internal_mutable_vname();
  public:
  void unsafe_arena_set_allocated_vname(
      ::tensorflow::VName* vname);
  ::tensorflow::VName* unsafe_arena_release_vname();

  // .tensorflow.VName source_vname = 6;
  bool has_source_vname() const;
  private:
  bool _internal_has_source_vname() const;
  public:
  void clear_source_vname();
  const ::tensorflow::VName& source_vname() const;
  PROTOBUF_NODISCARD ::tensorflow::VName* release_source_vname();
  ::tensorflow::VName* mutable_source_vname();
  void set_allocated_source_vname(::tensorflow::VName* source_vname);
  private:
  const ::tensorflow::VName& _internal_source_vname() const;
  ::tensorflow::VName* _internal_mutable_source_vname();
  public:
  void unsafe_arena_set_allocated_source_vname(
      ::tensorflow::VName* source_vname);
  ::tensorflow::VName* unsafe_arena_release_source_vname();

  // .tensorflow.MappingRule.Type type = 1;
  void clear_type();
  ::tensorflow::MappingRule_Type type() const;
  void set_type(::tensorflow::MappingRule_Type value);
  private:
  ::tensorflow::MappingRule_Type _internal_type() const;
  void _internal_set_type(::tensorflow::MappingRule_Type value);
  public:

  // uint32 begin = 4;
  void clear_begin();
  uint32_t begin() const;
  void set_begin(uint32_t value);
  private:
  uint32_t _internal_begin() const;
  void _internal_set_begin(uint32_t value);
  public:

  // uint32 end = 5;
  void clear_end();
  uint32_t end() const;
  void set_end(uint32_t value);
  private:
  uint32_t _internal_end() const;
  void _internal_set_end(uint32_t value);
  public:

  // uint32 source_begin = 7;
  void clear_source_begin();
  uint32_t source_begin() const;
  void set_source_begin(uint32_t value);
  private:
  uint32_t _internal_source_begin() const;
  void _internal_set_source_begin(uint32_t value);
  public:

  // uint32 source_end = 8;
  void clear_source_end();
  uint32_t source_end() const;
  void set_source_end(uint32_t value);
  private:
  uint32_t _internal_source_end() const;
  void _internal_set_source_end(uint32_t value);
  public:

  // uint32 target_begin = 9;
  void clear_target_begin();
  uint32_t target_begin() const;
  void set_target_begin(uint32_t value);
  private:
  uint32_t _internal_target_begin() const;
  void _internal_set_target_begin(uint32_t value);
  public:

  // uint32 target_end = 10;
  void clear_target_end();
  uint32_t target_end() const;
  void set_target_end(uint32_t value);
  private:
  uint32_t _internal_target_end() const;
  void _internal_set_target_end(uint32_t value);
  public:

  // .tensorflow.MappingRule.Semantic semantic = 11;
  void clear_semantic();
  ::tensorflow::MappingRule_Semantic semantic() const;
  void set_semantic(::tensorflow::MappingRule_Semantic value);
  private:
  ::tensorflow::MappingRule_Semantic _internal_semantic() const;
  void _internal_set_semantic(::tensorflow::MappingRule_Semantic value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MappingRule)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr edge_;
    ::tensorflow::VName* vname_;
    ::tensorflow::VName* source_vname_;
    int type_;
    uint32_t begin_;
    uint32_t end_;
    uint32_t source_begin_;
    uint32_t source_end_;
    uint32_t target_begin_;
    uint32_t target_end_;
    int semantic_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto;
};
// -------------------------------------------------------------------

class VName final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.VName) */ {
 public:
  inline VName() : VName(nullptr) {}
  ~VName() override;
  explicit PROTOBUF_CONSTEXPR VName(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VName(const VName& from);
  VName(VName&& from) noexcept
    : VName() {
    *this = ::std::move(from);
  }

  inline VName& operator=(const VName& from) {
    CopyFrom(from);
    return *this;
  }
  inline VName& operator=(VName&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VName& default_instance() {
    return *internal_default_instance();
  }
  static inline const VName* internal_default_instance() {
    return reinterpret_cast<const VName*>(
               &_VName_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(VName& a, VName& b) {
    a.Swap(&b);
  }
  inline void Swap(VName* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VName* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VName* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VName>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VName& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const VName& from) {
    VName::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VName* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.VName";
  }
  protected:
  explicit VName(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSignatureFieldNumber = 1,
    kCorpusFieldNumber = 2,
    kRootFieldNumber = 3,
    kPathFieldNumber = 4,
    kLanguageFieldNumber = 5,
  };
  // string signature = 1;
  void clear_signature();
  const std::string& signature() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_signature(ArgT0&& arg0, ArgT... args);
  std::string* mutable_signature();
  PROTOBUF_NODISCARD std::string* release_signature();
  void set_allocated_signature(std::string* signature);
  private:
  const std::string& _internal_signature() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_signature(const std::string& value);
  std::string* _internal_mutable_signature();
  public:

  // string corpus = 2;
  void clear_corpus();
  const std::string& corpus() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_corpus(ArgT0&& arg0, ArgT... args);
  std::string* mutable_corpus();
  PROTOBUF_NODISCARD std::string* release_corpus();
  void set_allocated_corpus(std::string* corpus);
  private:
  const std::string& _internal_corpus() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_corpus(const std::string& value);
  std::string* _internal_mutable_corpus();
  public:

  // string root = 3;
  void clear_root();
  const std::string& root() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_root(ArgT0&& arg0, ArgT... args);
  std::string* mutable_root();
  PROTOBUF_NODISCARD std::string* release_root();
  void set_allocated_root(std::string* root);
  private:
  const std::string& _internal_root() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_root(const std::string& value);
  std::string* _internal_mutable_root();
  public:

  // string path = 4;
  void clear_path();
  const std::string& path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_path();
  PROTOBUF_NODISCARD std::string* release_path();
  void set_allocated_path(std::string* path);
  private:
  const std::string& _internal_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_path(const std::string& value);
  std::string* _internal_mutable_path();
  public:

  // string language = 5;
  void clear_language();
  const std::string& language() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_language(ArgT0&& arg0, ArgT... args);
  std::string* mutable_language();
  PROTOBUF_NODISCARD std::string* release_language();
  void set_allocated_language(std::string* language);
  private:
  const std::string& _internal_language() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_language(const std::string& value);
  std::string* _internal_mutable_language();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.VName)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr signature_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr corpus_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr root_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr path_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr language_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GeneratedCodeInfo

// .tensorflow.GeneratedCodeInfo.Type type = 1;
inline void GeneratedCodeInfo::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::GeneratedCodeInfo_Type GeneratedCodeInfo::_internal_type() const {
  return static_cast< ::tensorflow::GeneratedCodeInfo_Type >(_impl_.type_);
}
inline ::tensorflow::GeneratedCodeInfo_Type GeneratedCodeInfo::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.GeneratedCodeInfo.type)
  return _internal_type();
}
inline void GeneratedCodeInfo::_internal_set_type(::tensorflow::GeneratedCodeInfo_Type value) {
  
  _impl_.type_ = value;
}
inline void GeneratedCodeInfo::set_type(::tensorflow::GeneratedCodeInfo_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.GeneratedCodeInfo.type)
}

// repeated .tensorflow.MappingRule meta = 2;
inline int GeneratedCodeInfo::_internal_meta_size() const {
  return _impl_.meta_.size();
}
inline int GeneratedCodeInfo::meta_size() const {
  return _internal_meta_size();
}
inline void GeneratedCodeInfo::clear_meta() {
  _impl_.meta_.Clear();
}
inline ::tensorflow::MappingRule* GeneratedCodeInfo::mutable_meta(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.GeneratedCodeInfo.meta)
  return _impl_.meta_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MappingRule >*
GeneratedCodeInfo::mutable_meta() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.GeneratedCodeInfo.meta)
  return &_impl_.meta_;
}
inline const ::tensorflow::MappingRule& GeneratedCodeInfo::_internal_meta(int index) const {
  return _impl_.meta_.Get(index);
}
inline const ::tensorflow::MappingRule& GeneratedCodeInfo::meta(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.GeneratedCodeInfo.meta)
  return _internal_meta(index);
}
inline ::tensorflow::MappingRule* GeneratedCodeInfo::_internal_add_meta() {
  return _impl_.meta_.Add();
}
inline ::tensorflow::MappingRule* GeneratedCodeInfo::add_meta() {
  ::tensorflow::MappingRule* _add = _internal_add_meta();
  // @@protoc_insertion_point(field_add:tensorflow.GeneratedCodeInfo.meta)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MappingRule >&
GeneratedCodeInfo::meta() const {
  // @@protoc_insertion_point(field_list:tensorflow.GeneratedCodeInfo.meta)
  return _impl_.meta_;
}

// -------------------------------------------------------------------

// MappingRule

// .tensorflow.MappingRule.Type type = 1;
inline void MappingRule::clear_type() {
  _impl_.type_ = 0;
}
inline ::tensorflow::MappingRule_Type MappingRule::_internal_type() const {
  return static_cast< ::tensorflow::MappingRule_Type >(_impl_.type_);
}
inline ::tensorflow::MappingRule_Type MappingRule::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.type)
  return _internal_type();
}
inline void MappingRule::_internal_set_type(::tensorflow::MappingRule_Type value) {
  
  _impl_.type_ = value;
}
inline void MappingRule::set_type(::tensorflow::MappingRule_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.type)
}

// string edge = 2;
inline void MappingRule::clear_edge() {
  _impl_.edge_.ClearToEmpty();
}
inline const std::string& MappingRule::edge() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.edge)
  return _internal_edge();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MappingRule::set_edge(ArgT0&& arg0, ArgT... args) {
 
 _impl_.edge_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.edge)
}
inline std::string* MappingRule::mutable_edge() {
  std::string* _s = _internal_mutable_edge();
  // @@protoc_insertion_point(field_mutable:tensorflow.MappingRule.edge)
  return _s;
}
inline const std::string& MappingRule::_internal_edge() const {
  return _impl_.edge_.Get();
}
inline void MappingRule::_internal_set_edge(const std::string& value) {
  
  _impl_.edge_.Set(value, GetArenaForAllocation());
}
inline std::string* MappingRule::_internal_mutable_edge() {
  
  return _impl_.edge_.Mutable(GetArenaForAllocation());
}
inline std::string* MappingRule::release_edge() {
  // @@protoc_insertion_point(field_release:tensorflow.MappingRule.edge)
  return _impl_.edge_.Release();
}
inline void MappingRule::set_allocated_edge(std::string* edge) {
  if (edge != nullptr) {
    
  } else {
    
  }
  _impl_.edge_.SetAllocated(edge, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.edge_.IsDefault()) {
    _impl_.edge_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MappingRule.edge)
}

// .tensorflow.VName vname = 3;
inline bool MappingRule::_internal_has_vname() const {
  return this != internal_default_instance() && _impl_.vname_ != nullptr;
}
inline bool MappingRule::has_vname() const {
  return _internal_has_vname();
}
inline void MappingRule::clear_vname() {
  if (GetArenaForAllocation() == nullptr && _impl_.vname_ != nullptr) {
    delete _impl_.vname_;
  }
  _impl_.vname_ = nullptr;
}
inline const ::tensorflow::VName& MappingRule::_internal_vname() const {
  const ::tensorflow::VName* p = _impl_.vname_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VName&>(
      ::tensorflow::_VName_default_instance_);
}
inline const ::tensorflow::VName& MappingRule::vname() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.vname)
  return _internal_vname();
}
inline void MappingRule::unsafe_arena_set_allocated_vname(
    ::tensorflow::VName* vname) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.vname_);
  }
  _impl_.vname_ = vname;
  if (vname) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MappingRule.vname)
}
inline ::tensorflow::VName* MappingRule::release_vname() {
  
  ::tensorflow::VName* temp = _impl_.vname_;
  _impl_.vname_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VName* MappingRule::unsafe_arena_release_vname() {
  // @@protoc_insertion_point(field_release:tensorflow.MappingRule.vname)
  
  ::tensorflow::VName* temp = _impl_.vname_;
  _impl_.vname_ = nullptr;
  return temp;
}
inline ::tensorflow::VName* MappingRule::_internal_mutable_vname() {
  
  if (_impl_.vname_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VName>(GetArenaForAllocation());
    _impl_.vname_ = p;
  }
  return _impl_.vname_;
}
inline ::tensorflow::VName* MappingRule::mutable_vname() {
  ::tensorflow::VName* _msg = _internal_mutable_vname();
  // @@protoc_insertion_point(field_mutable:tensorflow.MappingRule.vname)
  return _msg;
}
inline void MappingRule::set_allocated_vname(::tensorflow::VName* vname) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.vname_;
  }
  if (vname) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(vname);
    if (message_arena != submessage_arena) {
      vname = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, vname, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.vname_ = vname;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MappingRule.vname)
}

// uint32 begin = 4;
inline void MappingRule::clear_begin() {
  _impl_.begin_ = 0u;
}
inline uint32_t MappingRule::_internal_begin() const {
  return _impl_.begin_;
}
inline uint32_t MappingRule::begin() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.begin)
  return _internal_begin();
}
inline void MappingRule::_internal_set_begin(uint32_t value) {
  
  _impl_.begin_ = value;
}
inline void MappingRule::set_begin(uint32_t value) {
  _internal_set_begin(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.begin)
}

// uint32 end = 5;
inline void MappingRule::clear_end() {
  _impl_.end_ = 0u;
}
inline uint32_t MappingRule::_internal_end() const {
  return _impl_.end_;
}
inline uint32_t MappingRule::end() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.end)
  return _internal_end();
}
inline void MappingRule::_internal_set_end(uint32_t value) {
  
  _impl_.end_ = value;
}
inline void MappingRule::set_end(uint32_t value) {
  _internal_set_end(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.end)
}

// .tensorflow.MappingRule.Semantic semantic = 11;
inline void MappingRule::clear_semantic() {
  _impl_.semantic_ = 0;
}
inline ::tensorflow::MappingRule_Semantic MappingRule::_internal_semantic() const {
  return static_cast< ::tensorflow::MappingRule_Semantic >(_impl_.semantic_);
}
inline ::tensorflow::MappingRule_Semantic MappingRule::semantic() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.semantic)
  return _internal_semantic();
}
inline void MappingRule::_internal_set_semantic(::tensorflow::MappingRule_Semantic value) {
  
  _impl_.semantic_ = value;
}
inline void MappingRule::set_semantic(::tensorflow::MappingRule_Semantic value) {
  _internal_set_semantic(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.semantic)
}

// .tensorflow.VName source_vname = 6;
inline bool MappingRule::_internal_has_source_vname() const {
  return this != internal_default_instance() && _impl_.source_vname_ != nullptr;
}
inline bool MappingRule::has_source_vname() const {
  return _internal_has_source_vname();
}
inline void MappingRule::clear_source_vname() {
  if (GetArenaForAllocation() == nullptr && _impl_.source_vname_ != nullptr) {
    delete _impl_.source_vname_;
  }
  _impl_.source_vname_ = nullptr;
}
inline const ::tensorflow::VName& MappingRule::_internal_source_vname() const {
  const ::tensorflow::VName* p = _impl_.source_vname_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::VName&>(
      ::tensorflow::_VName_default_instance_);
}
inline const ::tensorflow::VName& MappingRule::source_vname() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.source_vname)
  return _internal_source_vname();
}
inline void MappingRule::unsafe_arena_set_allocated_source_vname(
    ::tensorflow::VName* source_vname) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.source_vname_);
  }
  _impl_.source_vname_ = source_vname;
  if (source_vname) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MappingRule.source_vname)
}
inline ::tensorflow::VName* MappingRule::release_source_vname() {
  
  ::tensorflow::VName* temp = _impl_.source_vname_;
  _impl_.source_vname_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::VName* MappingRule::unsafe_arena_release_source_vname() {
  // @@protoc_insertion_point(field_release:tensorflow.MappingRule.source_vname)
  
  ::tensorflow::VName* temp = _impl_.source_vname_;
  _impl_.source_vname_ = nullptr;
  return temp;
}
inline ::tensorflow::VName* MappingRule::_internal_mutable_source_vname() {
  
  if (_impl_.source_vname_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::VName>(GetArenaForAllocation());
    _impl_.source_vname_ = p;
  }
  return _impl_.source_vname_;
}
inline ::tensorflow::VName* MappingRule::mutable_source_vname() {
  ::tensorflow::VName* _msg = _internal_mutable_source_vname();
  // @@protoc_insertion_point(field_mutable:tensorflow.MappingRule.source_vname)
  return _msg;
}
inline void MappingRule::set_allocated_source_vname(::tensorflow::VName* source_vname) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.source_vname_;
  }
  if (source_vname) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(source_vname);
    if (message_arena != submessage_arena) {
      source_vname = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, source_vname, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.source_vname_ = source_vname;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MappingRule.source_vname)
}

// uint32 source_begin = 7;
inline void MappingRule::clear_source_begin() {
  _impl_.source_begin_ = 0u;
}
inline uint32_t MappingRule::_internal_source_begin() const {
  return _impl_.source_begin_;
}
inline uint32_t MappingRule::source_begin() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.source_begin)
  return _internal_source_begin();
}
inline void MappingRule::_internal_set_source_begin(uint32_t value) {
  
  _impl_.source_begin_ = value;
}
inline void MappingRule::set_source_begin(uint32_t value) {
  _internal_set_source_begin(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.source_begin)
}

// uint32 source_end = 8;
inline void MappingRule::clear_source_end() {
  _impl_.source_end_ = 0u;
}
inline uint32_t MappingRule::_internal_source_end() const {
  return _impl_.source_end_;
}
inline uint32_t MappingRule::source_end() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.source_end)
  return _internal_source_end();
}
inline void MappingRule::_internal_set_source_end(uint32_t value) {
  
  _impl_.source_end_ = value;
}
inline void MappingRule::set_source_end(uint32_t value) {
  _internal_set_source_end(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.source_end)
}

// uint32 target_begin = 9;
inline void MappingRule::clear_target_begin() {
  _impl_.target_begin_ = 0u;
}
inline uint32_t MappingRule::_internal_target_begin() const {
  return _impl_.target_begin_;
}
inline uint32_t MappingRule::target_begin() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.target_begin)
  return _internal_target_begin();
}
inline void MappingRule::_internal_set_target_begin(uint32_t value) {
  
  _impl_.target_begin_ = value;
}
inline void MappingRule::set_target_begin(uint32_t value) {
  _internal_set_target_begin(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.target_begin)
}

// uint32 target_end = 10;
inline void MappingRule::clear_target_end() {
  _impl_.target_end_ = 0u;
}
inline uint32_t MappingRule::_internal_target_end() const {
  return _impl_.target_end_;
}
inline uint32_t MappingRule::target_end() const {
  // @@protoc_insertion_point(field_get:tensorflow.MappingRule.target_end)
  return _internal_target_end();
}
inline void MappingRule::_internal_set_target_end(uint32_t value) {
  
  _impl_.target_end_ = value;
}
inline void MappingRule::set_target_end(uint32_t value) {
  _internal_set_target_end(value);
  // @@protoc_insertion_point(field_set:tensorflow.MappingRule.target_end)
}

// -------------------------------------------------------------------

// VName

// string signature = 1;
inline void VName::clear_signature() {
  _impl_.signature_.ClearToEmpty();
}
inline const std::string& VName::signature() const {
  // @@protoc_insertion_point(field_get:tensorflow.VName.signature)
  return _internal_signature();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VName::set_signature(ArgT0&& arg0, ArgT... args) {
 
 _impl_.signature_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VName.signature)
}
inline std::string* VName::mutable_signature() {
  std::string* _s = _internal_mutable_signature();
  // @@protoc_insertion_point(field_mutable:tensorflow.VName.signature)
  return _s;
}
inline const std::string& VName::_internal_signature() const {
  return _impl_.signature_.Get();
}
inline void VName::_internal_set_signature(const std::string& value) {
  
  _impl_.signature_.Set(value, GetArenaForAllocation());
}
inline std::string* VName::_internal_mutable_signature() {
  
  return _impl_.signature_.Mutable(GetArenaForAllocation());
}
inline std::string* VName::release_signature() {
  // @@protoc_insertion_point(field_release:tensorflow.VName.signature)
  return _impl_.signature_.Release();
}
inline void VName::set_allocated_signature(std::string* signature) {
  if (signature != nullptr) {
    
  } else {
    
  }
  _impl_.signature_.SetAllocated(signature, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.signature_.IsDefault()) {
    _impl_.signature_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VName.signature)
}

// string corpus = 2;
inline void VName::clear_corpus() {
  _impl_.corpus_.ClearToEmpty();
}
inline const std::string& VName::corpus() const {
  // @@protoc_insertion_point(field_get:tensorflow.VName.corpus)
  return _internal_corpus();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VName::set_corpus(ArgT0&& arg0, ArgT... args) {
 
 _impl_.corpus_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VName.corpus)
}
inline std::string* VName::mutable_corpus() {
  std::string* _s = _internal_mutable_corpus();
  // @@protoc_insertion_point(field_mutable:tensorflow.VName.corpus)
  return _s;
}
inline const std::string& VName::_internal_corpus() const {
  return _impl_.corpus_.Get();
}
inline void VName::_internal_set_corpus(const std::string& value) {
  
  _impl_.corpus_.Set(value, GetArenaForAllocation());
}
inline std::string* VName::_internal_mutable_corpus() {
  
  return _impl_.corpus_.Mutable(GetArenaForAllocation());
}
inline std::string* VName::release_corpus() {
  // @@protoc_insertion_point(field_release:tensorflow.VName.corpus)
  return _impl_.corpus_.Release();
}
inline void VName::set_allocated_corpus(std::string* corpus) {
  if (corpus != nullptr) {
    
  } else {
    
  }
  _impl_.corpus_.SetAllocated(corpus, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.corpus_.IsDefault()) {
    _impl_.corpus_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VName.corpus)
}

// string root = 3;
inline void VName::clear_root() {
  _impl_.root_.ClearToEmpty();
}
inline const std::string& VName::root() const {
  // @@protoc_insertion_point(field_get:tensorflow.VName.root)
  return _internal_root();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VName::set_root(ArgT0&& arg0, ArgT... args) {
 
 _impl_.root_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VName.root)
}
inline std::string* VName::mutable_root() {
  std::string* _s = _internal_mutable_root();
  // @@protoc_insertion_point(field_mutable:tensorflow.VName.root)
  return _s;
}
inline const std::string& VName::_internal_root() const {
  return _impl_.root_.Get();
}
inline void VName::_internal_set_root(const std::string& value) {
  
  _impl_.root_.Set(value, GetArenaForAllocation());
}
inline std::string* VName::_internal_mutable_root() {
  
  return _impl_.root_.Mutable(GetArenaForAllocation());
}
inline std::string* VName::release_root() {
  // @@protoc_insertion_point(field_release:tensorflow.VName.root)
  return _impl_.root_.Release();
}
inline void VName::set_allocated_root(std::string* root) {
  if (root != nullptr) {
    
  } else {
    
  }
  _impl_.root_.SetAllocated(root, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.root_.IsDefault()) {
    _impl_.root_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VName.root)
}

// string path = 4;
inline void VName::clear_path() {
  _impl_.path_.ClearToEmpty();
}
inline const std::string& VName::path() const {
  // @@protoc_insertion_point(field_get:tensorflow.VName.path)
  return _internal_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VName::set_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VName.path)
}
inline std::string* VName::mutable_path() {
  std::string* _s = _internal_mutable_path();
  // @@protoc_insertion_point(field_mutable:tensorflow.VName.path)
  return _s;
}
inline const std::string& VName::_internal_path() const {
  return _impl_.path_.Get();
}
inline void VName::_internal_set_path(const std::string& value) {
  
  _impl_.path_.Set(value, GetArenaForAllocation());
}
inline std::string* VName::_internal_mutable_path() {
  
  return _impl_.path_.Mutable(GetArenaForAllocation());
}
inline std::string* VName::release_path() {
  // @@protoc_insertion_point(field_release:tensorflow.VName.path)
  return _impl_.path_.Release();
}
inline void VName::set_allocated_path(std::string* path) {
  if (path != nullptr) {
    
  } else {
    
  }
  _impl_.path_.SetAllocated(path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.path_.IsDefault()) {
    _impl_.path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VName.path)
}

// string language = 5;
inline void VName::clear_language() {
  _impl_.language_.ClearToEmpty();
}
inline const std::string& VName::language() const {
  // @@protoc_insertion_point(field_get:tensorflow.VName.language)
  return _internal_language();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void VName::set_language(ArgT0&& arg0, ArgT... args) {
 
 _impl_.language_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.VName.language)
}
inline std::string* VName::mutable_language() {
  std::string* _s = _internal_mutable_language();
  // @@protoc_insertion_point(field_mutable:tensorflow.VName.language)
  return _s;
}
inline const std::string& VName::_internal_language() const {
  return _impl_.language_.Get();
}
inline void VName::_internal_set_language(const std::string& value) {
  
  _impl_.language_.Set(value, GetArenaForAllocation());
}
inline std::string* VName::_internal_mutable_language() {
  
  return _impl_.language_.Mutable(GetArenaForAllocation());
}
inline std::string* VName::release_language() {
  // @@protoc_insertion_point(field_release:tensorflow.VName.language)
  return _impl_.language_.Release();
}
inline void VName::set_allocated_language(std::string* language) {
  if (language != nullptr) {
    
  } else {
    
  }
  _impl_.language_.SetAllocated(language, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.language_.IsDefault()) {
    _impl_.language_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.VName.language)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::GeneratedCodeInfo_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::GeneratedCodeInfo_Type>() {
  return ::tensorflow::GeneratedCodeInfo_Type_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::MappingRule_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::MappingRule_Type>() {
  return ::tensorflow::MappingRule_Type_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::MappingRule_Semantic> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::MappingRule_Semantic>() {
  return ::tensorflow::MappingRule_Semantic_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fpython_2fframework_2fkythe_5fmetadata_2eproto
