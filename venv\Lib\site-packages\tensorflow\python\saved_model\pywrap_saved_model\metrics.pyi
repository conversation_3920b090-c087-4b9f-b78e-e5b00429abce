# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

from typing import Any

kFingerprintError: str
kFingerprintFound: str
kFingerprintNotFound: str

class MetricException(Exception): ...

def AddAsyncCheckpointWriteDuration(*args, **kwargs) -> Any: ...
def AddCheckpointReadDuration(*args, **kwargs) -> Any: ...
def AddCheckpointWriteDuration(*args, **kwargs) -> Any: ...
def AddTrainingTimeSaved(*args, **kwargs) -> Any: ...
def CalculateFileSize(arg0: str) -> int: ...
def GetAsyncCheckpointWriteDurations(*args, **kwargs) -> Any: ...
def GetCheckpointReadDurations(*args, **kwargs) -> Any: ...
def GetCheckpointSize(*args, **kwargs) -> Any: ...
def GetCheckpointWriteDurations(*args, **kwargs) -> Any: ...
def GetFoundFingerprintOnLoad() -> str: ...
def GetRead(*args, **kwargs) -> Any: ...
def GetReadApi(arg0: str) -> int: ...
def GetReadFingerprint() -> str: ...
def GetReadPath() -> str: ...
def GetReadPathAndSingleprint() -> tuple[str,str]: ...
def GetTrainingTimeSaved(*args, **kwargs) -> Any: ...
def GetWrite(*args, **kwargs) -> Any: ...
def GetWriteApi(arg0: str) -> int: ...
def GetWriteFingerprint() -> str: ...
def GetWritePath() -> str: ...
def GetWritePathAndSingleprint() -> tuple[str,str]: ...
def IncrementRead(*args, **kwargs) -> Any: ...
def IncrementReadApi(arg0: str) -> None: ...
def IncrementWrite(*args, **kwargs) -> Any: ...
def IncrementWriteApi(arg0: str) -> None: ...
def RecordCheckpointSize(*args, **kwargs) -> Any: ...
def SetFoundFingerprintOnLoad(*args, **kwargs) -> Any: ...
def SetReadFingerprint(*args, **kwargs) -> Any: ...
def SetReadPath(*args, **kwargs) -> Any: ...
def SetReadPathAndSingleprint(*args, **kwargs) -> Any: ...
def SetWriteFingerprint(*args, **kwargs) -> Any: ...
def SetWritePath(*args, **kwargs) -> Any: ...
def SetWritePathAndSingleprint(*args, **kwargs) -> Any: ...
