# Copyright 2023 The TensorFlow Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================

from typing import Any

class InferredAttributes:
    def __init__(self, *args, **kwargs) -> None: ...
    @property
    def lengths(self) -> list[int]: ...
    @property
    def type_lists(self) -> Any: ...
    @property
    def types(self) -> Any: ...

class PythonAPIInfo:
    def __init__(self, arg0: str) -> None: ...
    def DebugInfo(self) -> str: ...
    def InferredLengthAttrs(self) -> list[str]: ...
    def InferredTypeAttrs(self) -> list[str]: ...
    def InferredTypeListAttrs(self) -> list[str]: ...
    def InitializeFromParamSpecs(self, arg0: dict[str,str], arg1: dict[str,str], arg2: list[str], arg3: object) -> None: ...
    def InitializeFromRegisteredOp(self, arg0: str) -> None: ...
