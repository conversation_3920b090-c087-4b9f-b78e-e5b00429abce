// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/tpu_embedding_configuration.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/protobuf/tpu/optimization_parameters.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
namespace tensorflow {
namespace tpu {
class TPUEmbeddingConfiguration;
struct TPUEmbeddingConfigurationDefaultTypeInternal;
extern TPUEmbeddingConfigurationDefaultTypeInternal _TPUEmbeddingConfiguration_default_instance_;
class TPUEmbeddingConfiguration_FeatureDescriptor;
struct TPUEmbeddingConfiguration_FeatureDescriptorDefaultTypeInternal;
extern TPUEmbeddingConfiguration_FeatureDescriptorDefaultTypeInternal _TPUEmbeddingConfiguration_FeatureDescriptor_default_instance_;
class TPUEmbeddingConfiguration_SpmdSharding;
struct TPUEmbeddingConfiguration_SpmdShardingDefaultTypeInternal;
extern TPUEmbeddingConfiguration_SpmdShardingDefaultTypeInternal _TPUEmbeddingConfiguration_SpmdSharding_default_instance_;
class TPUEmbeddingConfiguration_TableDescriptor;
struct TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal;
extern TPUEmbeddingConfiguration_TableDescriptorDefaultTypeInternal _TPUEmbeddingConfiguration_TableDescriptor_default_instance_;
class TPUEmbeddingError;
struct TPUEmbeddingErrorDefaultTypeInternal;
extern TPUEmbeddingErrorDefaultTypeInternal _TPUEmbeddingError_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor>(Arena*);
template<> ::tensorflow::tpu::TPUEmbeddingError* Arena::CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingError>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

enum TPUEmbeddingConfiguration_Mode : int {
  TPUEmbeddingConfiguration_Mode_UNSPECIFIED = 0,
  TPUEmbeddingConfiguration_Mode_INFERENCE = 1,
  TPUEmbeddingConfiguration_Mode_TRAINING = 2,
  TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY = 3,
  TPUEmbeddingConfiguration_Mode_TPUEmbeddingConfiguration_Mode_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TPUEmbeddingConfiguration_Mode_TPUEmbeddingConfiguration_Mode_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TPUEmbeddingConfiguration_Mode_IsValid(int value);
constexpr TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration_Mode_Mode_MIN = TPUEmbeddingConfiguration_Mode_UNSPECIFIED;
constexpr TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration_Mode_Mode_MAX = TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY;
constexpr int TPUEmbeddingConfiguration_Mode_Mode_ARRAYSIZE = TPUEmbeddingConfiguration_Mode_Mode_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUEmbeddingConfiguration_Mode_descriptor();
template<typename T>
inline const std::string& TPUEmbeddingConfiguration_Mode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUEmbeddingConfiguration_Mode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUEmbeddingConfiguration_Mode_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUEmbeddingConfiguration_Mode_descriptor(), enum_t_value);
}
inline bool TPUEmbeddingConfiguration_Mode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TPUEmbeddingConfiguration_Mode* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUEmbeddingConfiguration_Mode>(
    TPUEmbeddingConfiguration_Mode_descriptor(), name, value);
}
enum TPUEmbeddingConfiguration_ShardingStrategy : int {
  TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT = 0,
  TPUEmbeddingConfiguration_ShardingStrategy_MOD = 1,
  TPUEmbeddingConfiguration_ShardingStrategy_TPUEmbeddingConfiguration_ShardingStrategy_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TPUEmbeddingConfiguration_ShardingStrategy_TPUEmbeddingConfiguration_ShardingStrategy_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TPUEmbeddingConfiguration_ShardingStrategy_IsValid(int value);
constexpr TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MIN = TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT;
constexpr TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX = TPUEmbeddingConfiguration_ShardingStrategy_MOD;
constexpr int TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_ARRAYSIZE = TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
template<typename T>
inline const std::string& TPUEmbeddingConfiguration_ShardingStrategy_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TPUEmbeddingConfiguration_ShardingStrategy>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TPUEmbeddingConfiguration_ShardingStrategy_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TPUEmbeddingConfiguration_ShardingStrategy_descriptor(), enum_t_value);
}
inline bool TPUEmbeddingConfiguration_ShardingStrategy_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TPUEmbeddingConfiguration_ShardingStrategy* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TPUEmbeddingConfiguration_ShardingStrategy>(
    TPUEmbeddingConfiguration_ShardingStrategy_descriptor(), name, value);
}
// ===================================================================

class TPUEmbeddingConfiguration_TableDescriptor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor) */ {
 public:
  inline TPUEmbeddingConfiguration_TableDescriptor() : TPUEmbeddingConfiguration_TableDescriptor(nullptr) {}
  ~TPUEmbeddingConfiguration_TableDescriptor() override;
  explicit PROTOBUF_CONSTEXPR TPUEmbeddingConfiguration_TableDescriptor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUEmbeddingConfiguration_TableDescriptor(const TPUEmbeddingConfiguration_TableDescriptor& from);
  TPUEmbeddingConfiguration_TableDescriptor(TPUEmbeddingConfiguration_TableDescriptor&& from) noexcept
    : TPUEmbeddingConfiguration_TableDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration_TableDescriptor& operator=(const TPUEmbeddingConfiguration_TableDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingConfiguration_TableDescriptor& operator=(TPUEmbeddingConfiguration_TableDescriptor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUEmbeddingConfiguration_TableDescriptor& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUEmbeddingConfiguration_TableDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration_TableDescriptor*>(
               &_TPUEmbeddingConfiguration_TableDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TPUEmbeddingConfiguration_TableDescriptor& a, TPUEmbeddingConfiguration_TableDescriptor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingConfiguration_TableDescriptor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUEmbeddingConfiguration_TableDescriptor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUEmbeddingConfiguration_TableDescriptor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_TableDescriptor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUEmbeddingConfiguration_TableDescriptor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUEmbeddingConfiguration_TableDescriptor& from) {
    TPUEmbeddingConfiguration_TableDescriptor::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration_TableDescriptor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor";
  }
  protected:
  explicit TPUEmbeddingConfiguration_TableDescriptor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kOptimizationParametersFieldNumber = 5,
    kVocabularySizeFieldNumber = 2,
    kDimensionFieldNumber = 3,
    kNumFeaturesFieldNumber = 4,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
  bool has_optimization_parameters() const;
  private:
  bool _internal_has_optimization_parameters() const;
  public:
  void clear_optimization_parameters();
  const ::tensorflow::tpu::OptimizationParameters& optimization_parameters() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::OptimizationParameters* release_optimization_parameters();
  ::tensorflow::tpu::OptimizationParameters* mutable_optimization_parameters();
  void set_allocated_optimization_parameters(::tensorflow::tpu::OptimizationParameters* optimization_parameters);
  private:
  const ::tensorflow::tpu::OptimizationParameters& _internal_optimization_parameters() const;
  ::tensorflow::tpu::OptimizationParameters* _internal_mutable_optimization_parameters();
  public:
  void unsafe_arena_set_allocated_optimization_parameters(
      ::tensorflow::tpu::OptimizationParameters* optimization_parameters);
  ::tensorflow::tpu::OptimizationParameters* unsafe_arena_release_optimization_parameters();

  // int64 vocabulary_size = 2;
  void clear_vocabulary_size();
  int64_t vocabulary_size() const;
  void set_vocabulary_size(int64_t value);
  private:
  int64_t _internal_vocabulary_size() const;
  void _internal_set_vocabulary_size(int64_t value);
  public:

  // int32 dimension = 3;
  void clear_dimension();
  int32_t dimension() const;
  void set_dimension(int32_t value);
  private:
  int32_t _internal_dimension() const;
  void _internal_set_dimension(int32_t value);
  public:

  // int32 num_features = 4;
  void clear_num_features();
  int32_t num_features() const;
  void set_num_features(int32_t value);
  private:
  int32_t _internal_num_features() const;
  void _internal_set_num_features(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::tpu::OptimizationParameters* optimization_parameters_;
    int64_t vocabulary_size_;
    int32_t dimension_;
    int32_t num_features_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingConfiguration_FeatureDescriptor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor) */ {
 public:
  inline TPUEmbeddingConfiguration_FeatureDescriptor() : TPUEmbeddingConfiguration_FeatureDescriptor(nullptr) {}
  ~TPUEmbeddingConfiguration_FeatureDescriptor() override;
  explicit PROTOBUF_CONSTEXPR TPUEmbeddingConfiguration_FeatureDescriptor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUEmbeddingConfiguration_FeatureDescriptor(const TPUEmbeddingConfiguration_FeatureDescriptor& from);
  TPUEmbeddingConfiguration_FeatureDescriptor(TPUEmbeddingConfiguration_FeatureDescriptor&& from) noexcept
    : TPUEmbeddingConfiguration_FeatureDescriptor() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration_FeatureDescriptor& operator=(const TPUEmbeddingConfiguration_FeatureDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingConfiguration_FeatureDescriptor& operator=(TPUEmbeddingConfiguration_FeatureDescriptor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUEmbeddingConfiguration_FeatureDescriptor& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUEmbeddingConfiguration_FeatureDescriptor* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration_FeatureDescriptor*>(
               &_TPUEmbeddingConfiguration_FeatureDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(TPUEmbeddingConfiguration_FeatureDescriptor& a, TPUEmbeddingConfiguration_FeatureDescriptor& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingConfiguration_FeatureDescriptor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUEmbeddingConfiguration_FeatureDescriptor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUEmbeddingConfiguration_FeatureDescriptor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_FeatureDescriptor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUEmbeddingConfiguration_FeatureDescriptor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUEmbeddingConfiguration_FeatureDescriptor& from) {
    TPUEmbeddingConfiguration_FeatureDescriptor::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration_FeatureDescriptor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor";
  }
  protected:
  explicit TPUEmbeddingConfiguration_FeatureDescriptor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputShapeFieldNumber = 3,
    kNameFieldNumber = 1,
    kTableIdFieldNumber = 2,
  };
  // repeated int32 input_shape = 3;
  int input_shape_size() const;
  private:
  int _internal_input_shape_size() const;
  public:
  void clear_input_shape();
  private:
  int32_t _internal_input_shape(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_input_shape() const;
  void _internal_add_input_shape(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_input_shape();
  public:
  int32_t input_shape(int index) const;
  void set_input_shape(int index, int32_t value);
  void add_input_shape(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      input_shape() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_input_shape();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // int32 table_id = 2;
  void clear_table_id();
  int32_t table_id() const;
  void set_table_id(int32_t value);
  private:
  int32_t _internal_table_id() const;
  void _internal_set_table_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > input_shape_;
    mutable std::atomic<int> _input_shape_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    int32_t table_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingConfiguration_SpmdSharding final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding) */ {
 public:
  inline TPUEmbeddingConfiguration_SpmdSharding() : TPUEmbeddingConfiguration_SpmdSharding(nullptr) {}
  ~TPUEmbeddingConfiguration_SpmdSharding() override;
  explicit PROTOBUF_CONSTEXPR TPUEmbeddingConfiguration_SpmdSharding(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUEmbeddingConfiguration_SpmdSharding(const TPUEmbeddingConfiguration_SpmdSharding& from);
  TPUEmbeddingConfiguration_SpmdSharding(TPUEmbeddingConfiguration_SpmdSharding&& from) noexcept
    : TPUEmbeddingConfiguration_SpmdSharding() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration_SpmdSharding& operator=(const TPUEmbeddingConfiguration_SpmdSharding& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingConfiguration_SpmdSharding& operator=(TPUEmbeddingConfiguration_SpmdSharding&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUEmbeddingConfiguration_SpmdSharding& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUEmbeddingConfiguration_SpmdSharding* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration_SpmdSharding*>(
               &_TPUEmbeddingConfiguration_SpmdSharding_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TPUEmbeddingConfiguration_SpmdSharding& a, TPUEmbeddingConfiguration_SpmdSharding& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingConfiguration_SpmdSharding* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUEmbeddingConfiguration_SpmdSharding* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUEmbeddingConfiguration_SpmdSharding* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration_SpmdSharding>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUEmbeddingConfiguration_SpmdSharding& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUEmbeddingConfiguration_SpmdSharding& from) {
    TPUEmbeddingConfiguration_SpmdSharding::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration_SpmdSharding* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding";
  }
  protected:
  explicit TPUEmbeddingConfiguration_SpmdSharding(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
    kNumCoresPerReplicaFieldNumber = 2,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // int32 num_cores_per_replica = 2;
  void clear_num_cores_per_replica();
  int32_t num_cores_per_replica() const;
  void set_num_cores_per_replica(int32_t value);
  private:
  int32_t _internal_num_cores_per_replica() const;
  void _internal_set_num_cores_per_replica(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool enabled_;
    int32_t num_cores_per_replica_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingConfiguration) */ {
 public:
  inline TPUEmbeddingConfiguration() : TPUEmbeddingConfiguration(nullptr) {}
  ~TPUEmbeddingConfiguration() override;
  explicit PROTOBUF_CONSTEXPR TPUEmbeddingConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUEmbeddingConfiguration(const TPUEmbeddingConfiguration& from);
  TPUEmbeddingConfiguration(TPUEmbeddingConfiguration&& from) noexcept
    : TPUEmbeddingConfiguration() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingConfiguration& operator=(const TPUEmbeddingConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingConfiguration& operator=(TPUEmbeddingConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUEmbeddingConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUEmbeddingConfiguration* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingConfiguration*>(
               &_TPUEmbeddingConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TPUEmbeddingConfiguration& a, TPUEmbeddingConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUEmbeddingConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUEmbeddingConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUEmbeddingConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TPUEmbeddingConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TPUEmbeddingConfiguration& from) {
    TPUEmbeddingConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TPUEmbeddingConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingConfiguration";
  }
  protected:
  explicit TPUEmbeddingConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TPUEmbeddingConfiguration_TableDescriptor TableDescriptor;
  typedef TPUEmbeddingConfiguration_FeatureDescriptor FeatureDescriptor;
  typedef TPUEmbeddingConfiguration_SpmdSharding SpmdSharding;

  typedef TPUEmbeddingConfiguration_Mode Mode;
  static constexpr Mode UNSPECIFIED =
    TPUEmbeddingConfiguration_Mode_UNSPECIFIED;
  static constexpr Mode INFERENCE =
    TPUEmbeddingConfiguration_Mode_INFERENCE;
  static constexpr Mode TRAINING =
    TPUEmbeddingConfiguration_Mode_TRAINING;
  static constexpr Mode BACKWARD_PASS_ONLY =
    TPUEmbeddingConfiguration_Mode_BACKWARD_PASS_ONLY;
  static inline bool Mode_IsValid(int value) {
    return TPUEmbeddingConfiguration_Mode_IsValid(value);
  }
  static constexpr Mode Mode_MIN =
    TPUEmbeddingConfiguration_Mode_Mode_MIN;
  static constexpr Mode Mode_MAX =
    TPUEmbeddingConfiguration_Mode_Mode_MAX;
  static constexpr int Mode_ARRAYSIZE =
    TPUEmbeddingConfiguration_Mode_Mode_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Mode_descriptor() {
    return TPUEmbeddingConfiguration_Mode_descriptor();
  }
  template<typename T>
  static inline const std::string& Mode_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Mode>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Mode_Name.");
    return TPUEmbeddingConfiguration_Mode_Name(enum_t_value);
  }
  static inline bool Mode_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Mode* value) {
    return TPUEmbeddingConfiguration_Mode_Parse(name, value);
  }

  typedef TPUEmbeddingConfiguration_ShardingStrategy ShardingStrategy;
  static constexpr ShardingStrategy DIV_DEFAULT =
    TPUEmbeddingConfiguration_ShardingStrategy_DIV_DEFAULT;
  static constexpr ShardingStrategy MOD =
    TPUEmbeddingConfiguration_ShardingStrategy_MOD;
  static inline bool ShardingStrategy_IsValid(int value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_IsValid(value);
  }
  static constexpr ShardingStrategy ShardingStrategy_MIN =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MIN;
  static constexpr ShardingStrategy ShardingStrategy_MAX =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_MAX;
  static constexpr int ShardingStrategy_ARRAYSIZE =
    TPUEmbeddingConfiguration_ShardingStrategy_ShardingStrategy_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  ShardingStrategy_descriptor() {
    return TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
  }
  template<typename T>
  static inline const std::string& ShardingStrategy_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ShardingStrategy>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ShardingStrategy_Name.");
    return TPUEmbeddingConfiguration_ShardingStrategy_Name(enum_t_value);
  }
  static inline bool ShardingStrategy_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ShardingStrategy* value) {
    return TPUEmbeddingConfiguration_ShardingStrategy_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTableDescriptorFieldNumber = 1,
    kFeatureDescriptorFieldNumber = 10,
    kProfileDataDirectoryFieldNumber = 9,
    kSpmdShardingFieldNumber = 11,
    kModeFieldNumber = 2,
    kBatchSizePerTensorCoreFieldNumber = 3,
    kNumHostsFieldNumber = 4,
    kNumTensorCoresFieldNumber = 5,
    kShardingStrategyFieldNumber = 6,
    kPipelineExecutionWithTensorCoreFieldNumber = 7,
  };
  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
  int table_descriptor_size() const;
  private:
  int _internal_table_descriptor_size() const;
  public:
  void clear_table_descriptor();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* mutable_table_descriptor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >*
      mutable_table_descriptor();
  private:
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& _internal_table_descriptor(int index) const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* _internal_add_table_descriptor();
  public:
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& table_descriptor(int index) const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* add_table_descriptor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >&
      table_descriptor() const;

  // repeated .tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor feature_descriptor = 10;
  int feature_descriptor_size() const;
  private:
  int _internal_feature_descriptor_size() const;
  public:
  void clear_feature_descriptor();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* mutable_feature_descriptor(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor >*
      mutable_feature_descriptor();
  private:
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor& _internal_feature_descriptor(int index) const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* _internal_add_feature_descriptor();
  public:
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor& feature_descriptor(int index) const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* add_feature_descriptor();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor >&
      feature_descriptor() const;

  // string profile_data_directory = 9;
  void clear_profile_data_directory();
  const std::string& profile_data_directory() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_profile_data_directory(ArgT0&& arg0, ArgT... args);
  std::string* mutable_profile_data_directory();
  PROTOBUF_NODISCARD std::string* release_profile_data_directory();
  void set_allocated_profile_data_directory(std::string* profile_data_directory);
  private:
  const std::string& _internal_profile_data_directory() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_profile_data_directory(const std::string& value);
  std::string* _internal_mutable_profile_data_directory();
  public:

  // .tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding spmd_sharding = 11;
  bool has_spmd_sharding() const;
  private:
  bool _internal_has_spmd_sharding() const;
  public:
  void clear_spmd_sharding();
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding& spmd_sharding() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* release_spmd_sharding();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* mutable_spmd_sharding();
  void set_allocated_spmd_sharding(::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* spmd_sharding);
  private:
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding& _internal_spmd_sharding() const;
  ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* _internal_mutable_spmd_sharding();
  public:
  void unsafe_arena_set_allocated_spmd_sharding(
      ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* spmd_sharding);
  ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* unsafe_arena_release_spmd_sharding();

  // .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
  void clear_mode();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode mode() const;
  void set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value);
  private:
  ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode _internal_mode() const;
  void _internal_set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value);
  public:

  // int32 batch_size_per_tensor_core = 3;
  void clear_batch_size_per_tensor_core();
  int32_t batch_size_per_tensor_core() const;
  void set_batch_size_per_tensor_core(int32_t value);
  private:
  int32_t _internal_batch_size_per_tensor_core() const;
  void _internal_set_batch_size_per_tensor_core(int32_t value);
  public:

  // int32 num_hosts = 4;
  void clear_num_hosts();
  int32_t num_hosts() const;
  void set_num_hosts(int32_t value);
  private:
  int32_t _internal_num_hosts() const;
  void _internal_set_num_hosts(int32_t value);
  public:

  // int32 num_tensor_cores = 5;
  void clear_num_tensor_cores();
  int32_t num_tensor_cores() const;
  void set_num_tensor_cores(int32_t value);
  private:
  int32_t _internal_num_tensor_cores() const;
  void _internal_set_num_tensor_cores(int32_t value);
  public:

  // .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
  void clear_sharding_strategy();
  ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy sharding_strategy() const;
  void set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value);
  private:
  ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy _internal_sharding_strategy() const;
  void _internal_set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value);
  public:

  // bool pipeline_execution_with_tensor_core = 7;
  void clear_pipeline_execution_with_tensor_core();
  bool pipeline_execution_with_tensor_core() const;
  void set_pipeline_execution_with_tensor_core(bool value);
  private:
  bool _internal_pipeline_execution_with_tensor_core() const;
  void _internal_set_pipeline_execution_with_tensor_core(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor > table_descriptor_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor > feature_descriptor_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr profile_data_directory_;
    ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* spmd_sharding_;
    int mode_;
    int32_t batch_size_per_tensor_core_;
    int32_t num_hosts_;
    int32_t num_tensor_cores_;
    int sharding_strategy_;
    bool pipeline_execution_with_tensor_core_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// -------------------------------------------------------------------

class TPUEmbeddingError final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.TPUEmbeddingError) */ {
 public:
  inline TPUEmbeddingError() : TPUEmbeddingError(nullptr) {}
  explicit PROTOBUF_CONSTEXPR TPUEmbeddingError(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TPUEmbeddingError(const TPUEmbeddingError& from);
  TPUEmbeddingError(TPUEmbeddingError&& from) noexcept
    : TPUEmbeddingError() {
    *this = ::std::move(from);
  }

  inline TPUEmbeddingError& operator=(const TPUEmbeddingError& from) {
    CopyFrom(from);
    return *this;
  }
  inline TPUEmbeddingError& operator=(TPUEmbeddingError&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TPUEmbeddingError& default_instance() {
    return *internal_default_instance();
  }
  static inline const TPUEmbeddingError* internal_default_instance() {
    return reinterpret_cast<const TPUEmbeddingError*>(
               &_TPUEmbeddingError_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TPUEmbeddingError& a, TPUEmbeddingError& b) {
    a.Swap(&b);
  }
  inline void Swap(TPUEmbeddingError* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TPUEmbeddingError* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TPUEmbeddingError* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TPUEmbeddingError>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const TPUEmbeddingError& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const TPUEmbeddingError& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.TPUEmbeddingError";
  }
  protected:
  explicit TPUEmbeddingError(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.TPUEmbeddingError)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TPUEmbeddingConfiguration_TableDescriptor

// string name = 1;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& TPUEmbeddingConfiguration_TableDescriptor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUEmbeddingConfiguration_TableDescriptor::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}
inline std::string* TPUEmbeddingConfiguration_TableDescriptor::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return _s;
}
inline const std::string& TPUEmbeddingConfiguration_TableDescriptor::_internal_name() const {
  return _impl_.name_.Get();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUEmbeddingConfiguration_TableDescriptor::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUEmbeddingConfiguration_TableDescriptor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
  return _impl_.name_.Release();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.name)
}

// int64 vocabulary_size = 2;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_vocabulary_size() {
  _impl_.vocabulary_size_ = int64_t{0};
}
inline int64_t TPUEmbeddingConfiguration_TableDescriptor::_internal_vocabulary_size() const {
  return _impl_.vocabulary_size_;
}
inline int64_t TPUEmbeddingConfiguration_TableDescriptor::vocabulary_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.vocabulary_size)
  return _internal_vocabulary_size();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::_internal_set_vocabulary_size(int64_t value) {
  
  _impl_.vocabulary_size_ = value;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_vocabulary_size(int64_t value) {
  _internal_set_vocabulary_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.vocabulary_size)
}

// int32 dimension = 3;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_dimension() {
  _impl_.dimension_ = 0;
}
inline int32_t TPUEmbeddingConfiguration_TableDescriptor::_internal_dimension() const {
  return _impl_.dimension_;
}
inline int32_t TPUEmbeddingConfiguration_TableDescriptor::dimension() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.dimension)
  return _internal_dimension();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::_internal_set_dimension(int32_t value) {
  
  _impl_.dimension_ = value;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_dimension(int32_t value) {
  _internal_set_dimension(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.dimension)
}

// int32 num_features = 4;
inline void TPUEmbeddingConfiguration_TableDescriptor::clear_num_features() {
  _impl_.num_features_ = 0;
}
inline int32_t TPUEmbeddingConfiguration_TableDescriptor::_internal_num_features() const {
  return _impl_.num_features_;
}
inline int32_t TPUEmbeddingConfiguration_TableDescriptor::num_features() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.num_features)
  return _internal_num_features();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::_internal_set_num_features(int32_t value) {
  
  _impl_.num_features_ = value;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_num_features(int32_t value) {
  _internal_set_num_features(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.num_features)
}

// .tensorflow.tpu.OptimizationParameters optimization_parameters = 5;
inline bool TPUEmbeddingConfiguration_TableDescriptor::_internal_has_optimization_parameters() const {
  return this != internal_default_instance() && _impl_.optimization_parameters_ != nullptr;
}
inline bool TPUEmbeddingConfiguration_TableDescriptor::has_optimization_parameters() const {
  return _internal_has_optimization_parameters();
}
inline const ::tensorflow::tpu::OptimizationParameters& TPUEmbeddingConfiguration_TableDescriptor::_internal_optimization_parameters() const {
  const ::tensorflow::tpu::OptimizationParameters* p = _impl_.optimization_parameters_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::OptimizationParameters&>(
      ::tensorflow::tpu::_OptimizationParameters_default_instance_);
}
inline const ::tensorflow::tpu::OptimizationParameters& TPUEmbeddingConfiguration_TableDescriptor::optimization_parameters() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  return _internal_optimization_parameters();
}
inline void TPUEmbeddingConfiguration_TableDescriptor::unsafe_arena_set_allocated_optimization_parameters(
    ::tensorflow::tpu::OptimizationParameters* optimization_parameters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimization_parameters_);
  }
  _impl_.optimization_parameters_ = optimization_parameters;
  if (optimization_parameters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::release_optimization_parameters() {
  
  ::tensorflow::tpu::OptimizationParameters* temp = _impl_.optimization_parameters_;
  _impl_.optimization_parameters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::unsafe_arena_release_optimization_parameters() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  
  ::tensorflow::tpu::OptimizationParameters* temp = _impl_.optimization_parameters_;
  _impl_.optimization_parameters_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::_internal_mutable_optimization_parameters() {
  
  if (_impl_.optimization_parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::OptimizationParameters>(GetArenaForAllocation());
    _impl_.optimization_parameters_ = p;
  }
  return _impl_.optimization_parameters_;
}
inline ::tensorflow::tpu::OptimizationParameters* TPUEmbeddingConfiguration_TableDescriptor::mutable_optimization_parameters() {
  ::tensorflow::tpu::OptimizationParameters* _msg = _internal_mutable_optimization_parameters();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
  return _msg;
}
inline void TPUEmbeddingConfiguration_TableDescriptor::set_allocated_optimization_parameters(::tensorflow::tpu::OptimizationParameters* optimization_parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.optimization_parameters_);
  }
  if (optimization_parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(optimization_parameters));
    if (message_arena != submessage_arena) {
      optimization_parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, optimization_parameters, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.optimization_parameters_ = optimization_parameters;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor.optimization_parameters)
}

// -------------------------------------------------------------------

// TPUEmbeddingConfiguration_FeatureDescriptor

// string name = 1;
inline void TPUEmbeddingConfiguration_FeatureDescriptor::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& TPUEmbeddingConfiguration_FeatureDescriptor::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUEmbeddingConfiguration_FeatureDescriptor::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.name)
}
inline std::string* TPUEmbeddingConfiguration_FeatureDescriptor::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.name)
  return _s;
}
inline const std::string& TPUEmbeddingConfiguration_FeatureDescriptor::_internal_name() const {
  return _impl_.name_.Get();
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUEmbeddingConfiguration_FeatureDescriptor::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUEmbeddingConfiguration_FeatureDescriptor::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.name)
  return _impl_.name_.Release();
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.name)
}

// int32 table_id = 2;
inline void TPUEmbeddingConfiguration_FeatureDescriptor::clear_table_id() {
  _impl_.table_id_ = 0;
}
inline int32_t TPUEmbeddingConfiguration_FeatureDescriptor::_internal_table_id() const {
  return _impl_.table_id_;
}
inline int32_t TPUEmbeddingConfiguration_FeatureDescriptor::table_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.table_id)
  return _internal_table_id();
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::_internal_set_table_id(int32_t value) {
  
  _impl_.table_id_ = value;
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::set_table_id(int32_t value) {
  _internal_set_table_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.table_id)
}

// repeated int32 input_shape = 3;
inline int TPUEmbeddingConfiguration_FeatureDescriptor::_internal_input_shape_size() const {
  return _impl_.input_shape_.size();
}
inline int TPUEmbeddingConfiguration_FeatureDescriptor::input_shape_size() const {
  return _internal_input_shape_size();
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::clear_input_shape() {
  _impl_.input_shape_.Clear();
}
inline int32_t TPUEmbeddingConfiguration_FeatureDescriptor::_internal_input_shape(int index) const {
  return _impl_.input_shape_.Get(index);
}
inline int32_t TPUEmbeddingConfiguration_FeatureDescriptor::input_shape(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.input_shape)
  return _internal_input_shape(index);
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::set_input_shape(int index, int32_t value) {
  _impl_.input_shape_.Set(index, value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.input_shape)
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::_internal_add_input_shape(int32_t value) {
  _impl_.input_shape_.Add(value);
}
inline void TPUEmbeddingConfiguration_FeatureDescriptor::add_input_shape(int32_t value) {
  _internal_add_input_shape(value);
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.input_shape)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TPUEmbeddingConfiguration_FeatureDescriptor::_internal_input_shape() const {
  return _impl_.input_shape_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
TPUEmbeddingConfiguration_FeatureDescriptor::input_shape() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.input_shape)
  return _internal_input_shape();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TPUEmbeddingConfiguration_FeatureDescriptor::_internal_mutable_input_shape() {
  return &_impl_.input_shape_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
TPUEmbeddingConfiguration_FeatureDescriptor::mutable_input_shape() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor.input_shape)
  return _internal_mutable_input_shape();
}

// -------------------------------------------------------------------

// TPUEmbeddingConfiguration_SpmdSharding

// bool enabled = 1;
inline void TPUEmbeddingConfiguration_SpmdSharding::clear_enabled() {
  _impl_.enabled_ = false;
}
inline bool TPUEmbeddingConfiguration_SpmdSharding::_internal_enabled() const {
  return _impl_.enabled_;
}
inline bool TPUEmbeddingConfiguration_SpmdSharding::enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding.enabled)
  return _internal_enabled();
}
inline void TPUEmbeddingConfiguration_SpmdSharding::_internal_set_enabled(bool value) {
  
  _impl_.enabled_ = value;
}
inline void TPUEmbeddingConfiguration_SpmdSharding::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding.enabled)
}

// int32 num_cores_per_replica = 2;
inline void TPUEmbeddingConfiguration_SpmdSharding::clear_num_cores_per_replica() {
  _impl_.num_cores_per_replica_ = 0;
}
inline int32_t TPUEmbeddingConfiguration_SpmdSharding::_internal_num_cores_per_replica() const {
  return _impl_.num_cores_per_replica_;
}
inline int32_t TPUEmbeddingConfiguration_SpmdSharding::num_cores_per_replica() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding.num_cores_per_replica)
  return _internal_num_cores_per_replica();
}
inline void TPUEmbeddingConfiguration_SpmdSharding::_internal_set_num_cores_per_replica(int32_t value) {
  
  _impl_.num_cores_per_replica_ = value;
}
inline void TPUEmbeddingConfiguration_SpmdSharding::set_num_cores_per_replica(int32_t value) {
  _internal_set_num_cores_per_replica(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding.num_cores_per_replica)
}

// -------------------------------------------------------------------

// TPUEmbeddingConfiguration

// repeated .tensorflow.tpu.TPUEmbeddingConfiguration.TableDescriptor table_descriptor = 1;
inline int TPUEmbeddingConfiguration::_internal_table_descriptor_size() const {
  return _impl_.table_descriptor_.size();
}
inline int TPUEmbeddingConfiguration::table_descriptor_size() const {
  return _internal_table_descriptor_size();
}
inline void TPUEmbeddingConfiguration::clear_table_descriptor() {
  _impl_.table_descriptor_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::mutable_table_descriptor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return _impl_.table_descriptor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >*
TPUEmbeddingConfiguration::mutable_table_descriptor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return &_impl_.table_descriptor_;
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& TPUEmbeddingConfiguration::_internal_table_descriptor(int index) const {
  return _impl_.table_descriptor_.Get(index);
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor& TPUEmbeddingConfiguration::table_descriptor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return _internal_table_descriptor(index);
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::_internal_add_table_descriptor() {
  return _impl_.table_descriptor_.Add();
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* TPUEmbeddingConfiguration::add_table_descriptor() {
  ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor* _add = _internal_add_table_descriptor();
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_TableDescriptor >&
TPUEmbeddingConfiguration::table_descriptor() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingConfiguration.table_descriptor)
  return _impl_.table_descriptor_;
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.Mode mode = 2;
inline void TPUEmbeddingConfiguration::clear_mode() {
  _impl_.mode_ = 0;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::_internal_mode() const {
  return static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode >(_impl_.mode_);
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode TPUEmbeddingConfiguration::mode() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.mode)
  return _internal_mode();
}
inline void TPUEmbeddingConfiguration::_internal_set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value) {
  
  _impl_.mode_ = value;
}
inline void TPUEmbeddingConfiguration::set_mode(::tensorflow::tpu::TPUEmbeddingConfiguration_Mode value) {
  _internal_set_mode(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.mode)
}

// int32 batch_size_per_tensor_core = 3;
inline void TPUEmbeddingConfiguration::clear_batch_size_per_tensor_core() {
  _impl_.batch_size_per_tensor_core_ = 0;
}
inline int32_t TPUEmbeddingConfiguration::_internal_batch_size_per_tensor_core() const {
  return _impl_.batch_size_per_tensor_core_;
}
inline int32_t TPUEmbeddingConfiguration::batch_size_per_tensor_core() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.batch_size_per_tensor_core)
  return _internal_batch_size_per_tensor_core();
}
inline void TPUEmbeddingConfiguration::_internal_set_batch_size_per_tensor_core(int32_t value) {
  
  _impl_.batch_size_per_tensor_core_ = value;
}
inline void TPUEmbeddingConfiguration::set_batch_size_per_tensor_core(int32_t value) {
  _internal_set_batch_size_per_tensor_core(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.batch_size_per_tensor_core)
}

// int32 num_hosts = 4;
inline void TPUEmbeddingConfiguration::clear_num_hosts() {
  _impl_.num_hosts_ = 0;
}
inline int32_t TPUEmbeddingConfiguration::_internal_num_hosts() const {
  return _impl_.num_hosts_;
}
inline int32_t TPUEmbeddingConfiguration::num_hosts() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.num_hosts)
  return _internal_num_hosts();
}
inline void TPUEmbeddingConfiguration::_internal_set_num_hosts(int32_t value) {
  
  _impl_.num_hosts_ = value;
}
inline void TPUEmbeddingConfiguration::set_num_hosts(int32_t value) {
  _internal_set_num_hosts(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.num_hosts)
}

// int32 num_tensor_cores = 5;
inline void TPUEmbeddingConfiguration::clear_num_tensor_cores() {
  _impl_.num_tensor_cores_ = 0;
}
inline int32_t TPUEmbeddingConfiguration::_internal_num_tensor_cores() const {
  return _impl_.num_tensor_cores_;
}
inline int32_t TPUEmbeddingConfiguration::num_tensor_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.num_tensor_cores)
  return _internal_num_tensor_cores();
}
inline void TPUEmbeddingConfiguration::_internal_set_num_tensor_cores(int32_t value) {
  
  _impl_.num_tensor_cores_ = value;
}
inline void TPUEmbeddingConfiguration::set_num_tensor_cores(int32_t value) {
  _internal_set_num_tensor_cores(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.num_tensor_cores)
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.ShardingStrategy sharding_strategy = 6;
inline void TPUEmbeddingConfiguration::clear_sharding_strategy() {
  _impl_.sharding_strategy_ = 0;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::_internal_sharding_strategy() const {
  return static_cast< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy >(_impl_.sharding_strategy_);
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy TPUEmbeddingConfiguration::sharding_strategy() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.sharding_strategy)
  return _internal_sharding_strategy();
}
inline void TPUEmbeddingConfiguration::_internal_set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value) {
  
  _impl_.sharding_strategy_ = value;
}
inline void TPUEmbeddingConfiguration::set_sharding_strategy(::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy value) {
  _internal_set_sharding_strategy(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.sharding_strategy)
}

// bool pipeline_execution_with_tensor_core = 7;
inline void TPUEmbeddingConfiguration::clear_pipeline_execution_with_tensor_core() {
  _impl_.pipeline_execution_with_tensor_core_ = false;
}
inline bool TPUEmbeddingConfiguration::_internal_pipeline_execution_with_tensor_core() const {
  return _impl_.pipeline_execution_with_tensor_core_;
}
inline bool TPUEmbeddingConfiguration::pipeline_execution_with_tensor_core() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.pipeline_execution_with_tensor_core)
  return _internal_pipeline_execution_with_tensor_core();
}
inline void TPUEmbeddingConfiguration::_internal_set_pipeline_execution_with_tensor_core(bool value) {
  
  _impl_.pipeline_execution_with_tensor_core_ = value;
}
inline void TPUEmbeddingConfiguration::set_pipeline_execution_with_tensor_core(bool value) {
  _internal_set_pipeline_execution_with_tensor_core(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.pipeline_execution_with_tensor_core)
}

// string profile_data_directory = 9;
inline void TPUEmbeddingConfiguration::clear_profile_data_directory() {
  _impl_.profile_data_directory_.ClearToEmpty();
}
inline const std::string& TPUEmbeddingConfiguration::profile_data_directory() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
  return _internal_profile_data_directory();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TPUEmbeddingConfiguration::set_profile_data_directory(ArgT0&& arg0, ArgT... args) {
 
 _impl_.profile_data_directory_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}
inline std::string* TPUEmbeddingConfiguration::mutable_profile_data_directory() {
  std::string* _s = _internal_mutable_profile_data_directory();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
  return _s;
}
inline const std::string& TPUEmbeddingConfiguration::_internal_profile_data_directory() const {
  return _impl_.profile_data_directory_.Get();
}
inline void TPUEmbeddingConfiguration::_internal_set_profile_data_directory(const std::string& value) {
  
  _impl_.profile_data_directory_.Set(value, GetArenaForAllocation());
}
inline std::string* TPUEmbeddingConfiguration::_internal_mutable_profile_data_directory() {
  
  return _impl_.profile_data_directory_.Mutable(GetArenaForAllocation());
}
inline std::string* TPUEmbeddingConfiguration::release_profile_data_directory() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
  return _impl_.profile_data_directory_.Release();
}
inline void TPUEmbeddingConfiguration::set_allocated_profile_data_directory(std::string* profile_data_directory) {
  if (profile_data_directory != nullptr) {
    
  } else {
    
  }
  _impl_.profile_data_directory_.SetAllocated(profile_data_directory, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.profile_data_directory_.IsDefault()) {
    _impl_.profile_data_directory_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.profile_data_directory)
}

// repeated .tensorflow.tpu.TPUEmbeddingConfiguration.FeatureDescriptor feature_descriptor = 10;
inline int TPUEmbeddingConfiguration::_internal_feature_descriptor_size() const {
  return _impl_.feature_descriptor_.size();
}
inline int TPUEmbeddingConfiguration::feature_descriptor_size() const {
  return _internal_feature_descriptor_size();
}
inline void TPUEmbeddingConfiguration::clear_feature_descriptor() {
  _impl_.feature_descriptor_.Clear();
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* TPUEmbeddingConfiguration::mutable_feature_descriptor(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.feature_descriptor)
  return _impl_.feature_descriptor_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor >*
TPUEmbeddingConfiguration::mutable_feature_descriptor() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.tpu.TPUEmbeddingConfiguration.feature_descriptor)
  return &_impl_.feature_descriptor_;
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor& TPUEmbeddingConfiguration::_internal_feature_descriptor(int index) const {
  return _impl_.feature_descriptor_.Get(index);
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor& TPUEmbeddingConfiguration::feature_descriptor(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.feature_descriptor)
  return _internal_feature_descriptor(index);
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* TPUEmbeddingConfiguration::_internal_add_feature_descriptor() {
  return _impl_.feature_descriptor_.Add();
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* TPUEmbeddingConfiguration::add_feature_descriptor() {
  ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor* _add = _internal_add_feature_descriptor();
  // @@protoc_insertion_point(field_add:tensorflow.tpu.TPUEmbeddingConfiguration.feature_descriptor)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::tpu::TPUEmbeddingConfiguration_FeatureDescriptor >&
TPUEmbeddingConfiguration::feature_descriptor() const {
  // @@protoc_insertion_point(field_list:tensorflow.tpu.TPUEmbeddingConfiguration.feature_descriptor)
  return _impl_.feature_descriptor_;
}

// .tensorflow.tpu.TPUEmbeddingConfiguration.SpmdSharding spmd_sharding = 11;
inline bool TPUEmbeddingConfiguration::_internal_has_spmd_sharding() const {
  return this != internal_default_instance() && _impl_.spmd_sharding_ != nullptr;
}
inline bool TPUEmbeddingConfiguration::has_spmd_sharding() const {
  return _internal_has_spmd_sharding();
}
inline void TPUEmbeddingConfiguration::clear_spmd_sharding() {
  if (GetArenaForAllocation() == nullptr && _impl_.spmd_sharding_ != nullptr) {
    delete _impl_.spmd_sharding_;
  }
  _impl_.spmd_sharding_ = nullptr;
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding& TPUEmbeddingConfiguration::_internal_spmd_sharding() const {
  const ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* p = _impl_.spmd_sharding_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding&>(
      ::tensorflow::tpu::_TPUEmbeddingConfiguration_SpmdSharding_default_instance_);
}
inline const ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding& TPUEmbeddingConfiguration::spmd_sharding() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.TPUEmbeddingConfiguration.spmd_sharding)
  return _internal_spmd_sharding();
}
inline void TPUEmbeddingConfiguration::unsafe_arena_set_allocated_spmd_sharding(
    ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* spmd_sharding) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.spmd_sharding_);
  }
  _impl_.spmd_sharding_ = spmd_sharding;
  if (spmd_sharding) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.spmd_sharding)
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* TPUEmbeddingConfiguration::release_spmd_sharding() {
  
  ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* temp = _impl_.spmd_sharding_;
  _impl_.spmd_sharding_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* TPUEmbeddingConfiguration::unsafe_arena_release_spmd_sharding() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.TPUEmbeddingConfiguration.spmd_sharding)
  
  ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* temp = _impl_.spmd_sharding_;
  _impl_.spmd_sharding_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* TPUEmbeddingConfiguration::_internal_mutable_spmd_sharding() {
  
  if (_impl_.spmd_sharding_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding>(GetArenaForAllocation());
    _impl_.spmd_sharding_ = p;
  }
  return _impl_.spmd_sharding_;
}
inline ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* TPUEmbeddingConfiguration::mutable_spmd_sharding() {
  ::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* _msg = _internal_mutable_spmd_sharding();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.TPUEmbeddingConfiguration.spmd_sharding)
  return _msg;
}
inline void TPUEmbeddingConfiguration::set_allocated_spmd_sharding(::tensorflow::tpu::TPUEmbeddingConfiguration_SpmdSharding* spmd_sharding) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.spmd_sharding_;
  }
  if (spmd_sharding) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(spmd_sharding);
    if (message_arena != submessage_arena) {
      spmd_sharding = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, spmd_sharding, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.spmd_sharding_ = spmd_sharding;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.TPUEmbeddingConfiguration.spmd_sharding)
}

// -------------------------------------------------------------------

// TPUEmbeddingError

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode>() {
  return ::tensorflow::tpu::TPUEmbeddingConfiguration_Mode_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy>() {
  return ::tensorflow::tpu::TPUEmbeddingConfiguration_ShardingStrategy_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2ftpu_5fembedding_5fconfiguration_2eproto
