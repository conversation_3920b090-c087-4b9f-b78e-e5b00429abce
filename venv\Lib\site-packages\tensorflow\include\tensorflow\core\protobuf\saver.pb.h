// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/saver.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto;
namespace tensorflow {
class SaverDef;
struct SaverDefDefaultTypeInternal;
extern SaverDefDefaultTypeInternal _SaverDef_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::SaverDef* Arena::CreateMaybeMessage<::tensorflow::SaverDef>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum SaverDef_CheckpointFormatVersion : int {
  SaverDef_CheckpointFormatVersion_LEGACY = 0,
  SaverDef_CheckpointFormatVersion_V1 = 1,
  SaverDef_CheckpointFormatVersion_V2 = 2,
  SaverDef_CheckpointFormatVersion_SaverDef_CheckpointFormatVersion_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SaverDef_CheckpointFormatVersion_SaverDef_CheckpointFormatVersion_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SaverDef_CheckpointFormatVersion_IsValid(int value);
constexpr SaverDef_CheckpointFormatVersion SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MIN = SaverDef_CheckpointFormatVersion_LEGACY;
constexpr SaverDef_CheckpointFormatVersion SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MAX = SaverDef_CheckpointFormatVersion_V2;
constexpr int SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_ARRAYSIZE = SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SaverDef_CheckpointFormatVersion_descriptor();
template<typename T>
inline const std::string& SaverDef_CheckpointFormatVersion_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SaverDef_CheckpointFormatVersion>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SaverDef_CheckpointFormatVersion_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SaverDef_CheckpointFormatVersion_descriptor(), enum_t_value);
}
inline bool SaverDef_CheckpointFormatVersion_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SaverDef_CheckpointFormatVersion* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SaverDef_CheckpointFormatVersion>(
    SaverDef_CheckpointFormatVersion_descriptor(), name, value);
}
// ===================================================================

class SaverDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SaverDef) */ {
 public:
  inline SaverDef() : SaverDef(nullptr) {}
  ~SaverDef() override;
  explicit PROTOBUF_CONSTEXPR SaverDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaverDef(const SaverDef& from);
  SaverDef(SaverDef&& from) noexcept
    : SaverDef() {
    *this = ::std::move(from);
  }

  inline SaverDef& operator=(const SaverDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaverDef& operator=(SaverDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaverDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaverDef* internal_default_instance() {
    return reinterpret_cast<const SaverDef*>(
               &_SaverDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SaverDef& a, SaverDef& b) {
    a.Swap(&b);
  }
  inline void Swap(SaverDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaverDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaverDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaverDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaverDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SaverDef& from) {
    SaverDef::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaverDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SaverDef";
  }
  protected:
  explicit SaverDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef SaverDef_CheckpointFormatVersion CheckpointFormatVersion;
  static constexpr CheckpointFormatVersion LEGACY =
    SaverDef_CheckpointFormatVersion_LEGACY;
  static constexpr CheckpointFormatVersion V1 =
    SaverDef_CheckpointFormatVersion_V1;
  static constexpr CheckpointFormatVersion V2 =
    SaverDef_CheckpointFormatVersion_V2;
  static inline bool CheckpointFormatVersion_IsValid(int value) {
    return SaverDef_CheckpointFormatVersion_IsValid(value);
  }
  static constexpr CheckpointFormatVersion CheckpointFormatVersion_MIN =
    SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MIN;
  static constexpr CheckpointFormatVersion CheckpointFormatVersion_MAX =
    SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_MAX;
  static constexpr int CheckpointFormatVersion_ARRAYSIZE =
    SaverDef_CheckpointFormatVersion_CheckpointFormatVersion_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  CheckpointFormatVersion_descriptor() {
    return SaverDef_CheckpointFormatVersion_descriptor();
  }
  template<typename T>
  static inline const std::string& CheckpointFormatVersion_Name(T enum_t_value) {
    static_assert(::std::is_same<T, CheckpointFormatVersion>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function CheckpointFormatVersion_Name.");
    return SaverDef_CheckpointFormatVersion_Name(enum_t_value);
  }
  static inline bool CheckpointFormatVersion_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      CheckpointFormatVersion* value) {
    return SaverDef_CheckpointFormatVersion_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kFilenameTensorNameFieldNumber = 1,
    kSaveTensorNameFieldNumber = 2,
    kRestoreOpNameFieldNumber = 3,
    kMaxToKeepFieldNumber = 4,
    kShardedFieldNumber = 5,
    kKeepCheckpointEveryNHoursFieldNumber = 6,
    kVersionFieldNumber = 7,
  };
  // string filename_tensor_name = 1;
  void clear_filename_tensor_name();
  const std::string& filename_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_filename_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_filename_tensor_name();
  PROTOBUF_NODISCARD std::string* release_filename_tensor_name();
  void set_allocated_filename_tensor_name(std::string* filename_tensor_name);
  private:
  const std::string& _internal_filename_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_filename_tensor_name(const std::string& value);
  std::string* _internal_mutable_filename_tensor_name();
  public:

  // string save_tensor_name = 2;
  void clear_save_tensor_name();
  const std::string& save_tensor_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_save_tensor_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_save_tensor_name();
  PROTOBUF_NODISCARD std::string* release_save_tensor_name();
  void set_allocated_save_tensor_name(std::string* save_tensor_name);
  private:
  const std::string& _internal_save_tensor_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_save_tensor_name(const std::string& value);
  std::string* _internal_mutable_save_tensor_name();
  public:

  // string restore_op_name = 3;
  void clear_restore_op_name();
  const std::string& restore_op_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_restore_op_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_restore_op_name();
  PROTOBUF_NODISCARD std::string* release_restore_op_name();
  void set_allocated_restore_op_name(std::string* restore_op_name);
  private:
  const std::string& _internal_restore_op_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_restore_op_name(const std::string& value);
  std::string* _internal_mutable_restore_op_name();
  public:

  // int32 max_to_keep = 4;
  void clear_max_to_keep();
  int32_t max_to_keep() const;
  void set_max_to_keep(int32_t value);
  private:
  int32_t _internal_max_to_keep() const;
  void _internal_set_max_to_keep(int32_t value);
  public:

  // bool sharded = 5;
  void clear_sharded();
  bool sharded() const;
  void set_sharded(bool value);
  private:
  bool _internal_sharded() const;
  void _internal_set_sharded(bool value);
  public:

  // float keep_checkpoint_every_n_hours = 6;
  void clear_keep_checkpoint_every_n_hours();
  float keep_checkpoint_every_n_hours() const;
  void set_keep_checkpoint_every_n_hours(float value);
  private:
  float _internal_keep_checkpoint_every_n_hours() const;
  void _internal_set_keep_checkpoint_every_n_hours(float value);
  public:

  // .tensorflow.SaverDef.CheckpointFormatVersion version = 7;
  void clear_version();
  ::tensorflow::SaverDef_CheckpointFormatVersion version() const;
  void set_version(::tensorflow::SaverDef_CheckpointFormatVersion value);
  private:
  ::tensorflow::SaverDef_CheckpointFormatVersion _internal_version() const;
  void _internal_set_version(::tensorflow::SaverDef_CheckpointFormatVersion value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SaverDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr filename_tensor_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr save_tensor_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr restore_op_name_;
    int32_t max_to_keep_;
    bool sharded_;
    float keep_checkpoint_every_n_hours_;
    int version_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SaverDef

// string filename_tensor_name = 1;
inline void SaverDef::clear_filename_tensor_name() {
  _impl_.filename_tensor_name_.ClearToEmpty();
}
inline const std::string& SaverDef::filename_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.filename_tensor_name)
  return _internal_filename_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaverDef::set_filename_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.filename_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.filename_tensor_name)
}
inline std::string* SaverDef::mutable_filename_tensor_name() {
  std::string* _s = _internal_mutable_filename_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SaverDef.filename_tensor_name)
  return _s;
}
inline const std::string& SaverDef::_internal_filename_tensor_name() const {
  return _impl_.filename_tensor_name_.Get();
}
inline void SaverDef::_internal_set_filename_tensor_name(const std::string& value) {
  
  _impl_.filename_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SaverDef::_internal_mutable_filename_tensor_name() {
  
  return _impl_.filename_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SaverDef::release_filename_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaverDef.filename_tensor_name)
  return _impl_.filename_tensor_name_.Release();
}
inline void SaverDef::set_allocated_filename_tensor_name(std::string* filename_tensor_name) {
  if (filename_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.filename_tensor_name_.SetAllocated(filename_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.filename_tensor_name_.IsDefault()) {
    _impl_.filename_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaverDef.filename_tensor_name)
}

// string save_tensor_name = 2;
inline void SaverDef::clear_save_tensor_name() {
  _impl_.save_tensor_name_.ClearToEmpty();
}
inline const std::string& SaverDef::save_tensor_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.save_tensor_name)
  return _internal_save_tensor_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaverDef::set_save_tensor_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.save_tensor_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.save_tensor_name)
}
inline std::string* SaverDef::mutable_save_tensor_name() {
  std::string* _s = _internal_mutable_save_tensor_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SaverDef.save_tensor_name)
  return _s;
}
inline const std::string& SaverDef::_internal_save_tensor_name() const {
  return _impl_.save_tensor_name_.Get();
}
inline void SaverDef::_internal_set_save_tensor_name(const std::string& value) {
  
  _impl_.save_tensor_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SaverDef::_internal_mutable_save_tensor_name() {
  
  return _impl_.save_tensor_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SaverDef::release_save_tensor_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaverDef.save_tensor_name)
  return _impl_.save_tensor_name_.Release();
}
inline void SaverDef::set_allocated_save_tensor_name(std::string* save_tensor_name) {
  if (save_tensor_name != nullptr) {
    
  } else {
    
  }
  _impl_.save_tensor_name_.SetAllocated(save_tensor_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.save_tensor_name_.IsDefault()) {
    _impl_.save_tensor_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaverDef.save_tensor_name)
}

// string restore_op_name = 3;
inline void SaverDef::clear_restore_op_name() {
  _impl_.restore_op_name_.ClearToEmpty();
}
inline const std::string& SaverDef::restore_op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.restore_op_name)
  return _internal_restore_op_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SaverDef::set_restore_op_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.restore_op_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.restore_op_name)
}
inline std::string* SaverDef::mutable_restore_op_name() {
  std::string* _s = _internal_mutable_restore_op_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.SaverDef.restore_op_name)
  return _s;
}
inline const std::string& SaverDef::_internal_restore_op_name() const {
  return _impl_.restore_op_name_.Get();
}
inline void SaverDef::_internal_set_restore_op_name(const std::string& value) {
  
  _impl_.restore_op_name_.Set(value, GetArenaForAllocation());
}
inline std::string* SaverDef::_internal_mutable_restore_op_name() {
  
  return _impl_.restore_op_name_.Mutable(GetArenaForAllocation());
}
inline std::string* SaverDef::release_restore_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.SaverDef.restore_op_name)
  return _impl_.restore_op_name_.Release();
}
inline void SaverDef::set_allocated_restore_op_name(std::string* restore_op_name) {
  if (restore_op_name != nullptr) {
    
  } else {
    
  }
  _impl_.restore_op_name_.SetAllocated(restore_op_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.restore_op_name_.IsDefault()) {
    _impl_.restore_op_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.SaverDef.restore_op_name)
}

// int32 max_to_keep = 4;
inline void SaverDef::clear_max_to_keep() {
  _impl_.max_to_keep_ = 0;
}
inline int32_t SaverDef::_internal_max_to_keep() const {
  return _impl_.max_to_keep_;
}
inline int32_t SaverDef::max_to_keep() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.max_to_keep)
  return _internal_max_to_keep();
}
inline void SaverDef::_internal_set_max_to_keep(int32_t value) {
  
  _impl_.max_to_keep_ = value;
}
inline void SaverDef::set_max_to_keep(int32_t value) {
  _internal_set_max_to_keep(value);
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.max_to_keep)
}

// bool sharded = 5;
inline void SaverDef::clear_sharded() {
  _impl_.sharded_ = false;
}
inline bool SaverDef::_internal_sharded() const {
  return _impl_.sharded_;
}
inline bool SaverDef::sharded() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.sharded)
  return _internal_sharded();
}
inline void SaverDef::_internal_set_sharded(bool value) {
  
  _impl_.sharded_ = value;
}
inline void SaverDef::set_sharded(bool value) {
  _internal_set_sharded(value);
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.sharded)
}

// float keep_checkpoint_every_n_hours = 6;
inline void SaverDef::clear_keep_checkpoint_every_n_hours() {
  _impl_.keep_checkpoint_every_n_hours_ = 0;
}
inline float SaverDef::_internal_keep_checkpoint_every_n_hours() const {
  return _impl_.keep_checkpoint_every_n_hours_;
}
inline float SaverDef::keep_checkpoint_every_n_hours() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.keep_checkpoint_every_n_hours)
  return _internal_keep_checkpoint_every_n_hours();
}
inline void SaverDef::_internal_set_keep_checkpoint_every_n_hours(float value) {
  
  _impl_.keep_checkpoint_every_n_hours_ = value;
}
inline void SaverDef::set_keep_checkpoint_every_n_hours(float value) {
  _internal_set_keep_checkpoint_every_n_hours(value);
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.keep_checkpoint_every_n_hours)
}

// .tensorflow.SaverDef.CheckpointFormatVersion version = 7;
inline void SaverDef::clear_version() {
  _impl_.version_ = 0;
}
inline ::tensorflow::SaverDef_CheckpointFormatVersion SaverDef::_internal_version() const {
  return static_cast< ::tensorflow::SaverDef_CheckpointFormatVersion >(_impl_.version_);
}
inline ::tensorflow::SaverDef_CheckpointFormatVersion SaverDef::version() const {
  // @@protoc_insertion_point(field_get:tensorflow.SaverDef.version)
  return _internal_version();
}
inline void SaverDef::_internal_set_version(::tensorflow::SaverDef_CheckpointFormatVersion value) {
  
  _impl_.version_ = value;
}
inline void SaverDef::set_version(::tensorflow::SaverDef_CheckpointFormatVersion value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:tensorflow.SaverDef.version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::SaverDef_CheckpointFormatVersion> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::SaverDef_CheckpointFormatVersion>() {
  return ::tensorflow::SaverDef_CheckpointFormatVersion_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fsaver_2eproto
