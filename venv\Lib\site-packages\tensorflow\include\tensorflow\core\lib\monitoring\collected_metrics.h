/* Copyright 2016 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

// Standard format in which the metrics are collected, before being exported.
// These are to be used only by the CollectionRegistry and exporters which
// collect metrics using the CollectionRegistry.

#ifndef TENSORFLOW_CORE_LIB_MONITORING_COLLECTED_METRICS_H_
#define TENSORFLOW_CORE_LIB_MONITORING_COLLECTED_METRICS_H_

#include <map>
#include <memory>
#include <string>
#include <vector>

#include "tensorflow/core/framework/summary.pb.h"
#include "tensorflow/core/lib/monitoring/metric_def.h"
#include "tensorflow/core/lib/monitoring/types.h"
#include "tsl/lib/monitoring/collected_metrics.h"
// NOLINTBEGIN(misc-unused-using-decls)
namespace tensorflow {
namespace monitoring {
using tsl::monitoring::CollectedMetrics;
using tsl::monitoring::MetricDescriptor;
using tsl::monitoring::Point;
using tsl::monitoring::PointSet;
}  // namespace monitoring
}  // namespace tensorflow
// NOLINTEND(misc-unused-using-decls)
#endif  // TENSORFLOW_CORE_LIB_MONITORING_COLLECTED_METRICS_H_
