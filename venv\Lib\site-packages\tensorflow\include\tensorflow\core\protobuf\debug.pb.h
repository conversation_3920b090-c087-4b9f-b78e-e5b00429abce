// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/debug.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
namespace tensorflow {
class DebugOptions;
struct DebugOptionsDefaultTypeInternal;
extern DebugOptionsDefaultTypeInternal _DebugOptions_default_instance_;
class DebugTensorWatch;
struct DebugTensorWatchDefaultTypeInternal;
extern DebugTensorWatchDefaultTypeInternal _DebugTensorWatch_default_instance_;
class DebuggedSourceFile;
struct DebuggedSourceFileDefaultTypeInternal;
extern DebuggedSourceFileDefaultTypeInternal _DebuggedSourceFile_default_instance_;
class DebuggedSourceFiles;
struct DebuggedSourceFilesDefaultTypeInternal;
extern DebuggedSourceFilesDefaultTypeInternal _DebuggedSourceFiles_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::DebugOptions* Arena::CreateMaybeMessage<::tensorflow::DebugOptions>(Arena*);
template<> ::tensorflow::DebugTensorWatch* Arena::CreateMaybeMessage<::tensorflow::DebugTensorWatch>(Arena*);
template<> ::tensorflow::DebuggedSourceFile* Arena::CreateMaybeMessage<::tensorflow::DebuggedSourceFile>(Arena*);
template<> ::tensorflow::DebuggedSourceFiles* Arena::CreateMaybeMessage<::tensorflow::DebuggedSourceFiles>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class DebugTensorWatch final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugTensorWatch) */ {
 public:
  inline DebugTensorWatch() : DebugTensorWatch(nullptr) {}
  ~DebugTensorWatch() override;
  explicit PROTOBUF_CONSTEXPR DebugTensorWatch(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebugTensorWatch(const DebugTensorWatch& from);
  DebugTensorWatch(DebugTensorWatch&& from) noexcept
    : DebugTensorWatch() {
    *this = ::std::move(from);
  }

  inline DebugTensorWatch& operator=(const DebugTensorWatch& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugTensorWatch& operator=(DebugTensorWatch&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebugTensorWatch& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebugTensorWatch* internal_default_instance() {
    return reinterpret_cast<const DebugTensorWatch*>(
               &_DebugTensorWatch_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DebugTensorWatch& a, DebugTensorWatch& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugTensorWatch* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugTensorWatch* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebugTensorWatch* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebugTensorWatch>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebugTensorWatch& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebugTensorWatch& from) {
    DebugTensorWatch::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugTensorWatch* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugTensorWatch";
  }
  protected:
  explicit DebugTensorWatch(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDebugOpsFieldNumber = 3,
    kDebugUrlsFieldNumber = 4,
    kNodeNameFieldNumber = 1,
    kOutputSlotFieldNumber = 2,
    kTolerateDebugOpCreationFailuresFieldNumber = 5,
  };
  // repeated string debug_ops = 3;
  int debug_ops_size() const;
  private:
  int _internal_debug_ops_size() const;
  public:
  void clear_debug_ops();
  const std::string& debug_ops(int index) const;
  std::string* mutable_debug_ops(int index);
  void set_debug_ops(int index, const std::string& value);
  void set_debug_ops(int index, std::string&& value);
  void set_debug_ops(int index, const char* value);
  void set_debug_ops(int index, const char* value, size_t size);
  std::string* add_debug_ops();
  void add_debug_ops(const std::string& value);
  void add_debug_ops(std::string&& value);
  void add_debug_ops(const char* value);
  void add_debug_ops(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& debug_ops() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_debug_ops();
  private:
  const std::string& _internal_debug_ops(int index) const;
  std::string* _internal_add_debug_ops();
  public:

  // repeated string debug_urls = 4;
  int debug_urls_size() const;
  private:
  int _internal_debug_urls_size() const;
  public:
  void clear_debug_urls();
  const std::string& debug_urls(int index) const;
  std::string* mutable_debug_urls(int index);
  void set_debug_urls(int index, const std::string& value);
  void set_debug_urls(int index, std::string&& value);
  void set_debug_urls(int index, const char* value);
  void set_debug_urls(int index, const char* value, size_t size);
  std::string* add_debug_urls();
  void add_debug_urls(const std::string& value);
  void add_debug_urls(std::string&& value);
  void add_debug_urls(const char* value);
  void add_debug_urls(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& debug_urls() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_debug_urls();
  private:
  const std::string& _internal_debug_urls(int index) const;
  std::string* _internal_add_debug_urls();
  public:

  // string node_name = 1;
  void clear_node_name();
  const std::string& node_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_node_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_node_name();
  PROTOBUF_NODISCARD std::string* release_node_name();
  void set_allocated_node_name(std::string* node_name);
  private:
  const std::string& _internal_node_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_node_name(const std::string& value);
  std::string* _internal_mutable_node_name();
  public:

  // int32 output_slot = 2;
  void clear_output_slot();
  int32_t output_slot() const;
  void set_output_slot(int32_t value);
  private:
  int32_t _internal_output_slot() const;
  void _internal_set_output_slot(int32_t value);
  public:

  // bool tolerate_debug_op_creation_failures = 5;
  void clear_tolerate_debug_op_creation_failures();
  bool tolerate_debug_op_creation_failures() const;
  void set_tolerate_debug_op_creation_failures(bool value);
  private:
  bool _internal_tolerate_debug_op_creation_failures() const;
  void _internal_set_tolerate_debug_op_creation_failures(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DebugTensorWatch)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> debug_ops_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> debug_urls_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr node_name_;
    int32_t output_slot_;
    bool tolerate_debug_op_creation_failures_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class DebugOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebugOptions) */ {
 public:
  inline DebugOptions() : DebugOptions(nullptr) {}
  ~DebugOptions() override;
  explicit PROTOBUF_CONSTEXPR DebugOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebugOptions(const DebugOptions& from);
  DebugOptions(DebugOptions&& from) noexcept
    : DebugOptions() {
    *this = ::std::move(from);
  }

  inline DebugOptions& operator=(const DebugOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebugOptions& operator=(DebugOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebugOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebugOptions* internal_default_instance() {
    return reinterpret_cast<const DebugOptions*>(
               &_DebugOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DebugOptions& a, DebugOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(DebugOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebugOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebugOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebugOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebugOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebugOptions& from) {
    DebugOptions::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebugOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebugOptions";
  }
  protected:
  explicit DebugOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDebugTensorWatchOptsFieldNumber = 4,
    kGlobalStepFieldNumber = 10,
    kResetDiskByteUsageFieldNumber = 11,
  };
  // repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
  int debug_tensor_watch_opts_size() const;
  private:
  int _internal_debug_tensor_watch_opts_size() const;
  public:
  void clear_debug_tensor_watch_opts();
  ::tensorflow::DebugTensorWatch* mutable_debug_tensor_watch_opts(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >*
      mutable_debug_tensor_watch_opts();
  private:
  const ::tensorflow::DebugTensorWatch& _internal_debug_tensor_watch_opts(int index) const;
  ::tensorflow::DebugTensorWatch* _internal_add_debug_tensor_watch_opts();
  public:
  const ::tensorflow::DebugTensorWatch& debug_tensor_watch_opts(int index) const;
  ::tensorflow::DebugTensorWatch* add_debug_tensor_watch_opts();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >&
      debug_tensor_watch_opts() const;

  // int64 global_step = 10;
  void clear_global_step();
  int64_t global_step() const;
  void set_global_step(int64_t value);
  private:
  int64_t _internal_global_step() const;
  void _internal_set_global_step(int64_t value);
  public:

  // bool reset_disk_byte_usage = 11;
  void clear_reset_disk_byte_usage();
  bool reset_disk_byte_usage() const;
  void set_reset_disk_byte_usage(bool value);
  private:
  bool _internal_reset_disk_byte_usage() const;
  void _internal_set_reset_disk_byte_usage(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DebugOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch > debug_tensor_watch_opts_;
    int64_t global_step_;
    bool reset_disk_byte_usage_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class DebuggedSourceFile final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedSourceFile) */ {
 public:
  inline DebuggedSourceFile() : DebuggedSourceFile(nullptr) {}
  ~DebuggedSourceFile() override;
  explicit PROTOBUF_CONSTEXPR DebuggedSourceFile(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebuggedSourceFile(const DebuggedSourceFile& from);
  DebuggedSourceFile(DebuggedSourceFile&& from) noexcept
    : DebuggedSourceFile() {
    *this = ::std::move(from);
  }

  inline DebuggedSourceFile& operator=(const DebuggedSourceFile& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedSourceFile& operator=(DebuggedSourceFile&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebuggedSourceFile& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebuggedSourceFile* internal_default_instance() {
    return reinterpret_cast<const DebuggedSourceFile*>(
               &_DebuggedSourceFile_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DebuggedSourceFile& a, DebuggedSourceFile& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedSourceFile* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedSourceFile* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebuggedSourceFile* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebuggedSourceFile>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebuggedSourceFile& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebuggedSourceFile& from) {
    DebuggedSourceFile::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedSourceFile* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedSourceFile";
  }
  protected:
  explicit DebuggedSourceFile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinesFieldNumber = 5,
    kHostFieldNumber = 1,
    kFilePathFieldNumber = 2,
    kLastModifiedFieldNumber = 3,
    kBytesFieldNumber = 4,
  };
  // repeated string lines = 5;
  int lines_size() const;
  private:
  int _internal_lines_size() const;
  public:
  void clear_lines();
  const std::string& lines(int index) const;
  std::string* mutable_lines(int index);
  void set_lines(int index, const std::string& value);
  void set_lines(int index, std::string&& value);
  void set_lines(int index, const char* value);
  void set_lines(int index, const char* value, size_t size);
  std::string* add_lines();
  void add_lines(const std::string& value);
  void add_lines(std::string&& value);
  void add_lines(const char* value);
  void add_lines(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& lines() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_lines();
  private:
  const std::string& _internal_lines(int index) const;
  std::string* _internal_add_lines();
  public:

  // string host = 1;
  void clear_host();
  const std::string& host() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host();
  PROTOBUF_NODISCARD std::string* release_host();
  void set_allocated_host(std::string* host);
  private:
  const std::string& _internal_host() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host(const std::string& value);
  std::string* _internal_mutable_host();
  public:

  // string file_path = 2;
  void clear_file_path();
  const std::string& file_path() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_file_path(ArgT0&& arg0, ArgT... args);
  std::string* mutable_file_path();
  PROTOBUF_NODISCARD std::string* release_file_path();
  void set_allocated_file_path(std::string* file_path);
  private:
  const std::string& _internal_file_path() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_file_path(const std::string& value);
  std::string* _internal_mutable_file_path();
  public:

  // int64 last_modified = 3;
  void clear_last_modified();
  int64_t last_modified() const;
  void set_last_modified(int64_t value);
  private:
  int64_t _internal_last_modified() const;
  void _internal_set_last_modified(int64_t value);
  public:

  // int64 bytes = 4;
  void clear_bytes();
  int64_t bytes() const;
  void set_bytes(int64_t value);
  private:
  int64_t _internal_bytes() const;
  void _internal_set_bytes(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedSourceFile)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> lines_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr file_path_;
    int64_t last_modified_;
    int64_t bytes_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class DebuggedSourceFiles final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DebuggedSourceFiles) */ {
 public:
  inline DebuggedSourceFiles() : DebuggedSourceFiles(nullptr) {}
  ~DebuggedSourceFiles() override;
  explicit PROTOBUF_CONSTEXPR DebuggedSourceFiles(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DebuggedSourceFiles(const DebuggedSourceFiles& from);
  DebuggedSourceFiles(DebuggedSourceFiles&& from) noexcept
    : DebuggedSourceFiles() {
    *this = ::std::move(from);
  }

  inline DebuggedSourceFiles& operator=(const DebuggedSourceFiles& from) {
    CopyFrom(from);
    return *this;
  }
  inline DebuggedSourceFiles& operator=(DebuggedSourceFiles&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DebuggedSourceFiles& default_instance() {
    return *internal_default_instance();
  }
  static inline const DebuggedSourceFiles* internal_default_instance() {
    return reinterpret_cast<const DebuggedSourceFiles*>(
               &_DebuggedSourceFiles_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DebuggedSourceFiles& a, DebuggedSourceFiles& b) {
    a.Swap(&b);
  }
  inline void Swap(DebuggedSourceFiles* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DebuggedSourceFiles* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DebuggedSourceFiles* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DebuggedSourceFiles>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DebuggedSourceFiles& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DebuggedSourceFiles& from) {
    DebuggedSourceFiles::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DebuggedSourceFiles* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DebuggedSourceFiles";
  }
  protected:
  explicit DebuggedSourceFiles(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSourceFilesFieldNumber = 1,
  };
  // repeated .tensorflow.DebuggedSourceFile source_files = 1;
  int source_files_size() const;
  private:
  int _internal_source_files_size() const;
  public:
  void clear_source_files();
  ::tensorflow::DebuggedSourceFile* mutable_source_files(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >*
      mutable_source_files();
  private:
  const ::tensorflow::DebuggedSourceFile& _internal_source_files(int index) const;
  ::tensorflow::DebuggedSourceFile* _internal_add_source_files();
  public:
  const ::tensorflow::DebuggedSourceFile& source_files(int index) const;
  ::tensorflow::DebuggedSourceFile* add_source_files();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >&
      source_files() const;

  // @@protoc_insertion_point(class_scope:tensorflow.DebuggedSourceFiles)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile > source_files_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugTensorWatch

// string node_name = 1;
inline void DebugTensorWatch::clear_node_name() {
  _impl_.node_name_.ClearToEmpty();
}
inline const std::string& DebugTensorWatch::node_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.node_name)
  return _internal_node_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebugTensorWatch::set_node_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.node_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.node_name)
}
inline std::string* DebugTensorWatch::mutable_node_name() {
  std::string* _s = _internal_mutable_node_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.node_name)
  return _s;
}
inline const std::string& DebugTensorWatch::_internal_node_name() const {
  return _impl_.node_name_.Get();
}
inline void DebugTensorWatch::_internal_set_node_name(const std::string& value) {
  
  _impl_.node_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DebugTensorWatch::_internal_mutable_node_name() {
  
  return _impl_.node_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DebugTensorWatch::release_node_name() {
  // @@protoc_insertion_point(field_release:tensorflow.DebugTensorWatch.node_name)
  return _impl_.node_name_.Release();
}
inline void DebugTensorWatch::set_allocated_node_name(std::string* node_name) {
  if (node_name != nullptr) {
    
  } else {
    
  }
  _impl_.node_name_.SetAllocated(node_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.node_name_.IsDefault()) {
    _impl_.node_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebugTensorWatch.node_name)
}

// int32 output_slot = 2;
inline void DebugTensorWatch::clear_output_slot() {
  _impl_.output_slot_ = 0;
}
inline int32_t DebugTensorWatch::_internal_output_slot() const {
  return _impl_.output_slot_;
}
inline int32_t DebugTensorWatch::output_slot() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.output_slot)
  return _internal_output_slot();
}
inline void DebugTensorWatch::_internal_set_output_slot(int32_t value) {
  
  _impl_.output_slot_ = value;
}
inline void DebugTensorWatch::set_output_slot(int32_t value) {
  _internal_set_output_slot(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.output_slot)
}

// repeated string debug_ops = 3;
inline int DebugTensorWatch::_internal_debug_ops_size() const {
  return _impl_.debug_ops_.size();
}
inline int DebugTensorWatch::debug_ops_size() const {
  return _internal_debug_ops_size();
}
inline void DebugTensorWatch::clear_debug_ops() {
  _impl_.debug_ops_.Clear();
}
inline std::string* DebugTensorWatch::add_debug_ops() {
  std::string* _s = _internal_add_debug_ops();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebugTensorWatch.debug_ops)
  return _s;
}
inline const std::string& DebugTensorWatch::_internal_debug_ops(int index) const {
  return _impl_.debug_ops_.Get(index);
}
inline const std::string& DebugTensorWatch::debug_ops(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.debug_ops)
  return _internal_debug_ops(index);
}
inline std::string* DebugTensorWatch::mutable_debug_ops(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.debug_ops)
  return _impl_.debug_ops_.Mutable(index);
}
inline void DebugTensorWatch::set_debug_ops(int index, const std::string& value) {
  _impl_.debug_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::set_debug_ops(int index, std::string&& value) {
  _impl_.debug_ops_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::set_debug_ops(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.debug_ops_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::set_debug_ops(int index, const char* value, size_t size) {
  _impl_.debug_ops_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.debug_ops)
}
inline std::string* DebugTensorWatch::_internal_add_debug_ops() {
  return _impl_.debug_ops_.Add();
}
inline void DebugTensorWatch::add_debug_ops(const std::string& value) {
  _impl_.debug_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(std::string&& value) {
  _impl_.debug_ops_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.debug_ops_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebugTensorWatch.debug_ops)
}
inline void DebugTensorWatch::add_debug_ops(const char* value, size_t size) {
  _impl_.debug_ops_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebugTensorWatch.debug_ops)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugTensorWatch::debug_ops() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugTensorWatch.debug_ops)
  return _impl_.debug_ops_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugTensorWatch::mutable_debug_ops() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugTensorWatch.debug_ops)
  return &_impl_.debug_ops_;
}

// repeated string debug_urls = 4;
inline int DebugTensorWatch::_internal_debug_urls_size() const {
  return _impl_.debug_urls_.size();
}
inline int DebugTensorWatch::debug_urls_size() const {
  return _internal_debug_urls_size();
}
inline void DebugTensorWatch::clear_debug_urls() {
  _impl_.debug_urls_.Clear();
}
inline std::string* DebugTensorWatch::add_debug_urls() {
  std::string* _s = _internal_add_debug_urls();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebugTensorWatch.debug_urls)
  return _s;
}
inline const std::string& DebugTensorWatch::_internal_debug_urls(int index) const {
  return _impl_.debug_urls_.Get(index);
}
inline const std::string& DebugTensorWatch::debug_urls(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.debug_urls)
  return _internal_debug_urls(index);
}
inline std::string* DebugTensorWatch::mutable_debug_urls(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugTensorWatch.debug_urls)
  return _impl_.debug_urls_.Mutable(index);
}
inline void DebugTensorWatch::set_debug_urls(int index, const std::string& value) {
  _impl_.debug_urls_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::set_debug_urls(int index, std::string&& value) {
  _impl_.debug_urls_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::set_debug_urls(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.debug_urls_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::set_debug_urls(int index, const char* value, size_t size) {
  _impl_.debug_urls_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebugTensorWatch.debug_urls)
}
inline std::string* DebugTensorWatch::_internal_add_debug_urls() {
  return _impl_.debug_urls_.Add();
}
inline void DebugTensorWatch::add_debug_urls(const std::string& value) {
  _impl_.debug_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(std::string&& value) {
  _impl_.debug_urls_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.debug_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebugTensorWatch.debug_urls)
}
inline void DebugTensorWatch::add_debug_urls(const char* value, size_t size) {
  _impl_.debug_urls_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebugTensorWatch.debug_urls)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebugTensorWatch::debug_urls() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugTensorWatch.debug_urls)
  return _impl_.debug_urls_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebugTensorWatch::mutable_debug_urls() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugTensorWatch.debug_urls)
  return &_impl_.debug_urls_;
}

// bool tolerate_debug_op_creation_failures = 5;
inline void DebugTensorWatch::clear_tolerate_debug_op_creation_failures() {
  _impl_.tolerate_debug_op_creation_failures_ = false;
}
inline bool DebugTensorWatch::_internal_tolerate_debug_op_creation_failures() const {
  return _impl_.tolerate_debug_op_creation_failures_;
}
inline bool DebugTensorWatch::tolerate_debug_op_creation_failures() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugTensorWatch.tolerate_debug_op_creation_failures)
  return _internal_tolerate_debug_op_creation_failures();
}
inline void DebugTensorWatch::_internal_set_tolerate_debug_op_creation_failures(bool value) {
  
  _impl_.tolerate_debug_op_creation_failures_ = value;
}
inline void DebugTensorWatch::set_tolerate_debug_op_creation_failures(bool value) {
  _internal_set_tolerate_debug_op_creation_failures(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugTensorWatch.tolerate_debug_op_creation_failures)
}

// -------------------------------------------------------------------

// DebugOptions

// repeated .tensorflow.DebugTensorWatch debug_tensor_watch_opts = 4;
inline int DebugOptions::_internal_debug_tensor_watch_opts_size() const {
  return _impl_.debug_tensor_watch_opts_.size();
}
inline int DebugOptions::debug_tensor_watch_opts_size() const {
  return _internal_debug_tensor_watch_opts_size();
}
inline void DebugOptions::clear_debug_tensor_watch_opts() {
  _impl_.debug_tensor_watch_opts_.Clear();
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::mutable_debug_tensor_watch_opts(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return _impl_.debug_tensor_watch_opts_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >*
DebugOptions::mutable_debug_tensor_watch_opts() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return &_impl_.debug_tensor_watch_opts_;
}
inline const ::tensorflow::DebugTensorWatch& DebugOptions::_internal_debug_tensor_watch_opts(int index) const {
  return _impl_.debug_tensor_watch_opts_.Get(index);
}
inline const ::tensorflow::DebugTensorWatch& DebugOptions::debug_tensor_watch_opts(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return _internal_debug_tensor_watch_opts(index);
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::_internal_add_debug_tensor_watch_opts() {
  return _impl_.debug_tensor_watch_opts_.Add();
}
inline ::tensorflow::DebugTensorWatch* DebugOptions::add_debug_tensor_watch_opts() {
  ::tensorflow::DebugTensorWatch* _add = _internal_add_debug_tensor_watch_opts();
  // @@protoc_insertion_point(field_add:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebugTensorWatch >&
DebugOptions::debug_tensor_watch_opts() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebugOptions.debug_tensor_watch_opts)
  return _impl_.debug_tensor_watch_opts_;
}

// int64 global_step = 10;
inline void DebugOptions::clear_global_step() {
  _impl_.global_step_ = int64_t{0};
}
inline int64_t DebugOptions::_internal_global_step() const {
  return _impl_.global_step_;
}
inline int64_t DebugOptions::global_step() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.global_step)
  return _internal_global_step();
}
inline void DebugOptions::_internal_set_global_step(int64_t value) {
  
  _impl_.global_step_ = value;
}
inline void DebugOptions::set_global_step(int64_t value) {
  _internal_set_global_step(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugOptions.global_step)
}

// bool reset_disk_byte_usage = 11;
inline void DebugOptions::clear_reset_disk_byte_usage() {
  _impl_.reset_disk_byte_usage_ = false;
}
inline bool DebugOptions::_internal_reset_disk_byte_usage() const {
  return _impl_.reset_disk_byte_usage_;
}
inline bool DebugOptions::reset_disk_byte_usage() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebugOptions.reset_disk_byte_usage)
  return _internal_reset_disk_byte_usage();
}
inline void DebugOptions::_internal_set_reset_disk_byte_usage(bool value) {
  
  _impl_.reset_disk_byte_usage_ = value;
}
inline void DebugOptions::set_reset_disk_byte_usage(bool value) {
  _internal_set_reset_disk_byte_usage(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebugOptions.reset_disk_byte_usage)
}

// -------------------------------------------------------------------

// DebuggedSourceFile

// string host = 1;
inline void DebuggedSourceFile::clear_host() {
  _impl_.host_.ClearToEmpty();
}
inline const std::string& DebuggedSourceFile::host() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.host)
  return _internal_host();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedSourceFile::set_host(ArgT0&& arg0, ArgT... args) {
 
 _impl_.host_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.host)
}
inline std::string* DebuggedSourceFile::mutable_host() {
  std::string* _s = _internal_mutable_host();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.host)
  return _s;
}
inline const std::string& DebuggedSourceFile::_internal_host() const {
  return _impl_.host_.Get();
}
inline void DebuggedSourceFile::_internal_set_host(const std::string& value) {
  
  _impl_.host_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedSourceFile::_internal_mutable_host() {
  
  return _impl_.host_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedSourceFile::release_host() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedSourceFile.host)
  return _impl_.host_.Release();
}
inline void DebuggedSourceFile::set_allocated_host(std::string* host) {
  if (host != nullptr) {
    
  } else {
    
  }
  _impl_.host_.SetAllocated(host, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.host_.IsDefault()) {
    _impl_.host_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedSourceFile.host)
}

// string file_path = 2;
inline void DebuggedSourceFile::clear_file_path() {
  _impl_.file_path_.ClearToEmpty();
}
inline const std::string& DebuggedSourceFile::file_path() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.file_path)
  return _internal_file_path();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DebuggedSourceFile::set_file_path(ArgT0&& arg0, ArgT... args) {
 
 _impl_.file_path_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.file_path)
}
inline std::string* DebuggedSourceFile::mutable_file_path() {
  std::string* _s = _internal_mutable_file_path();
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.file_path)
  return _s;
}
inline const std::string& DebuggedSourceFile::_internal_file_path() const {
  return _impl_.file_path_.Get();
}
inline void DebuggedSourceFile::_internal_set_file_path(const std::string& value) {
  
  _impl_.file_path_.Set(value, GetArenaForAllocation());
}
inline std::string* DebuggedSourceFile::_internal_mutable_file_path() {
  
  return _impl_.file_path_.Mutable(GetArenaForAllocation());
}
inline std::string* DebuggedSourceFile::release_file_path() {
  // @@protoc_insertion_point(field_release:tensorflow.DebuggedSourceFile.file_path)
  return _impl_.file_path_.Release();
}
inline void DebuggedSourceFile::set_allocated_file_path(std::string* file_path) {
  if (file_path != nullptr) {
    
  } else {
    
  }
  _impl_.file_path_.SetAllocated(file_path, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.file_path_.IsDefault()) {
    _impl_.file_path_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DebuggedSourceFile.file_path)
}

// int64 last_modified = 3;
inline void DebuggedSourceFile::clear_last_modified() {
  _impl_.last_modified_ = int64_t{0};
}
inline int64_t DebuggedSourceFile::_internal_last_modified() const {
  return _impl_.last_modified_;
}
inline int64_t DebuggedSourceFile::last_modified() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.last_modified)
  return _internal_last_modified();
}
inline void DebuggedSourceFile::_internal_set_last_modified(int64_t value) {
  
  _impl_.last_modified_ = value;
}
inline void DebuggedSourceFile::set_last_modified(int64_t value) {
  _internal_set_last_modified(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.last_modified)
}

// int64 bytes = 4;
inline void DebuggedSourceFile::clear_bytes() {
  _impl_.bytes_ = int64_t{0};
}
inline int64_t DebuggedSourceFile::_internal_bytes() const {
  return _impl_.bytes_;
}
inline int64_t DebuggedSourceFile::bytes() const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.bytes)
  return _internal_bytes();
}
inline void DebuggedSourceFile::_internal_set_bytes(int64_t value) {
  
  _impl_.bytes_ = value;
}
inline void DebuggedSourceFile::set_bytes(int64_t value) {
  _internal_set_bytes(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.bytes)
}

// repeated string lines = 5;
inline int DebuggedSourceFile::_internal_lines_size() const {
  return _impl_.lines_.size();
}
inline int DebuggedSourceFile::lines_size() const {
  return _internal_lines_size();
}
inline void DebuggedSourceFile::clear_lines() {
  _impl_.lines_.Clear();
}
inline std::string* DebuggedSourceFile::add_lines() {
  std::string* _s = _internal_add_lines();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.DebuggedSourceFile.lines)
  return _s;
}
inline const std::string& DebuggedSourceFile::_internal_lines(int index) const {
  return _impl_.lines_.Get(index);
}
inline const std::string& DebuggedSourceFile::lines(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFile.lines)
  return _internal_lines(index);
}
inline std::string* DebuggedSourceFile::mutable_lines(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFile.lines)
  return _impl_.lines_.Mutable(index);
}
inline void DebuggedSourceFile::set_lines(int index, const std::string& value) {
  _impl_.lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::set_lines(int index, std::string&& value) {
  _impl_.lines_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::set_lines(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.lines_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::set_lines(int index, const char* value, size_t size) {
  _impl_.lines_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.DebuggedSourceFile.lines)
}
inline std::string* DebuggedSourceFile::_internal_add_lines() {
  return _impl_.lines_.Add();
}
inline void DebuggedSourceFile::add_lines(const std::string& value) {
  _impl_.lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(std::string&& value) {
  _impl_.lines_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.lines_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.DebuggedSourceFile.lines)
}
inline void DebuggedSourceFile::add_lines(const char* value, size_t size) {
  _impl_.lines_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.DebuggedSourceFile.lines)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DebuggedSourceFile::lines() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedSourceFile.lines)
  return _impl_.lines_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DebuggedSourceFile::mutable_lines() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedSourceFile.lines)
  return &_impl_.lines_;
}

// -------------------------------------------------------------------

// DebuggedSourceFiles

// repeated .tensorflow.DebuggedSourceFile source_files = 1;
inline int DebuggedSourceFiles::_internal_source_files_size() const {
  return _impl_.source_files_.size();
}
inline int DebuggedSourceFiles::source_files_size() const {
  return _internal_source_files_size();
}
inline void DebuggedSourceFiles::clear_source_files() {
  _impl_.source_files_.Clear();
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::mutable_source_files(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.DebuggedSourceFiles.source_files)
  return _impl_.source_files_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >*
DebuggedSourceFiles::mutable_source_files() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.DebuggedSourceFiles.source_files)
  return &_impl_.source_files_;
}
inline const ::tensorflow::DebuggedSourceFile& DebuggedSourceFiles::_internal_source_files(int index) const {
  return _impl_.source_files_.Get(index);
}
inline const ::tensorflow::DebuggedSourceFile& DebuggedSourceFiles::source_files(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.DebuggedSourceFiles.source_files)
  return _internal_source_files(index);
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::_internal_add_source_files() {
  return _impl_.source_files_.Add();
}
inline ::tensorflow::DebuggedSourceFile* DebuggedSourceFiles::add_source_files() {
  ::tensorflow::DebuggedSourceFile* _add = _internal_add_source_files();
  // @@protoc_insertion_point(field_add:tensorflow.DebuggedSourceFiles.source_files)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::DebuggedSourceFile >&
DebuggedSourceFiles::source_files() const {
  // @@protoc_insertion_point(field_list:tensorflow.DebuggedSourceFiles.source_files)
  return _impl_.source_files_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdebug_2eproto
