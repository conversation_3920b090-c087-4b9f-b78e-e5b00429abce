// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tsl/protobuf/bfc_memory_map.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
namespace tensorflow {
class BinSummary;
struct BinSummaryDefaultTypeInternal;
extern BinSummaryDefaultTypeInternal _BinSummary_default_instance_;
class MemAllocatorStats;
struct MemAllocatorStatsDefaultTypeInternal;
extern MemAllocatorStatsDefaultTypeInternal _MemAllocatorStats_default_instance_;
class MemChunk;
struct MemChunkDefaultTypeInternal;
extern MemChunkDefaultTypeInternal _MemChunk_default_instance_;
class MemoryDump;
struct MemoryDumpDefaultTypeInternal;
extern MemoryDumpDefaultTypeInternal _MemoryDump_default_instance_;
class SnapShot;
struct SnapShotDefaultTypeInternal;
extern SnapShotDefaultTypeInternal _SnapShot_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BinSummary* Arena::CreateMaybeMessage<::tensorflow::BinSummary>(Arena*);
template<> ::tensorflow::MemAllocatorStats* Arena::CreateMaybeMessage<::tensorflow::MemAllocatorStats>(Arena*);
template<> ::tensorflow::MemChunk* Arena::CreateMaybeMessage<::tensorflow::MemChunk>(Arena*);
template<> ::tensorflow::MemoryDump* Arena::CreateMaybeMessage<::tensorflow::MemoryDump>(Arena*);
template<> ::tensorflow::SnapShot* Arena::CreateMaybeMessage<::tensorflow::SnapShot>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class MemAllocatorStats final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemAllocatorStats) */ {
 public:
  inline MemAllocatorStats() : MemAllocatorStats(nullptr) {}
  ~MemAllocatorStats() override;
  explicit PROTOBUF_CONSTEXPR MemAllocatorStats(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemAllocatorStats(const MemAllocatorStats& from);
  MemAllocatorStats(MemAllocatorStats&& from) noexcept
    : MemAllocatorStats() {
    *this = ::std::move(from);
  }

  inline MemAllocatorStats& operator=(const MemAllocatorStats& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemAllocatorStats& operator=(MemAllocatorStats&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemAllocatorStats& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemAllocatorStats* internal_default_instance() {
    return reinterpret_cast<const MemAllocatorStats*>(
               &_MemAllocatorStats_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(MemAllocatorStats& a, MemAllocatorStats& b) {
    a.Swap(&b);
  }
  inline void Swap(MemAllocatorStats* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemAllocatorStats* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemAllocatorStats* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemAllocatorStats>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemAllocatorStats& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemAllocatorStats& from) {
    MemAllocatorStats::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemAllocatorStats* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemAllocatorStats";
  }
  protected:
  explicit MemAllocatorStats(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNumAllocsFieldNumber = 1,
    kBytesInUseFieldNumber = 2,
    kPeakBytesInUseFieldNumber = 3,
    kLargestAllocSizeFieldNumber = 4,
    kFragmentationMetricFieldNumber = 5,
  };
  // int64 num_allocs = 1;
  void clear_num_allocs();
  int64_t num_allocs() const;
  void set_num_allocs(int64_t value);
  private:
  int64_t _internal_num_allocs() const;
  void _internal_set_num_allocs(int64_t value);
  public:

  // int64 bytes_in_use = 2;
  void clear_bytes_in_use();
  int64_t bytes_in_use() const;
  void set_bytes_in_use(int64_t value);
  private:
  int64_t _internal_bytes_in_use() const;
  void _internal_set_bytes_in_use(int64_t value);
  public:

  // int64 peak_bytes_in_use = 3;
  void clear_peak_bytes_in_use();
  int64_t peak_bytes_in_use() const;
  void set_peak_bytes_in_use(int64_t value);
  private:
  int64_t _internal_peak_bytes_in_use() const;
  void _internal_set_peak_bytes_in_use(int64_t value);
  public:

  // int64 largest_alloc_size = 4;
  void clear_largest_alloc_size();
  int64_t largest_alloc_size() const;
  void set_largest_alloc_size(int64_t value);
  private:
  int64_t _internal_largest_alloc_size() const;
  void _internal_set_largest_alloc_size(int64_t value);
  public:

  // float fragmentation_metric = 5;
  void clear_fragmentation_metric();
  float fragmentation_metric() const;
  void set_fragmentation_metric(float value);
  private:
  float _internal_fragmentation_metric() const;
  void _internal_set_fragmentation_metric(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemAllocatorStats)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t num_allocs_;
    int64_t bytes_in_use_;
    int64_t peak_bytes_in_use_;
    int64_t largest_alloc_size_;
    float fragmentation_metric_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class MemChunk final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemChunk) */ {
 public:
  inline MemChunk() : MemChunk(nullptr) {}
  ~MemChunk() override;
  explicit PROTOBUF_CONSTEXPR MemChunk(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemChunk(const MemChunk& from);
  MemChunk(MemChunk&& from) noexcept
    : MemChunk() {
    *this = ::std::move(from);
  }

  inline MemChunk& operator=(const MemChunk& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemChunk& operator=(MemChunk&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemChunk& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemChunk* internal_default_instance() {
    return reinterpret_cast<const MemChunk*>(
               &_MemChunk_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MemChunk& a, MemChunk& b) {
    a.Swap(&b);
  }
  inline void Swap(MemChunk* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemChunk* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemChunk* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemChunk>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemChunk& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemChunk& from) {
    MemChunk::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemChunk* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemChunk";
  }
  protected:
  explicit MemChunk(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kOpNameFieldNumber = 5,
    kAddressFieldNumber = 1,
    kSizeFieldNumber = 2,
    kRequestedSizeFieldNumber = 3,
    kFreedAtCountFieldNumber = 6,
    kBinFieldNumber = 4,
    kInUseFieldNumber = 8,
    kActionCountFieldNumber = 7,
    kStepIdFieldNumber = 9,
  };
  // string op_name = 5;
  void clear_op_name();
  const std::string& op_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_op_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_op_name();
  PROTOBUF_NODISCARD std::string* release_op_name();
  void set_allocated_op_name(std::string* op_name);
  private:
  const std::string& _internal_op_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_op_name(const std::string& value);
  std::string* _internal_mutable_op_name();
  public:

  // uint64 address = 1;
  void clear_address();
  uint64_t address() const;
  void set_address(uint64_t value);
  private:
  uint64_t _internal_address() const;
  void _internal_set_address(uint64_t value);
  public:

  // int64 size = 2;
  void clear_size();
  int64_t size() const;
  void set_size(int64_t value);
  private:
  int64_t _internal_size() const;
  void _internal_set_size(int64_t value);
  public:

  // int64 requested_size = 3;
  void clear_requested_size();
  int64_t requested_size() const;
  void set_requested_size(int64_t value);
  private:
  int64_t _internal_requested_size() const;
  void _internal_set_requested_size(int64_t value);
  public:

  // uint64 freed_at_count = 6;
  void clear_freed_at_count();
  uint64_t freed_at_count() const;
  void set_freed_at_count(uint64_t value);
  private:
  uint64_t _internal_freed_at_count() const;
  void _internal_set_freed_at_count(uint64_t value);
  public:

  // int32 bin = 4;
  void clear_bin();
  int32_t bin() const;
  void set_bin(int32_t value);
  private:
  int32_t _internal_bin() const;
  void _internal_set_bin(int32_t value);
  public:

  // bool in_use = 8;
  void clear_in_use();
  bool in_use() const;
  void set_in_use(bool value);
  private:
  bool _internal_in_use() const;
  void _internal_set_in_use(bool value);
  public:

  // uint64 action_count = 7;
  void clear_action_count();
  uint64_t action_count() const;
  void set_action_count(uint64_t value);
  private:
  uint64_t _internal_action_count() const;
  void _internal_set_action_count(uint64_t value);
  public:

  // uint64 step_id = 9;
  void clear_step_id();
  uint64_t step_id() const;
  void set_step_id(uint64_t value);
  private:
  uint64_t _internal_step_id() const;
  void _internal_set_step_id(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.MemChunk)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr op_name_;
    uint64_t address_;
    int64_t size_;
    int64_t requested_size_;
    uint64_t freed_at_count_;
    int32_t bin_;
    bool in_use_;
    uint64_t action_count_;
    uint64_t step_id_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class BinSummary final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BinSummary) */ {
 public:
  inline BinSummary() : BinSummary(nullptr) {}
  ~BinSummary() override;
  explicit PROTOBUF_CONSTEXPR BinSummary(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BinSummary(const BinSummary& from);
  BinSummary(BinSummary&& from) noexcept
    : BinSummary() {
    *this = ::std::move(from);
  }

  inline BinSummary& operator=(const BinSummary& from) {
    CopyFrom(from);
    return *this;
  }
  inline BinSummary& operator=(BinSummary&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BinSummary& default_instance() {
    return *internal_default_instance();
  }
  static inline const BinSummary* internal_default_instance() {
    return reinterpret_cast<const BinSummary*>(
               &_BinSummary_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(BinSummary& a, BinSummary& b) {
    a.Swap(&b);
  }
  inline void Swap(BinSummary* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BinSummary* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BinSummary* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BinSummary>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BinSummary& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BinSummary& from) {
    BinSummary::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BinSummary* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BinSummary";
  }
  protected:
  explicit BinSummary(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTotalBytesInUseFieldNumber = 2,
    kTotalBytesInBinFieldNumber = 3,
    kTotalChunksInUseFieldNumber = 4,
    kTotalChunksInBinFieldNumber = 5,
    kBinFieldNumber = 1,
  };
  // int64 total_bytes_in_use = 2;
  void clear_total_bytes_in_use();
  int64_t total_bytes_in_use() const;
  void set_total_bytes_in_use(int64_t value);
  private:
  int64_t _internal_total_bytes_in_use() const;
  void _internal_set_total_bytes_in_use(int64_t value);
  public:

  // int64 total_bytes_in_bin = 3;
  void clear_total_bytes_in_bin();
  int64_t total_bytes_in_bin() const;
  void set_total_bytes_in_bin(int64_t value);
  private:
  int64_t _internal_total_bytes_in_bin() const;
  void _internal_set_total_bytes_in_bin(int64_t value);
  public:

  // int64 total_chunks_in_use = 4;
  void clear_total_chunks_in_use();
  int64_t total_chunks_in_use() const;
  void set_total_chunks_in_use(int64_t value);
  private:
  int64_t _internal_total_chunks_in_use() const;
  void _internal_set_total_chunks_in_use(int64_t value);
  public:

  // int64 total_chunks_in_bin = 5;
  void clear_total_chunks_in_bin();
  int64_t total_chunks_in_bin() const;
  void set_total_chunks_in_bin(int64_t value);
  private:
  int64_t _internal_total_chunks_in_bin() const;
  void _internal_set_total_chunks_in_bin(int64_t value);
  public:

  // int32 bin = 1;
  void clear_bin();
  int32_t bin() const;
  void set_bin(int32_t value);
  private:
  int32_t _internal_bin() const;
  void _internal_set_bin(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BinSummary)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int64_t total_bytes_in_use_;
    int64_t total_bytes_in_bin_;
    int64_t total_chunks_in_use_;
    int64_t total_chunks_in_bin_;
    int32_t bin_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class SnapShot final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.SnapShot) */ {
 public:
  inline SnapShot() : SnapShot(nullptr) {}
  ~SnapShot() override;
  explicit PROTOBUF_CONSTEXPR SnapShot(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SnapShot(const SnapShot& from);
  SnapShot(SnapShot&& from) noexcept
    : SnapShot() {
    *this = ::std::move(from);
  }

  inline SnapShot& operator=(const SnapShot& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapShot& operator=(SnapShot&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SnapShot& default_instance() {
    return *internal_default_instance();
  }
  static inline const SnapShot* internal_default_instance() {
    return reinterpret_cast<const SnapShot*>(
               &_SnapShot_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SnapShot& a, SnapShot& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapShot* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SnapShot* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SnapShot* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SnapShot>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SnapShot& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SnapShot& from) {
    SnapShot::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapShot* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.SnapShot";
  }
  protected:
  explicit SnapShot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kActionCountFieldNumber = 1,
    kSizeFieldNumber = 2,
  };
  // uint64 action_count = 1;
  void clear_action_count();
  uint64_t action_count() const;
  void set_action_count(uint64_t value);
  private:
  uint64_t _internal_action_count() const;
  void _internal_set_action_count(uint64_t value);
  public:

  // int64 size = 2;
  void clear_size();
  int64_t size() const;
  void set_size(int64_t value);
  private:
  int64_t _internal_size() const;
  void _internal_set_size(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.SnapShot)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint64_t action_count_;
    int64_t size_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// -------------------------------------------------------------------

class MemoryDump final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.MemoryDump) */ {
 public:
  inline MemoryDump() : MemoryDump(nullptr) {}
  ~MemoryDump() override;
  explicit PROTOBUF_CONSTEXPR MemoryDump(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MemoryDump(const MemoryDump& from);
  MemoryDump(MemoryDump&& from) noexcept
    : MemoryDump() {
    *this = ::std::move(from);
  }

  inline MemoryDump& operator=(const MemoryDump& from) {
    CopyFrom(from);
    return *this;
  }
  inline MemoryDump& operator=(MemoryDump&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MemoryDump& default_instance() {
    return *internal_default_instance();
  }
  static inline const MemoryDump* internal_default_instance() {
    return reinterpret_cast<const MemoryDump*>(
               &_MemoryDump_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MemoryDump& a, MemoryDump& b) {
    a.Swap(&b);
  }
  inline void Swap(MemoryDump* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MemoryDump* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MemoryDump* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MemoryDump>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MemoryDump& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MemoryDump& from) {
    MemoryDump::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MemoryDump* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.MemoryDump";
  }
  protected:
  explicit MemoryDump(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBinSummaryFieldNumber = 2,
    kChunkFieldNumber = 3,
    kSnapShotFieldNumber = 4,
    kAllocatorNameFieldNumber = 1,
    kStatsFieldNumber = 5,
  };
  // repeated .tensorflow.BinSummary bin_summary = 2;
  int bin_summary_size() const;
  private:
  int _internal_bin_summary_size() const;
  public:
  void clear_bin_summary();
  ::tensorflow::BinSummary* mutable_bin_summary(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >*
      mutable_bin_summary();
  private:
  const ::tensorflow::BinSummary& _internal_bin_summary(int index) const;
  ::tensorflow::BinSummary* _internal_add_bin_summary();
  public:
  const ::tensorflow::BinSummary& bin_summary(int index) const;
  ::tensorflow::BinSummary* add_bin_summary();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >&
      bin_summary() const;

  // repeated .tensorflow.MemChunk chunk = 3;
  int chunk_size() const;
  private:
  int _internal_chunk_size() const;
  public:
  void clear_chunk();
  ::tensorflow::MemChunk* mutable_chunk(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >*
      mutable_chunk();
  private:
  const ::tensorflow::MemChunk& _internal_chunk(int index) const;
  ::tensorflow::MemChunk* _internal_add_chunk();
  public:
  const ::tensorflow::MemChunk& chunk(int index) const;
  ::tensorflow::MemChunk* add_chunk();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >&
      chunk() const;

  // repeated .tensorflow.SnapShot snap_shot = 4;
  int snap_shot_size() const;
  private:
  int _internal_snap_shot_size() const;
  public:
  void clear_snap_shot();
  ::tensorflow::SnapShot* mutable_snap_shot(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >*
      mutable_snap_shot();
  private:
  const ::tensorflow::SnapShot& _internal_snap_shot(int index) const;
  ::tensorflow::SnapShot* _internal_add_snap_shot();
  public:
  const ::tensorflow::SnapShot& snap_shot(int index) const;
  ::tensorflow::SnapShot* add_snap_shot();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >&
      snap_shot() const;

  // string allocator_name = 1;
  void clear_allocator_name();
  const std::string& allocator_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_allocator_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_allocator_name();
  PROTOBUF_NODISCARD std::string* release_allocator_name();
  void set_allocated_allocator_name(std::string* allocator_name);
  private:
  const std::string& _internal_allocator_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_allocator_name(const std::string& value);
  std::string* _internal_mutable_allocator_name();
  public:

  // .tensorflow.MemAllocatorStats stats = 5;
  bool has_stats() const;
  private:
  bool _internal_has_stats() const;
  public:
  void clear_stats();
  const ::tensorflow::MemAllocatorStats& stats() const;
  PROTOBUF_NODISCARD ::tensorflow::MemAllocatorStats* release_stats();
  ::tensorflow::MemAllocatorStats* mutable_stats();
  void set_allocated_stats(::tensorflow::MemAllocatorStats* stats);
  private:
  const ::tensorflow::MemAllocatorStats& _internal_stats() const;
  ::tensorflow::MemAllocatorStats* _internal_mutable_stats();
  public:
  void unsafe_arena_set_allocated_stats(
      ::tensorflow::MemAllocatorStats* stats);
  ::tensorflow::MemAllocatorStats* unsafe_arena_release_stats();

  // @@protoc_insertion_point(class_scope:tensorflow.MemoryDump)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary > bin_summary_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk > chunk_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot > snap_shot_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr allocator_name_;
    ::tensorflow::MemAllocatorStats* stats_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// MemAllocatorStats

// int64 num_allocs = 1;
inline void MemAllocatorStats::clear_num_allocs() {
  _impl_.num_allocs_ = int64_t{0};
}
inline int64_t MemAllocatorStats::_internal_num_allocs() const {
  return _impl_.num_allocs_;
}
inline int64_t MemAllocatorStats::num_allocs() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.num_allocs)
  return _internal_num_allocs();
}
inline void MemAllocatorStats::_internal_set_num_allocs(int64_t value) {
  
  _impl_.num_allocs_ = value;
}
inline void MemAllocatorStats::set_num_allocs(int64_t value) {
  _internal_set_num_allocs(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.num_allocs)
}

// int64 bytes_in_use = 2;
inline void MemAllocatorStats::clear_bytes_in_use() {
  _impl_.bytes_in_use_ = int64_t{0};
}
inline int64_t MemAllocatorStats::_internal_bytes_in_use() const {
  return _impl_.bytes_in_use_;
}
inline int64_t MemAllocatorStats::bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.bytes_in_use)
  return _internal_bytes_in_use();
}
inline void MemAllocatorStats::_internal_set_bytes_in_use(int64_t value) {
  
  _impl_.bytes_in_use_ = value;
}
inline void MemAllocatorStats::set_bytes_in_use(int64_t value) {
  _internal_set_bytes_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.bytes_in_use)
}

// int64 peak_bytes_in_use = 3;
inline void MemAllocatorStats::clear_peak_bytes_in_use() {
  _impl_.peak_bytes_in_use_ = int64_t{0};
}
inline int64_t MemAllocatorStats::_internal_peak_bytes_in_use() const {
  return _impl_.peak_bytes_in_use_;
}
inline int64_t MemAllocatorStats::peak_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.peak_bytes_in_use)
  return _internal_peak_bytes_in_use();
}
inline void MemAllocatorStats::_internal_set_peak_bytes_in_use(int64_t value) {
  
  _impl_.peak_bytes_in_use_ = value;
}
inline void MemAllocatorStats::set_peak_bytes_in_use(int64_t value) {
  _internal_set_peak_bytes_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.peak_bytes_in_use)
}

// int64 largest_alloc_size = 4;
inline void MemAllocatorStats::clear_largest_alloc_size() {
  _impl_.largest_alloc_size_ = int64_t{0};
}
inline int64_t MemAllocatorStats::_internal_largest_alloc_size() const {
  return _impl_.largest_alloc_size_;
}
inline int64_t MemAllocatorStats::largest_alloc_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.largest_alloc_size)
  return _internal_largest_alloc_size();
}
inline void MemAllocatorStats::_internal_set_largest_alloc_size(int64_t value) {
  
  _impl_.largest_alloc_size_ = value;
}
inline void MemAllocatorStats::set_largest_alloc_size(int64_t value) {
  _internal_set_largest_alloc_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.largest_alloc_size)
}

// float fragmentation_metric = 5;
inline void MemAllocatorStats::clear_fragmentation_metric() {
  _impl_.fragmentation_metric_ = 0;
}
inline float MemAllocatorStats::_internal_fragmentation_metric() const {
  return _impl_.fragmentation_metric_;
}
inline float MemAllocatorStats::fragmentation_metric() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemAllocatorStats.fragmentation_metric)
  return _internal_fragmentation_metric();
}
inline void MemAllocatorStats::_internal_set_fragmentation_metric(float value) {
  
  _impl_.fragmentation_metric_ = value;
}
inline void MemAllocatorStats::set_fragmentation_metric(float value) {
  _internal_set_fragmentation_metric(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemAllocatorStats.fragmentation_metric)
}

// -------------------------------------------------------------------

// MemChunk

// uint64 address = 1;
inline void MemChunk::clear_address() {
  _impl_.address_ = uint64_t{0u};
}
inline uint64_t MemChunk::_internal_address() const {
  return _impl_.address_;
}
inline uint64_t MemChunk::address() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.address)
  return _internal_address();
}
inline void MemChunk::_internal_set_address(uint64_t value) {
  
  _impl_.address_ = value;
}
inline void MemChunk::set_address(uint64_t value) {
  _internal_set_address(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.address)
}

// int64 size = 2;
inline void MemChunk::clear_size() {
  _impl_.size_ = int64_t{0};
}
inline int64_t MemChunk::_internal_size() const {
  return _impl_.size_;
}
inline int64_t MemChunk::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.size)
  return _internal_size();
}
inline void MemChunk::_internal_set_size(int64_t value) {
  
  _impl_.size_ = value;
}
inline void MemChunk::set_size(int64_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.size)
}

// int64 requested_size = 3;
inline void MemChunk::clear_requested_size() {
  _impl_.requested_size_ = int64_t{0};
}
inline int64_t MemChunk::_internal_requested_size() const {
  return _impl_.requested_size_;
}
inline int64_t MemChunk::requested_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.requested_size)
  return _internal_requested_size();
}
inline void MemChunk::_internal_set_requested_size(int64_t value) {
  
  _impl_.requested_size_ = value;
}
inline void MemChunk::set_requested_size(int64_t value) {
  _internal_set_requested_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.requested_size)
}

// int32 bin = 4;
inline void MemChunk::clear_bin() {
  _impl_.bin_ = 0;
}
inline int32_t MemChunk::_internal_bin() const {
  return _impl_.bin_;
}
inline int32_t MemChunk::bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.bin)
  return _internal_bin();
}
inline void MemChunk::_internal_set_bin(int32_t value) {
  
  _impl_.bin_ = value;
}
inline void MemChunk::set_bin(int32_t value) {
  _internal_set_bin(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.bin)
}

// string op_name = 5;
inline void MemChunk::clear_op_name() {
  _impl_.op_name_.ClearToEmpty();
}
inline const std::string& MemChunk::op_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.op_name)
  return _internal_op_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemChunk::set_op_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.op_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.op_name)
}
inline std::string* MemChunk::mutable_op_name() {
  std::string* _s = _internal_mutable_op_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemChunk.op_name)
  return _s;
}
inline const std::string& MemChunk::_internal_op_name() const {
  return _impl_.op_name_.Get();
}
inline void MemChunk::_internal_set_op_name(const std::string& value) {
  
  _impl_.op_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemChunk::_internal_mutable_op_name() {
  
  return _impl_.op_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemChunk::release_op_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemChunk.op_name)
  return _impl_.op_name_.Release();
}
inline void MemChunk::set_allocated_op_name(std::string* op_name) {
  if (op_name != nullptr) {
    
  } else {
    
  }
  _impl_.op_name_.SetAllocated(op_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.op_name_.IsDefault()) {
    _impl_.op_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemChunk.op_name)
}

// uint64 freed_at_count = 6;
inline void MemChunk::clear_freed_at_count() {
  _impl_.freed_at_count_ = uint64_t{0u};
}
inline uint64_t MemChunk::_internal_freed_at_count() const {
  return _impl_.freed_at_count_;
}
inline uint64_t MemChunk::freed_at_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.freed_at_count)
  return _internal_freed_at_count();
}
inline void MemChunk::_internal_set_freed_at_count(uint64_t value) {
  
  _impl_.freed_at_count_ = value;
}
inline void MemChunk::set_freed_at_count(uint64_t value) {
  _internal_set_freed_at_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.freed_at_count)
}

// uint64 action_count = 7;
inline void MemChunk::clear_action_count() {
  _impl_.action_count_ = uint64_t{0u};
}
inline uint64_t MemChunk::_internal_action_count() const {
  return _impl_.action_count_;
}
inline uint64_t MemChunk::action_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.action_count)
  return _internal_action_count();
}
inline void MemChunk::_internal_set_action_count(uint64_t value) {
  
  _impl_.action_count_ = value;
}
inline void MemChunk::set_action_count(uint64_t value) {
  _internal_set_action_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.action_count)
}

// bool in_use = 8;
inline void MemChunk::clear_in_use() {
  _impl_.in_use_ = false;
}
inline bool MemChunk::_internal_in_use() const {
  return _impl_.in_use_;
}
inline bool MemChunk::in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.in_use)
  return _internal_in_use();
}
inline void MemChunk::_internal_set_in_use(bool value) {
  
  _impl_.in_use_ = value;
}
inline void MemChunk::set_in_use(bool value) {
  _internal_set_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.in_use)
}

// uint64 step_id = 9;
inline void MemChunk::clear_step_id() {
  _impl_.step_id_ = uint64_t{0u};
}
inline uint64_t MemChunk::_internal_step_id() const {
  return _impl_.step_id_;
}
inline uint64_t MemChunk::step_id() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemChunk.step_id)
  return _internal_step_id();
}
inline void MemChunk::_internal_set_step_id(uint64_t value) {
  
  _impl_.step_id_ = value;
}
inline void MemChunk::set_step_id(uint64_t value) {
  _internal_set_step_id(value);
  // @@protoc_insertion_point(field_set:tensorflow.MemChunk.step_id)
}

// -------------------------------------------------------------------

// BinSummary

// int32 bin = 1;
inline void BinSummary::clear_bin() {
  _impl_.bin_ = 0;
}
inline int32_t BinSummary::_internal_bin() const {
  return _impl_.bin_;
}
inline int32_t BinSummary::bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.bin)
  return _internal_bin();
}
inline void BinSummary::_internal_set_bin(int32_t value) {
  
  _impl_.bin_ = value;
}
inline void BinSummary::set_bin(int32_t value) {
  _internal_set_bin(value);
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.bin)
}

// int64 total_bytes_in_use = 2;
inline void BinSummary::clear_total_bytes_in_use() {
  _impl_.total_bytes_in_use_ = int64_t{0};
}
inline int64_t BinSummary::_internal_total_bytes_in_use() const {
  return _impl_.total_bytes_in_use_;
}
inline int64_t BinSummary::total_bytes_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_bytes_in_use)
  return _internal_total_bytes_in_use();
}
inline void BinSummary::_internal_set_total_bytes_in_use(int64_t value) {
  
  _impl_.total_bytes_in_use_ = value;
}
inline void BinSummary::set_total_bytes_in_use(int64_t value) {
  _internal_set_total_bytes_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_bytes_in_use)
}

// int64 total_bytes_in_bin = 3;
inline void BinSummary::clear_total_bytes_in_bin() {
  _impl_.total_bytes_in_bin_ = int64_t{0};
}
inline int64_t BinSummary::_internal_total_bytes_in_bin() const {
  return _impl_.total_bytes_in_bin_;
}
inline int64_t BinSummary::total_bytes_in_bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_bytes_in_bin)
  return _internal_total_bytes_in_bin();
}
inline void BinSummary::_internal_set_total_bytes_in_bin(int64_t value) {
  
  _impl_.total_bytes_in_bin_ = value;
}
inline void BinSummary::set_total_bytes_in_bin(int64_t value) {
  _internal_set_total_bytes_in_bin(value);
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_bytes_in_bin)
}

// int64 total_chunks_in_use = 4;
inline void BinSummary::clear_total_chunks_in_use() {
  _impl_.total_chunks_in_use_ = int64_t{0};
}
inline int64_t BinSummary::_internal_total_chunks_in_use() const {
  return _impl_.total_chunks_in_use_;
}
inline int64_t BinSummary::total_chunks_in_use() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_chunks_in_use)
  return _internal_total_chunks_in_use();
}
inline void BinSummary::_internal_set_total_chunks_in_use(int64_t value) {
  
  _impl_.total_chunks_in_use_ = value;
}
inline void BinSummary::set_total_chunks_in_use(int64_t value) {
  _internal_set_total_chunks_in_use(value);
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_chunks_in_use)
}

// int64 total_chunks_in_bin = 5;
inline void BinSummary::clear_total_chunks_in_bin() {
  _impl_.total_chunks_in_bin_ = int64_t{0};
}
inline int64_t BinSummary::_internal_total_chunks_in_bin() const {
  return _impl_.total_chunks_in_bin_;
}
inline int64_t BinSummary::total_chunks_in_bin() const {
  // @@protoc_insertion_point(field_get:tensorflow.BinSummary.total_chunks_in_bin)
  return _internal_total_chunks_in_bin();
}
inline void BinSummary::_internal_set_total_chunks_in_bin(int64_t value) {
  
  _impl_.total_chunks_in_bin_ = value;
}
inline void BinSummary::set_total_chunks_in_bin(int64_t value) {
  _internal_set_total_chunks_in_bin(value);
  // @@protoc_insertion_point(field_set:tensorflow.BinSummary.total_chunks_in_bin)
}

// -------------------------------------------------------------------

// SnapShot

// uint64 action_count = 1;
inline void SnapShot::clear_action_count() {
  _impl_.action_count_ = uint64_t{0u};
}
inline uint64_t SnapShot::_internal_action_count() const {
  return _impl_.action_count_;
}
inline uint64_t SnapShot::action_count() const {
  // @@protoc_insertion_point(field_get:tensorflow.SnapShot.action_count)
  return _internal_action_count();
}
inline void SnapShot::_internal_set_action_count(uint64_t value) {
  
  _impl_.action_count_ = value;
}
inline void SnapShot::set_action_count(uint64_t value) {
  _internal_set_action_count(value);
  // @@protoc_insertion_point(field_set:tensorflow.SnapShot.action_count)
}

// int64 size = 2;
inline void SnapShot::clear_size() {
  _impl_.size_ = int64_t{0};
}
inline int64_t SnapShot::_internal_size() const {
  return _impl_.size_;
}
inline int64_t SnapShot::size() const {
  // @@protoc_insertion_point(field_get:tensorflow.SnapShot.size)
  return _internal_size();
}
inline void SnapShot::_internal_set_size(int64_t value) {
  
  _impl_.size_ = value;
}
inline void SnapShot::set_size(int64_t value) {
  _internal_set_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.SnapShot.size)
}

// -------------------------------------------------------------------

// MemoryDump

// string allocator_name = 1;
inline void MemoryDump::clear_allocator_name() {
  _impl_.allocator_name_.ClearToEmpty();
}
inline const std::string& MemoryDump::allocator_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.allocator_name)
  return _internal_allocator_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MemoryDump::set_allocator_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.allocator_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.MemoryDump.allocator_name)
}
inline std::string* MemoryDump::mutable_allocator_name() {
  std::string* _s = _internal_mutable_allocator_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.allocator_name)
  return _s;
}
inline const std::string& MemoryDump::_internal_allocator_name() const {
  return _impl_.allocator_name_.Get();
}
inline void MemoryDump::_internal_set_allocator_name(const std::string& value) {
  
  _impl_.allocator_name_.Set(value, GetArenaForAllocation());
}
inline std::string* MemoryDump::_internal_mutable_allocator_name() {
  
  return _impl_.allocator_name_.Mutable(GetArenaForAllocation());
}
inline std::string* MemoryDump::release_allocator_name() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryDump.allocator_name)
  return _impl_.allocator_name_.Release();
}
inline void MemoryDump::set_allocated_allocator_name(std::string* allocator_name) {
  if (allocator_name != nullptr) {
    
  } else {
    
  }
  _impl_.allocator_name_.SetAllocated(allocator_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.allocator_name_.IsDefault()) {
    _impl_.allocator_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryDump.allocator_name)
}

// repeated .tensorflow.BinSummary bin_summary = 2;
inline int MemoryDump::_internal_bin_summary_size() const {
  return _impl_.bin_summary_.size();
}
inline int MemoryDump::bin_summary_size() const {
  return _internal_bin_summary_size();
}
inline void MemoryDump::clear_bin_summary() {
  _impl_.bin_summary_.Clear();
}
inline ::tensorflow::BinSummary* MemoryDump::mutable_bin_summary(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.bin_summary)
  return _impl_.bin_summary_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >*
MemoryDump::mutable_bin_summary() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryDump.bin_summary)
  return &_impl_.bin_summary_;
}
inline const ::tensorflow::BinSummary& MemoryDump::_internal_bin_summary(int index) const {
  return _impl_.bin_summary_.Get(index);
}
inline const ::tensorflow::BinSummary& MemoryDump::bin_summary(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.bin_summary)
  return _internal_bin_summary(index);
}
inline ::tensorflow::BinSummary* MemoryDump::_internal_add_bin_summary() {
  return _impl_.bin_summary_.Add();
}
inline ::tensorflow::BinSummary* MemoryDump::add_bin_summary() {
  ::tensorflow::BinSummary* _add = _internal_add_bin_summary();
  // @@protoc_insertion_point(field_add:tensorflow.MemoryDump.bin_summary)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::BinSummary >&
MemoryDump::bin_summary() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryDump.bin_summary)
  return _impl_.bin_summary_;
}

// repeated .tensorflow.MemChunk chunk = 3;
inline int MemoryDump::_internal_chunk_size() const {
  return _impl_.chunk_.size();
}
inline int MemoryDump::chunk_size() const {
  return _internal_chunk_size();
}
inline void MemoryDump::clear_chunk() {
  _impl_.chunk_.Clear();
}
inline ::tensorflow::MemChunk* MemoryDump::mutable_chunk(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.chunk)
  return _impl_.chunk_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >*
MemoryDump::mutable_chunk() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryDump.chunk)
  return &_impl_.chunk_;
}
inline const ::tensorflow::MemChunk& MemoryDump::_internal_chunk(int index) const {
  return _impl_.chunk_.Get(index);
}
inline const ::tensorflow::MemChunk& MemoryDump::chunk(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.chunk)
  return _internal_chunk(index);
}
inline ::tensorflow::MemChunk* MemoryDump::_internal_add_chunk() {
  return _impl_.chunk_.Add();
}
inline ::tensorflow::MemChunk* MemoryDump::add_chunk() {
  ::tensorflow::MemChunk* _add = _internal_add_chunk();
  // @@protoc_insertion_point(field_add:tensorflow.MemoryDump.chunk)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::MemChunk >&
MemoryDump::chunk() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryDump.chunk)
  return _impl_.chunk_;
}

// repeated .tensorflow.SnapShot snap_shot = 4;
inline int MemoryDump::_internal_snap_shot_size() const {
  return _impl_.snap_shot_.size();
}
inline int MemoryDump::snap_shot_size() const {
  return _internal_snap_shot_size();
}
inline void MemoryDump::clear_snap_shot() {
  _impl_.snap_shot_.Clear();
}
inline ::tensorflow::SnapShot* MemoryDump::mutable_snap_shot(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.snap_shot)
  return _impl_.snap_shot_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >*
MemoryDump::mutable_snap_shot() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.MemoryDump.snap_shot)
  return &_impl_.snap_shot_;
}
inline const ::tensorflow::SnapShot& MemoryDump::_internal_snap_shot(int index) const {
  return _impl_.snap_shot_.Get(index);
}
inline const ::tensorflow::SnapShot& MemoryDump::snap_shot(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.snap_shot)
  return _internal_snap_shot(index);
}
inline ::tensorflow::SnapShot* MemoryDump::_internal_add_snap_shot() {
  return _impl_.snap_shot_.Add();
}
inline ::tensorflow::SnapShot* MemoryDump::add_snap_shot() {
  ::tensorflow::SnapShot* _add = _internal_add_snap_shot();
  // @@protoc_insertion_point(field_add:tensorflow.MemoryDump.snap_shot)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::SnapShot >&
MemoryDump::snap_shot() const {
  // @@protoc_insertion_point(field_list:tensorflow.MemoryDump.snap_shot)
  return _impl_.snap_shot_;
}

// .tensorflow.MemAllocatorStats stats = 5;
inline bool MemoryDump::_internal_has_stats() const {
  return this != internal_default_instance() && _impl_.stats_ != nullptr;
}
inline bool MemoryDump::has_stats() const {
  return _internal_has_stats();
}
inline void MemoryDump::clear_stats() {
  if (GetArenaForAllocation() == nullptr && _impl_.stats_ != nullptr) {
    delete _impl_.stats_;
  }
  _impl_.stats_ = nullptr;
}
inline const ::tensorflow::MemAllocatorStats& MemoryDump::_internal_stats() const {
  const ::tensorflow::MemAllocatorStats* p = _impl_.stats_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::MemAllocatorStats&>(
      ::tensorflow::_MemAllocatorStats_default_instance_);
}
inline const ::tensorflow::MemAllocatorStats& MemoryDump::stats() const {
  // @@protoc_insertion_point(field_get:tensorflow.MemoryDump.stats)
  return _internal_stats();
}
inline void MemoryDump::unsafe_arena_set_allocated_stats(
    ::tensorflow::MemAllocatorStats* stats) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.stats_);
  }
  _impl_.stats_ = stats;
  if (stats) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.MemoryDump.stats)
}
inline ::tensorflow::MemAllocatorStats* MemoryDump::release_stats() {
  
  ::tensorflow::MemAllocatorStats* temp = _impl_.stats_;
  _impl_.stats_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::MemAllocatorStats* MemoryDump::unsafe_arena_release_stats() {
  // @@protoc_insertion_point(field_release:tensorflow.MemoryDump.stats)
  
  ::tensorflow::MemAllocatorStats* temp = _impl_.stats_;
  _impl_.stats_ = nullptr;
  return temp;
}
inline ::tensorflow::MemAllocatorStats* MemoryDump::_internal_mutable_stats() {
  
  if (_impl_.stats_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::MemAllocatorStats>(GetArenaForAllocation());
    _impl_.stats_ = p;
  }
  return _impl_.stats_;
}
inline ::tensorflow::MemAllocatorStats* MemoryDump::mutable_stats() {
  ::tensorflow::MemAllocatorStats* _msg = _internal_mutable_stats();
  // @@protoc_insertion_point(field_mutable:tensorflow.MemoryDump.stats)
  return _msg;
}
inline void MemoryDump::set_allocated_stats(::tensorflow::MemAllocatorStats* stats) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.stats_;
  }
  if (stats) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(stats);
    if (message_arena != submessage_arena) {
      stats = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stats, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.stats_ = stats;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.MemoryDump.stats)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tsl_2fprotobuf_2fbfc_5fmemory_5fmap_2eproto
