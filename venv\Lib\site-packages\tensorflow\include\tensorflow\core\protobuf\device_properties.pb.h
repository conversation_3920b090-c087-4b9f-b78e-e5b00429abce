// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/device_properties.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
namespace tensorflow {
class DeviceProperties;
struct DevicePropertiesDefaultTypeInternal;
extern DevicePropertiesDefaultTypeInternal _DeviceProperties_default_instance_;
class DeviceProperties_EnvironmentEntry_DoNotUse;
struct DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal;
extern DeviceProperties_EnvironmentEntry_DoNotUseDefaultTypeInternal _DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_;
class NamedDevice;
struct NamedDeviceDefaultTypeInternal;
extern NamedDeviceDefaultTypeInternal _NamedDevice_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::DeviceProperties* Arena::CreateMaybeMessage<::tensorflow::DeviceProperties>(Arena*);
template<> ::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DeviceProperties_EnvironmentEntry_DoNotUse>(Arena*);
template<> ::tensorflow::NamedDevice* Arena::CreateMaybeMessage<::tensorflow::NamedDevice>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class DeviceProperties_EnvironmentEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceProperties_EnvironmentEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DeviceProperties_EnvironmentEntry_DoNotUse, 
    std::string, std::string,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> SuperType;
  DeviceProperties_EnvironmentEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR DeviceProperties_EnvironmentEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DeviceProperties_EnvironmentEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DeviceProperties_EnvironmentEntry_DoNotUse& other);
  static const DeviceProperties_EnvironmentEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DeviceProperties_EnvironmentEntry_DoNotUse*>(&_DeviceProperties_EnvironmentEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DeviceProperties.EnvironmentEntry.key");
 }
  static bool ValidateValue(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DeviceProperties.EnvironmentEntry.value");
 }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
};

// -------------------------------------------------------------------

class DeviceProperties final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DeviceProperties) */ {
 public:
  inline DeviceProperties() : DeviceProperties(nullptr) {}
  ~DeviceProperties() override;
  explicit PROTOBUF_CONSTEXPR DeviceProperties(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeviceProperties(const DeviceProperties& from);
  DeviceProperties(DeviceProperties&& from) noexcept
    : DeviceProperties() {
    *this = ::std::move(from);
  }

  inline DeviceProperties& operator=(const DeviceProperties& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeviceProperties& operator=(DeviceProperties&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeviceProperties& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeviceProperties* internal_default_instance() {
    return reinterpret_cast<const DeviceProperties*>(
               &_DeviceProperties_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DeviceProperties& a, DeviceProperties& b) {
    a.Swap(&b);
  }
  inline void Swap(DeviceProperties* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeviceProperties* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeviceProperties* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeviceProperties>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeviceProperties& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DeviceProperties& from) {
    DeviceProperties::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeviceProperties* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DeviceProperties";
  }
  protected:
  explicit DeviceProperties(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kEnvironmentFieldNumber = 6,
    kTypeFieldNumber = 1,
    kVendorFieldNumber = 2,
    kModelFieldNumber = 3,
    kFrequencyFieldNumber = 4,
    kNumCoresFieldNumber = 5,
    kNumRegistersFieldNumber = 7,
    kL1CacheSizeFieldNumber = 8,
    kL2CacheSizeFieldNumber = 9,
    kL3CacheSizeFieldNumber = 10,
    kSharedMemorySizePerMultiprocessorFieldNumber = 11,
    kMemorySizeFieldNumber = 12,
    kBandwidthFieldNumber = 13,
  };
  // map<string, string> environment = 6;
  int environment_size() const;
  private:
  int _internal_environment_size() const;
  public:
  void clear_environment();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      _internal_environment() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      _internal_mutable_environment();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
      environment() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
      mutable_environment();

  // string type = 1;
  void clear_type();
  const std::string& type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type();
  PROTOBUF_NODISCARD std::string* release_type();
  void set_allocated_type(std::string* type);
  private:
  const std::string& _internal_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type(const std::string& value);
  std::string* _internal_mutable_type();
  public:

  // string vendor = 2;
  void clear_vendor();
  const std::string& vendor() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_vendor(ArgT0&& arg0, ArgT... args);
  std::string* mutable_vendor();
  PROTOBUF_NODISCARD std::string* release_vendor();
  void set_allocated_vendor(std::string* vendor);
  private:
  const std::string& _internal_vendor() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_vendor(const std::string& value);
  std::string* _internal_mutable_vendor();
  public:

  // string model = 3;
  void clear_model();
  const std::string& model() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model();
  PROTOBUF_NODISCARD std::string* release_model();
  void set_allocated_model(std::string* model);
  private:
  const std::string& _internal_model() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model(const std::string& value);
  std::string* _internal_mutable_model();
  public:

  // int64 frequency = 4;
  void clear_frequency();
  int64_t frequency() const;
  void set_frequency(int64_t value);
  private:
  int64_t _internal_frequency() const;
  void _internal_set_frequency(int64_t value);
  public:

  // int64 num_cores = 5;
  void clear_num_cores();
  int64_t num_cores() const;
  void set_num_cores(int64_t value);
  private:
  int64_t _internal_num_cores() const;
  void _internal_set_num_cores(int64_t value);
  public:

  // int64 num_registers = 7;
  void clear_num_registers();
  int64_t num_registers() const;
  void set_num_registers(int64_t value);
  private:
  int64_t _internal_num_registers() const;
  void _internal_set_num_registers(int64_t value);
  public:

  // int64 l1_cache_size = 8;
  void clear_l1_cache_size();
  int64_t l1_cache_size() const;
  void set_l1_cache_size(int64_t value);
  private:
  int64_t _internal_l1_cache_size() const;
  void _internal_set_l1_cache_size(int64_t value);
  public:

  // int64 l2_cache_size = 9;
  void clear_l2_cache_size();
  int64_t l2_cache_size() const;
  void set_l2_cache_size(int64_t value);
  private:
  int64_t _internal_l2_cache_size() const;
  void _internal_set_l2_cache_size(int64_t value);
  public:

  // int64 l3_cache_size = 10;
  void clear_l3_cache_size();
  int64_t l3_cache_size() const;
  void set_l3_cache_size(int64_t value);
  private:
  int64_t _internal_l3_cache_size() const;
  void _internal_set_l3_cache_size(int64_t value);
  public:

  // int64 shared_memory_size_per_multiprocessor = 11;
  void clear_shared_memory_size_per_multiprocessor();
  int64_t shared_memory_size_per_multiprocessor() const;
  void set_shared_memory_size_per_multiprocessor(int64_t value);
  private:
  int64_t _internal_shared_memory_size_per_multiprocessor() const;
  void _internal_set_shared_memory_size_per_multiprocessor(int64_t value);
  public:

  // int64 memory_size = 12;
  void clear_memory_size();
  int64_t memory_size() const;
  void set_memory_size(int64_t value);
  private:
  int64_t _internal_memory_size() const;
  void _internal_set_memory_size(int64_t value);
  public:

  // int64 bandwidth = 13;
  void clear_bandwidth();
  int64_t bandwidth() const;
  void set_bandwidth(int64_t value);
  private:
  int64_t _internal_bandwidth() const;
  void _internal_set_bandwidth(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.DeviceProperties)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        DeviceProperties_EnvironmentEntry_DoNotUse,
        std::string, std::string,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING> environment_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr vendor_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_;
    int64_t frequency_;
    int64_t num_cores_;
    int64_t num_registers_;
    int64_t l1_cache_size_;
    int64_t l2_cache_size_;
    int64_t l3_cache_size_;
    int64_t shared_memory_size_per_multiprocessor_;
    int64_t memory_size_;
    int64_t bandwidth_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
};
// -------------------------------------------------------------------

class NamedDevice final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NamedDevice) */ {
 public:
  inline NamedDevice() : NamedDevice(nullptr) {}
  ~NamedDevice() override;
  explicit PROTOBUF_CONSTEXPR NamedDevice(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NamedDevice(const NamedDevice& from);
  NamedDevice(NamedDevice&& from) noexcept
    : NamedDevice() {
    *this = ::std::move(from);
  }

  inline NamedDevice& operator=(const NamedDevice& from) {
    CopyFrom(from);
    return *this;
  }
  inline NamedDevice& operator=(NamedDevice&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NamedDevice& default_instance() {
    return *internal_default_instance();
  }
  static inline const NamedDevice* internal_default_instance() {
    return reinterpret_cast<const NamedDevice*>(
               &_NamedDevice_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(NamedDevice& a, NamedDevice& b) {
    a.Swap(&b);
  }
  inline void Swap(NamedDevice* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NamedDevice* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NamedDevice* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NamedDevice>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NamedDevice& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NamedDevice& from) {
    NamedDevice::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NamedDevice* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NamedDevice";
  }
  protected:
  explicit NamedDevice(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kPropertiesFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.DeviceProperties properties = 2;
  bool has_properties() const;
  private:
  bool _internal_has_properties() const;
  public:
  void clear_properties();
  const ::tensorflow::DeviceProperties& properties() const;
  PROTOBUF_NODISCARD ::tensorflow::DeviceProperties* release_properties();
  ::tensorflow::DeviceProperties* mutable_properties();
  void set_allocated_properties(::tensorflow::DeviceProperties* properties);
  private:
  const ::tensorflow::DeviceProperties& _internal_properties() const;
  ::tensorflow::DeviceProperties* _internal_mutable_properties();
  public:
  void unsafe_arena_set_allocated_properties(
      ::tensorflow::DeviceProperties* properties);
  ::tensorflow::DeviceProperties* unsafe_arena_release_properties();

  // @@protoc_insertion_point(class_scope:tensorflow.NamedDevice)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::DeviceProperties* properties_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// -------------------------------------------------------------------

// DeviceProperties

// string type = 1;
inline void DeviceProperties::clear_type() {
  _impl_.type_.ClearToEmpty();
}
inline const std::string& DeviceProperties::type() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.type)
  return _internal_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProperties::set_type(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.type)
}
inline std::string* DeviceProperties::mutable_type() {
  std::string* _s = _internal_mutable_type();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.type)
  return _s;
}
inline const std::string& DeviceProperties::_internal_type() const {
  return _impl_.type_.Get();
}
inline void DeviceProperties::_internal_set_type(const std::string& value) {
  
  _impl_.type_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProperties::_internal_mutable_type() {
  
  return _impl_.type_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProperties::release_type() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.type)
  return _impl_.type_.Release();
}
inline void DeviceProperties::set_allocated_type(std::string* type) {
  if (type != nullptr) {
    
  } else {
    
  }
  _impl_.type_.SetAllocated(type, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_.IsDefault()) {
    _impl_.type_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.type)
}

// string vendor = 2;
inline void DeviceProperties::clear_vendor() {
  _impl_.vendor_.ClearToEmpty();
}
inline const std::string& DeviceProperties::vendor() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.vendor)
  return _internal_vendor();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProperties::set_vendor(ArgT0&& arg0, ArgT... args) {
 
 _impl_.vendor_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.vendor)
}
inline std::string* DeviceProperties::mutable_vendor() {
  std::string* _s = _internal_mutable_vendor();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.vendor)
  return _s;
}
inline const std::string& DeviceProperties::_internal_vendor() const {
  return _impl_.vendor_.Get();
}
inline void DeviceProperties::_internal_set_vendor(const std::string& value) {
  
  _impl_.vendor_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProperties::_internal_mutable_vendor() {
  
  return _impl_.vendor_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProperties::release_vendor() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.vendor)
  return _impl_.vendor_.Release();
}
inline void DeviceProperties::set_allocated_vendor(std::string* vendor) {
  if (vendor != nullptr) {
    
  } else {
    
  }
  _impl_.vendor_.SetAllocated(vendor, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.vendor_.IsDefault()) {
    _impl_.vendor_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.vendor)
}

// string model = 3;
inline void DeviceProperties::clear_model() {
  _impl_.model_.ClearToEmpty();
}
inline const std::string& DeviceProperties::model() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.model)
  return _internal_model();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeviceProperties::set_model(ArgT0&& arg0, ArgT... args) {
 
 _impl_.model_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.model)
}
inline std::string* DeviceProperties::mutable_model() {
  std::string* _s = _internal_mutable_model();
  // @@protoc_insertion_point(field_mutable:tensorflow.DeviceProperties.model)
  return _s;
}
inline const std::string& DeviceProperties::_internal_model() const {
  return _impl_.model_.Get();
}
inline void DeviceProperties::_internal_set_model(const std::string& value) {
  
  _impl_.model_.Set(value, GetArenaForAllocation());
}
inline std::string* DeviceProperties::_internal_mutable_model() {
  
  return _impl_.model_.Mutable(GetArenaForAllocation());
}
inline std::string* DeviceProperties::release_model() {
  // @@protoc_insertion_point(field_release:tensorflow.DeviceProperties.model)
  return _impl_.model_.Release();
}
inline void DeviceProperties::set_allocated_model(std::string* model) {
  if (model != nullptr) {
    
  } else {
    
  }
  _impl_.model_.SetAllocated(model, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.model_.IsDefault()) {
    _impl_.model_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.DeviceProperties.model)
}

// int64 frequency = 4;
inline void DeviceProperties::clear_frequency() {
  _impl_.frequency_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_frequency() const {
  return _impl_.frequency_;
}
inline int64_t DeviceProperties::frequency() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.frequency)
  return _internal_frequency();
}
inline void DeviceProperties::_internal_set_frequency(int64_t value) {
  
  _impl_.frequency_ = value;
}
inline void DeviceProperties::set_frequency(int64_t value) {
  _internal_set_frequency(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.frequency)
}

// int64 num_cores = 5;
inline void DeviceProperties::clear_num_cores() {
  _impl_.num_cores_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_num_cores() const {
  return _impl_.num_cores_;
}
inline int64_t DeviceProperties::num_cores() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.num_cores)
  return _internal_num_cores();
}
inline void DeviceProperties::_internal_set_num_cores(int64_t value) {
  
  _impl_.num_cores_ = value;
}
inline void DeviceProperties::set_num_cores(int64_t value) {
  _internal_set_num_cores(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.num_cores)
}

// map<string, string> environment = 6;
inline int DeviceProperties::_internal_environment_size() const {
  return _impl_.environment_.size();
}
inline int DeviceProperties::environment_size() const {
  return _internal_environment_size();
}
inline void DeviceProperties::clear_environment() {
  _impl_.environment_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
DeviceProperties::_internal_environment() const {
  return _impl_.environment_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >&
DeviceProperties::environment() const {
  // @@protoc_insertion_point(field_map:tensorflow.DeviceProperties.environment)
  return _internal_environment();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
DeviceProperties::_internal_mutable_environment() {
  return _impl_.environment_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >*
DeviceProperties::mutable_environment() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DeviceProperties.environment)
  return _internal_mutable_environment();
}

// int64 num_registers = 7;
inline void DeviceProperties::clear_num_registers() {
  _impl_.num_registers_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_num_registers() const {
  return _impl_.num_registers_;
}
inline int64_t DeviceProperties::num_registers() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.num_registers)
  return _internal_num_registers();
}
inline void DeviceProperties::_internal_set_num_registers(int64_t value) {
  
  _impl_.num_registers_ = value;
}
inline void DeviceProperties::set_num_registers(int64_t value) {
  _internal_set_num_registers(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.num_registers)
}

// int64 l1_cache_size = 8;
inline void DeviceProperties::clear_l1_cache_size() {
  _impl_.l1_cache_size_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_l1_cache_size() const {
  return _impl_.l1_cache_size_;
}
inline int64_t DeviceProperties::l1_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l1_cache_size)
  return _internal_l1_cache_size();
}
inline void DeviceProperties::_internal_set_l1_cache_size(int64_t value) {
  
  _impl_.l1_cache_size_ = value;
}
inline void DeviceProperties::set_l1_cache_size(int64_t value) {
  _internal_set_l1_cache_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l1_cache_size)
}

// int64 l2_cache_size = 9;
inline void DeviceProperties::clear_l2_cache_size() {
  _impl_.l2_cache_size_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_l2_cache_size() const {
  return _impl_.l2_cache_size_;
}
inline int64_t DeviceProperties::l2_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l2_cache_size)
  return _internal_l2_cache_size();
}
inline void DeviceProperties::_internal_set_l2_cache_size(int64_t value) {
  
  _impl_.l2_cache_size_ = value;
}
inline void DeviceProperties::set_l2_cache_size(int64_t value) {
  _internal_set_l2_cache_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l2_cache_size)
}

// int64 l3_cache_size = 10;
inline void DeviceProperties::clear_l3_cache_size() {
  _impl_.l3_cache_size_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_l3_cache_size() const {
  return _impl_.l3_cache_size_;
}
inline int64_t DeviceProperties::l3_cache_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.l3_cache_size)
  return _internal_l3_cache_size();
}
inline void DeviceProperties::_internal_set_l3_cache_size(int64_t value) {
  
  _impl_.l3_cache_size_ = value;
}
inline void DeviceProperties::set_l3_cache_size(int64_t value) {
  _internal_set_l3_cache_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.l3_cache_size)
}

// int64 shared_memory_size_per_multiprocessor = 11;
inline void DeviceProperties::clear_shared_memory_size_per_multiprocessor() {
  _impl_.shared_memory_size_per_multiprocessor_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_shared_memory_size_per_multiprocessor() const {
  return _impl_.shared_memory_size_per_multiprocessor_;
}
inline int64_t DeviceProperties::shared_memory_size_per_multiprocessor() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.shared_memory_size_per_multiprocessor)
  return _internal_shared_memory_size_per_multiprocessor();
}
inline void DeviceProperties::_internal_set_shared_memory_size_per_multiprocessor(int64_t value) {
  
  _impl_.shared_memory_size_per_multiprocessor_ = value;
}
inline void DeviceProperties::set_shared_memory_size_per_multiprocessor(int64_t value) {
  _internal_set_shared_memory_size_per_multiprocessor(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.shared_memory_size_per_multiprocessor)
}

// int64 memory_size = 12;
inline void DeviceProperties::clear_memory_size() {
  _impl_.memory_size_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_memory_size() const {
  return _impl_.memory_size_;
}
inline int64_t DeviceProperties::memory_size() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.memory_size)
  return _internal_memory_size();
}
inline void DeviceProperties::_internal_set_memory_size(int64_t value) {
  
  _impl_.memory_size_ = value;
}
inline void DeviceProperties::set_memory_size(int64_t value) {
  _internal_set_memory_size(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.memory_size)
}

// int64 bandwidth = 13;
inline void DeviceProperties::clear_bandwidth() {
  _impl_.bandwidth_ = int64_t{0};
}
inline int64_t DeviceProperties::_internal_bandwidth() const {
  return _impl_.bandwidth_;
}
inline int64_t DeviceProperties::bandwidth() const {
  // @@protoc_insertion_point(field_get:tensorflow.DeviceProperties.bandwidth)
  return _internal_bandwidth();
}
inline void DeviceProperties::_internal_set_bandwidth(int64_t value) {
  
  _impl_.bandwidth_ = value;
}
inline void DeviceProperties::set_bandwidth(int64_t value) {
  _internal_set_bandwidth(value);
  // @@protoc_insertion_point(field_set:tensorflow.DeviceProperties.bandwidth)
}

// -------------------------------------------------------------------

// NamedDevice

// string name = 1;
inline void NamedDevice::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& NamedDevice::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedDevice.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NamedDevice::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NamedDevice.name)
}
inline std::string* NamedDevice::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedDevice.name)
  return _s;
}
inline const std::string& NamedDevice::_internal_name() const {
  return _impl_.name_.Get();
}
inline void NamedDevice::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* NamedDevice::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* NamedDevice::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedDevice.name)
  return _impl_.name_.Release();
}
inline void NamedDevice::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedDevice.name)
}

// .tensorflow.DeviceProperties properties = 2;
inline bool NamedDevice::_internal_has_properties() const {
  return this != internal_default_instance() && _impl_.properties_ != nullptr;
}
inline bool NamedDevice::has_properties() const {
  return _internal_has_properties();
}
inline void NamedDevice::clear_properties() {
  if (GetArenaForAllocation() == nullptr && _impl_.properties_ != nullptr) {
    delete _impl_.properties_;
  }
  _impl_.properties_ = nullptr;
}
inline const ::tensorflow::DeviceProperties& NamedDevice::_internal_properties() const {
  const ::tensorflow::DeviceProperties* p = _impl_.properties_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::DeviceProperties&>(
      ::tensorflow::_DeviceProperties_default_instance_);
}
inline const ::tensorflow::DeviceProperties& NamedDevice::properties() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedDevice.properties)
  return _internal_properties();
}
inline void NamedDevice::unsafe_arena_set_allocated_properties(
    ::tensorflow::DeviceProperties* properties) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.properties_);
  }
  _impl_.properties_ = properties;
  if (properties) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.NamedDevice.properties)
}
inline ::tensorflow::DeviceProperties* NamedDevice::release_properties() {
  
  ::tensorflow::DeviceProperties* temp = _impl_.properties_;
  _impl_.properties_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::DeviceProperties* NamedDevice::unsafe_arena_release_properties() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedDevice.properties)
  
  ::tensorflow::DeviceProperties* temp = _impl_.properties_;
  _impl_.properties_ = nullptr;
  return temp;
}
inline ::tensorflow::DeviceProperties* NamedDevice::_internal_mutable_properties() {
  
  if (_impl_.properties_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::DeviceProperties>(GetArenaForAllocation());
    _impl_.properties_ = p;
  }
  return _impl_.properties_;
}
inline ::tensorflow::DeviceProperties* NamedDevice::mutable_properties() {
  ::tensorflow::DeviceProperties* _msg = _internal_mutable_properties();
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedDevice.properties)
  return _msg;
}
inline void NamedDevice::set_allocated_properties(::tensorflow::DeviceProperties* properties) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.properties_;
  }
  if (properties) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(properties);
    if (message_arena != submessage_arena) {
      properties = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, properties, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.properties_ = properties;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedDevice.properties)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5fproperties_2eproto
