#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速測試皮膚分析器的基本功能
"""

import os
import sys

def test_imports():
    """測試所有必要的模組導入"""
    print("🔍 測試模組導入...")
    
    try:
        import cv2
        print("✅ OpenCV 導入成功")
    except ImportError as e:
        print(f"❌ OpenCV 導入失敗: {e}")
        return False
    
    try:
        import mediapipe as mp
        print("✅ MediaPipe 導入成功")
    except ImportError as e:
        print(f"❌ MediaPipe 導入失敗: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy 導入成功")
    except ImportError as e:
        print(f"❌ NumPy 導入失敗: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL 導入成功")
    except ImportError as e:
        print(f"❌ PIL 導入失敗: {e}")
        return False
    
    try:
        from skimage import feature
        print("✅ scikit-image 導入成功")
    except ImportError as e:
        print(f"❌ scikit-image 導入失敗: {e}")
        return False
    
    try:
        # 設置 TensorFlow 日誌級別
        os.environ['TF_CPP_MIN_LOG_LEVEL'] = '2'
        from tensorflow.keras.models import load_model
        print("✅ TensorFlow 導入成功")
    except ImportError as e:
        print(f"❌ TensorFlow 導入失敗: {e}")
        return False
    
    return True

def test_files():
    """檢查必要文件是否存在"""
    print("\n📁 檢查必要文件...")
    
    files_to_check = [
        'skin_analyzer_cli.py',
        'more_data(3).h5',
        'requirements.txt'
    ]
    
    all_files_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_files_exist = False
    
    return all_files_exist

def test_basic_functionality():
    """測試基本功能"""
    print("\n🧪 測試基本功能...")
    
    try:
        # 導入皮膚分析器
        from skin_analyzer_cli import SkinAnalyzer
        
        # 創建分析器實例（不載入模型以加快測試）
        print("創建分析器實例...")
        
        # 測試是否能正常初始化
        print("✅ 皮膚分析器可以正常導入和初始化")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能測試失敗: {e}")
        return False

def main():
    print("🎯 MySkin.ai 皮膚分析器 - 快速測試")
    print("=" * 50)
    
    # 測試導入
    imports_ok = test_imports()
    
    # 測試文件
    files_ok = test_files()
    
    # 測試基本功能
    if imports_ok and files_ok:
        functionality_ok = test_basic_functionality()
    else:
        functionality_ok = False
    
    print("\n" + "=" * 50)
    print("📊 測試結果總結:")
    print(f"模組導入: {'✅ 通過' if imports_ok else '❌ 失敗'}")
    print(f"文件檢查: {'✅ 通過' if files_ok else '❌ 失敗'}")
    print(f"基本功能: {'✅ 通過' if functionality_ok else '❌ 失敗'}")
    
    if imports_ok and files_ok and functionality_ok:
        print("\n🎉 所有測試通過！皮膚分析器已準備就緒。")
        print("\n📝 使用方法:")
        print("1. 拖拽圖片到 run_skin_analysis.bat")
        print("2. 或運行: python skin_analyzer_cli.py 'image_path'")
    else:
        print("\n⚠️  部分測試失敗，請檢查環境配置。")
        if not imports_ok:
            print("💡 建議: 運行 'pip install -r requirements.txt' 安裝依賴")
        if not files_ok:
            print("💡 建議: 確保所有必要文件都在當前目錄中")

if __name__ == "__main__":
    main()
