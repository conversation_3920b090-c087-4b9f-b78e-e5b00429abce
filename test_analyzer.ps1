# Test the skin analyzer
Write-Host "Testing MySkin.ai Skin Analyzer..." -ForegroundColor Green

# Activate virtual environment
if (Test-Path "venv\Scripts\Activate.ps1") {
    Write-Host "Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
}

# Test if we can import required modules
Write-Host "Testing Python imports..." -ForegroundColor Yellow
python -c "
try:
    import cv2
    import mediapipe as mp
    import numpy as np
    from PIL import Image
    from skimage import feature
    from tensorflow.keras.models import load_model
    print('✅ All imports successful!')
except ImportError as e:
    print(f'❌ Import error: {e}')
    exit(1)
"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Environment setup successful!" -ForegroundColor Green
    Write-Host "You can now use the skin analyzer by:" -ForegroundColor Cyan
    Write-Host "1. Dragging image files onto run_skin_analysis.bat" -ForegroundColor White
    Write-Host "2. Running: python skin_analyzer_cli.py 'image_path'" -ForegroundColor White
} else {
    Write-Host "❌ Environment setup failed. Please install dependencies." -ForegroundColor Red
}

Read-Host "Press Enter to continue..."
