/* Copyright 2021 The TensorFlow Authors. All Rights Reserved.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
==============================================================================*/

#ifndef TENSORFLOW_CORE_TPU_OPS_TPU_EMBEDDING_OPS_H_
#define TENSORFLOW_CORE_TPU_OPS_TPU_EMBEDDING_OPS_H_

#include <string>
#include <vector>

namespace tensorflow {
// Get the names of the LoadTPUEmbedding*Parameters ops.
std::vector<std::string> GetPerTableLoadOptimizationParametersOps();

// Get the names of the RetrieveTPUEmbedding*Parameters ops.
std::vector<std::string> GetPerTableRetrieveOptimizationParametersOps();

// Type enum of elements in deduplication data tuple.
enum DedupTupleElementType {
  kInteger = 0,
  kFloat = 1,
};

}  // namespace tensorflow

#endif  // TENSORFLOW_CORE_TPU_OPS_TPU_EMBEDDING_OPS_H_
