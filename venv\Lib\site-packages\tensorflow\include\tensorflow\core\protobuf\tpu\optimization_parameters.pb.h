// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/tpu/optimization_parameters.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/wrappers.pb.h>
#include "xla/service/hlo.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
namespace tensorflow {
namespace tpu {
class AdadeltaParameters;
struct AdadeltaParametersDefaultTypeInternal;
extern AdadeltaParametersDefaultTypeInternal _AdadeltaParameters_default_instance_;
class AdagradMomentumParameters;
struct AdagradMomentumParametersDefaultTypeInternal;
extern AdagradMomentumParametersDefaultTypeInternal _AdagradMomentumParameters_default_instance_;
class AdagradParameters;
struct AdagradParametersDefaultTypeInternal;
extern AdagradParametersDefaultTypeInternal _AdagradParameters_default_instance_;
class AdamParameters;
struct AdamParametersDefaultTypeInternal;
extern AdamParametersDefaultTypeInternal _AdamParameters_default_instance_;
class AssignParameters;
struct AssignParametersDefaultTypeInternal;
extern AssignParametersDefaultTypeInternal _AssignParameters_default_instance_;
class BoundedAdagradParameters;
struct BoundedAdagradParametersDefaultTypeInternal;
extern BoundedAdagradParametersDefaultTypeInternal _BoundedAdagradParameters_default_instance_;
class CenteredRmsPropParameters;
struct CenteredRmsPropParametersDefaultTypeInternal;
extern CenteredRmsPropParametersDefaultTypeInternal _CenteredRmsPropParameters_default_instance_;
class ClippingLimits;
struct ClippingLimitsDefaultTypeInternal;
extern ClippingLimitsDefaultTypeInternal _ClippingLimits_default_instance_;
class DynamicLearningRate;
struct DynamicLearningRateDefaultTypeInternal;
extern DynamicLearningRateDefaultTypeInternal _DynamicLearningRate_default_instance_;
class FrequencyEstimatorParameters;
struct FrequencyEstimatorParametersDefaultTypeInternal;
extern FrequencyEstimatorParametersDefaultTypeInternal _FrequencyEstimatorParameters_default_instance_;
class FtrlParameters;
struct FtrlParametersDefaultTypeInternal;
extern FtrlParametersDefaultTypeInternal _FtrlParameters_default_instance_;
class GradientAccumulationStatus;
struct GradientAccumulationStatusDefaultTypeInternal;
extern GradientAccumulationStatusDefaultTypeInternal _GradientAccumulationStatus_default_instance_;
class HotIdReplicationConfiguration;
struct HotIdReplicationConfigurationDefaultTypeInternal;
extern HotIdReplicationConfigurationDefaultTypeInternal _HotIdReplicationConfiguration_default_instance_;
class LearningRate;
struct LearningRateDefaultTypeInternal;
extern LearningRateDefaultTypeInternal _LearningRate_default_instance_;
class LionParameters;
struct LionParametersDefaultTypeInternal;
extern LionParametersDefaultTypeInternal _LionParameters_default_instance_;
class LowDimensionalPackingStatus;
struct LowDimensionalPackingStatusDefaultTypeInternal;
extern LowDimensionalPackingStatusDefaultTypeInternal _LowDimensionalPackingStatus_default_instance_;
class MdlAdagradLightParameters;
struct MdlAdagradLightParametersDefaultTypeInternal;
extern MdlAdagradLightParametersDefaultTypeInternal _MdlAdagradLightParameters_default_instance_;
class MomentumParameters;
struct MomentumParametersDefaultTypeInternal;
extern MomentumParametersDefaultTypeInternal _MomentumParameters_default_instance_;
class OnlineYogiParameters;
struct OnlineYogiParametersDefaultTypeInternal;
extern OnlineYogiParametersDefaultTypeInternal _OnlineYogiParameters_default_instance_;
class OptimizationParameters;
struct OptimizationParametersDefaultTypeInternal;
extern OptimizationParametersDefaultTypeInternal _OptimizationParameters_default_instance_;
class ProximalAdagradParameters;
struct ProximalAdagradParametersDefaultTypeInternal;
extern ProximalAdagradParametersDefaultTypeInternal _ProximalAdagradParameters_default_instance_;
class ProximalYogiParameters;
struct ProximalYogiParametersDefaultTypeInternal;
extern ProximalYogiParametersDefaultTypeInternal _ProximalYogiParameters_default_instance_;
class RmsPropParameters;
struct RmsPropParametersDefaultTypeInternal;
extern RmsPropParametersDefaultTypeInternal _RmsPropParameters_default_instance_;
class SimulatedQuantization;
struct SimulatedQuantizationDefaultTypeInternal;
extern SimulatedQuantizationDefaultTypeInternal _SimulatedQuantization_default_instance_;
class StateVariableSpecification;
struct StateVariableSpecificationDefaultTypeInternal;
extern StateVariableSpecificationDefaultTypeInternal _StateVariableSpecification_default_instance_;
class StateVariableSpecification_FillWithConstant;
struct StateVariableSpecification_FillWithConstantDefaultTypeInternal;
extern StateVariableSpecification_FillWithConstantDefaultTypeInternal _StateVariableSpecification_FillWithConstant_default_instance_;
class StateVariableSpecification_UserDefined;
struct StateVariableSpecification_UserDefinedDefaultTypeInternal;
extern StateVariableSpecification_UserDefinedDefaultTypeInternal _StateVariableSpecification_UserDefined_default_instance_;
class StochasticGradientDescentParameters;
struct StochasticGradientDescentParametersDefaultTypeInternal;
extern StochasticGradientDescentParametersDefaultTypeInternal _StochasticGradientDescentParameters_default_instance_;
class UserDefinedProgramParameters;
struct UserDefinedProgramParametersDefaultTypeInternal;
extern UserDefinedProgramParametersDefaultTypeInternal _UserDefinedProgramParameters_default_instance_;
}  // namespace tpu
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::tpu::AdadeltaParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdadeltaParameters>(Arena*);
template<> ::tensorflow::tpu::AdagradMomentumParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdagradMomentumParameters>(Arena*);
template<> ::tensorflow::tpu::AdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdagradParameters>(Arena*);
template<> ::tensorflow::tpu::AdamParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AdamParameters>(Arena*);
template<> ::tensorflow::tpu::AssignParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::AssignParameters>(Arena*);
template<> ::tensorflow::tpu::BoundedAdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::BoundedAdagradParameters>(Arena*);
template<> ::tensorflow::tpu::CenteredRmsPropParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::CenteredRmsPropParameters>(Arena*);
template<> ::tensorflow::tpu::ClippingLimits* Arena::CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(Arena*);
template<> ::tensorflow::tpu::DynamicLearningRate* Arena::CreateMaybeMessage<::tensorflow::tpu::DynamicLearningRate>(Arena*);
template<> ::tensorflow::tpu::FrequencyEstimatorParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::FrequencyEstimatorParameters>(Arena*);
template<> ::tensorflow::tpu::FtrlParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::FtrlParameters>(Arena*);
template<> ::tensorflow::tpu::GradientAccumulationStatus* Arena::CreateMaybeMessage<::tensorflow::tpu::GradientAccumulationStatus>(Arena*);
template<> ::tensorflow::tpu::HotIdReplicationConfiguration* Arena::CreateMaybeMessage<::tensorflow::tpu::HotIdReplicationConfiguration>(Arena*);
template<> ::tensorflow::tpu::LearningRate* Arena::CreateMaybeMessage<::tensorflow::tpu::LearningRate>(Arena*);
template<> ::tensorflow::tpu::LionParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::LionParameters>(Arena*);
template<> ::tensorflow::tpu::LowDimensionalPackingStatus* Arena::CreateMaybeMessage<::tensorflow::tpu::LowDimensionalPackingStatus>(Arena*);
template<> ::tensorflow::tpu::MdlAdagradLightParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::MdlAdagradLightParameters>(Arena*);
template<> ::tensorflow::tpu::MomentumParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::MomentumParameters>(Arena*);
template<> ::tensorflow::tpu::OnlineYogiParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::OnlineYogiParameters>(Arena*);
template<> ::tensorflow::tpu::OptimizationParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::OptimizationParameters>(Arena*);
template<> ::tensorflow::tpu::ProximalAdagradParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::ProximalAdagradParameters>(Arena*);
template<> ::tensorflow::tpu::ProximalYogiParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::ProximalYogiParameters>(Arena*);
template<> ::tensorflow::tpu::RmsPropParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::RmsPropParameters>(Arena*);
template<> ::tensorflow::tpu::SimulatedQuantization* Arena::CreateMaybeMessage<::tensorflow::tpu::SimulatedQuantization>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification_FillWithConstant>(Arena*);
template<> ::tensorflow::tpu::StateVariableSpecification_UserDefined* Arena::CreateMaybeMessage<::tensorflow::tpu::StateVariableSpecification_UserDefined>(Arena*);
template<> ::tensorflow::tpu::StochasticGradientDescentParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::StochasticGradientDescentParameters>(Arena*);
template<> ::tensorflow::tpu::UserDefinedProgramParameters* Arena::CreateMaybeMessage<::tensorflow::tpu::UserDefinedProgramParameters>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {
namespace tpu {

enum GradientAccumulationStatus_Status : int {
  GradientAccumulationStatus_Status_UNSPECIFIED = 0,
  GradientAccumulationStatus_Status_ENABLED = 1,
  GradientAccumulationStatus_Status_DISABLED = 2,
  GradientAccumulationStatus_Status_GradientAccumulationStatus_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  GradientAccumulationStatus_Status_GradientAccumulationStatus_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool GradientAccumulationStatus_Status_IsValid(int value);
constexpr GradientAccumulationStatus_Status GradientAccumulationStatus_Status_Status_MIN = GradientAccumulationStatus_Status_UNSPECIFIED;
constexpr GradientAccumulationStatus_Status GradientAccumulationStatus_Status_Status_MAX = GradientAccumulationStatus_Status_DISABLED;
constexpr int GradientAccumulationStatus_Status_Status_ARRAYSIZE = GradientAccumulationStatus_Status_Status_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* GradientAccumulationStatus_Status_descriptor();
template<typename T>
inline const std::string& GradientAccumulationStatus_Status_Name(T enum_t_value) {
  static_assert(::std::is_same<T, GradientAccumulationStatus_Status>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function GradientAccumulationStatus_Status_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    GradientAccumulationStatus_Status_descriptor(), enum_t_value);
}
inline bool GradientAccumulationStatus_Status_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, GradientAccumulationStatus_Status* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<GradientAccumulationStatus_Status>(
    GradientAccumulationStatus_Status_descriptor(), name, value);
}
enum LowDimensionalPackingStatus_Status : int {
  LowDimensionalPackingStatus_Status_UNSPECIFIED = 0,
  LowDimensionalPackingStatus_Status_ENABLED = 1,
  LowDimensionalPackingStatus_Status_DISABLED = 2,
  LowDimensionalPackingStatus_Status_LowDimensionalPackingStatus_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  LowDimensionalPackingStatus_Status_LowDimensionalPackingStatus_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool LowDimensionalPackingStatus_Status_IsValid(int value);
constexpr LowDimensionalPackingStatus_Status LowDimensionalPackingStatus_Status_Status_MIN = LowDimensionalPackingStatus_Status_UNSPECIFIED;
constexpr LowDimensionalPackingStatus_Status LowDimensionalPackingStatus_Status_Status_MAX = LowDimensionalPackingStatus_Status_DISABLED;
constexpr int LowDimensionalPackingStatus_Status_Status_ARRAYSIZE = LowDimensionalPackingStatus_Status_Status_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* LowDimensionalPackingStatus_Status_descriptor();
template<typename T>
inline const std::string& LowDimensionalPackingStatus_Status_Name(T enum_t_value) {
  static_assert(::std::is_same<T, LowDimensionalPackingStatus_Status>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function LowDimensionalPackingStatus_Status_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    LowDimensionalPackingStatus_Status_descriptor(), enum_t_value);
}
inline bool LowDimensionalPackingStatus_Status_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, LowDimensionalPackingStatus_Status* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<LowDimensionalPackingStatus_Status>(
    LowDimensionalPackingStatus_Status_descriptor(), name, value);
}
enum HotIdReplicationConfiguration_Status : int {
  HotIdReplicationConfiguration_Status_UNSPECIFIED = 0,
  HotIdReplicationConfiguration_Status_ENABLED = 1,
  HotIdReplicationConfiguration_Status_DISABLED = 2,
  HotIdReplicationConfiguration_Status_MIGRATION_ONLY = 3,
  HotIdReplicationConfiguration_Status_HotIdReplicationConfiguration_Status_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  HotIdReplicationConfiguration_Status_HotIdReplicationConfiguration_Status_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool HotIdReplicationConfiguration_Status_IsValid(int value);
constexpr HotIdReplicationConfiguration_Status HotIdReplicationConfiguration_Status_Status_MIN = HotIdReplicationConfiguration_Status_UNSPECIFIED;
constexpr HotIdReplicationConfiguration_Status HotIdReplicationConfiguration_Status_Status_MAX = HotIdReplicationConfiguration_Status_MIGRATION_ONLY;
constexpr int HotIdReplicationConfiguration_Status_Status_ARRAYSIZE = HotIdReplicationConfiguration_Status_Status_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* HotIdReplicationConfiguration_Status_descriptor();
template<typename T>
inline const std::string& HotIdReplicationConfiguration_Status_Name(T enum_t_value) {
  static_assert(::std::is_same<T, HotIdReplicationConfiguration_Status>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function HotIdReplicationConfiguration_Status_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    HotIdReplicationConfiguration_Status_descriptor(), enum_t_value);
}
inline bool HotIdReplicationConfiguration_Status_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, HotIdReplicationConfiguration_Status* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<HotIdReplicationConfiguration_Status>(
    HotIdReplicationConfiguration_Status_descriptor(), name, value);
}
// ===================================================================

class ClippingLimits final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ClippingLimits) */ {
 public:
  inline ClippingLimits() : ClippingLimits(nullptr) {}
  ~ClippingLimits() override;
  explicit PROTOBUF_CONSTEXPR ClippingLimits(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ClippingLimits(const ClippingLimits& from);
  ClippingLimits(ClippingLimits&& from) noexcept
    : ClippingLimits() {
    *this = ::std::move(from);
  }

  inline ClippingLimits& operator=(const ClippingLimits& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClippingLimits& operator=(ClippingLimits&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ClippingLimits& default_instance() {
    return *internal_default_instance();
  }
  static inline const ClippingLimits* internal_default_instance() {
    return reinterpret_cast<const ClippingLimits*>(
               &_ClippingLimits_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ClippingLimits& a, ClippingLimits& b) {
    a.Swap(&b);
  }
  inline void Swap(ClippingLimits* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClippingLimits* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ClippingLimits* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ClippingLimits>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ClippingLimits& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ClippingLimits& from) {
    ClippingLimits::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClippingLimits* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.ClippingLimits";
  }
  protected:
  explicit ClippingLimits(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLowerFieldNumber = 1,
    kUpperFieldNumber = 2,
  };
  // .google.protobuf.FloatValue lower = 1;
  bool has_lower() const;
  private:
  bool _internal_has_lower() const;
  public:
  void clear_lower();
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& lower() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::FloatValue* release_lower();
  ::PROTOBUF_NAMESPACE_ID::FloatValue* mutable_lower();
  void set_allocated_lower(::PROTOBUF_NAMESPACE_ID::FloatValue* lower);
  private:
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& _internal_lower() const;
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _internal_mutable_lower();
  public:
  void unsafe_arena_set_allocated_lower(
      ::PROTOBUF_NAMESPACE_ID::FloatValue* lower);
  ::PROTOBUF_NAMESPACE_ID::FloatValue* unsafe_arena_release_lower();

  // .google.protobuf.FloatValue upper = 2;
  bool has_upper() const;
  private:
  bool _internal_has_upper() const;
  public:
  void clear_upper();
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& upper() const;
  PROTOBUF_NODISCARD ::PROTOBUF_NAMESPACE_ID::FloatValue* release_upper();
  ::PROTOBUF_NAMESPACE_ID::FloatValue* mutable_upper();
  void set_allocated_upper(::PROTOBUF_NAMESPACE_ID::FloatValue* upper);
  private:
  const ::PROTOBUF_NAMESPACE_ID::FloatValue& _internal_upper() const;
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _internal_mutable_upper();
  public:
  void unsafe_arena_set_allocated_upper(
      ::PROTOBUF_NAMESPACE_ID::FloatValue* upper);
  ::PROTOBUF_NAMESPACE_ID::FloatValue* unsafe_arena_release_upper();

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ClippingLimits)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::FloatValue* lower_;
    ::PROTOBUF_NAMESPACE_ID::FloatValue* upper_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class SimulatedQuantization final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.SimulatedQuantization) */ {
 public:
  inline SimulatedQuantization() : SimulatedQuantization(nullptr) {}
  ~SimulatedQuantization() override;
  explicit PROTOBUF_CONSTEXPR SimulatedQuantization(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SimulatedQuantization(const SimulatedQuantization& from);
  SimulatedQuantization(SimulatedQuantization&& from) noexcept
    : SimulatedQuantization() {
    *this = ::std::move(from);
  }

  inline SimulatedQuantization& operator=(const SimulatedQuantization& from) {
    CopyFrom(from);
    return *this;
  }
  inline SimulatedQuantization& operator=(SimulatedQuantization&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SimulatedQuantization& default_instance() {
    return *internal_default_instance();
  }
  static inline const SimulatedQuantization* internal_default_instance() {
    return reinterpret_cast<const SimulatedQuantization*>(
               &_SimulatedQuantization_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SimulatedQuantization& a, SimulatedQuantization& b) {
    a.Swap(&b);
  }
  inline void Swap(SimulatedQuantization* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SimulatedQuantization* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SimulatedQuantization* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SimulatedQuantization>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SimulatedQuantization& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const SimulatedQuantization& from) {
    SimulatedQuantization::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SimulatedQuantization* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.SimulatedQuantization";
  }
  protected:
  explicit SimulatedQuantization(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClippingLimitsFieldNumber = 2,
    kEnabledFieldNumber = 1,
    kNumBucketsFieldNumber = 3,
  };
  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  bool has_clipping_limits() const;
  private:
  bool _internal_has_clipping_limits() const;
  public:
  void clear_clipping_limits();
  const ::tensorflow::tpu::ClippingLimits& clipping_limits() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::ClippingLimits* release_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_clipping_limits();
  void set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits);
  private:
  const ::tensorflow::tpu::ClippingLimits& _internal_clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* _internal_mutable_clipping_limits();
  public:
  void unsafe_arena_set_allocated_clipping_limits(
      ::tensorflow::tpu::ClippingLimits* clipping_limits);
  ::tensorflow::tpu::ClippingLimits* unsafe_arena_release_clipping_limits();

  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // int32 num_buckets = 3;
  void clear_num_buckets();
  int32_t num_buckets() const;
  void set_num_buckets(int32_t value);
  private:
  int32_t _internal_num_buckets() const;
  void _internal_set_num_buckets(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.SimulatedQuantization)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::tpu::ClippingLimits* clipping_limits_;
    bool enabled_;
    int32_t num_buckets_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class DynamicLearningRate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.DynamicLearningRate) */ {
 public:
  inline DynamicLearningRate() : DynamicLearningRate(nullptr) {}
  ~DynamicLearningRate() override;
  explicit PROTOBUF_CONSTEXPR DynamicLearningRate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DynamicLearningRate(const DynamicLearningRate& from);
  DynamicLearningRate(DynamicLearningRate&& from) noexcept
    : DynamicLearningRate() {
    *this = ::std::move(from);
  }

  inline DynamicLearningRate& operator=(const DynamicLearningRate& from) {
    CopyFrom(from);
    return *this;
  }
  inline DynamicLearningRate& operator=(DynamicLearningRate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DynamicLearningRate& default_instance() {
    return *internal_default_instance();
  }
  static inline const DynamicLearningRate* internal_default_instance() {
    return reinterpret_cast<const DynamicLearningRate*>(
               &_DynamicLearningRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DynamicLearningRate& a, DynamicLearningRate& b) {
    a.Swap(&b);
  }
  inline void Swap(DynamicLearningRate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DynamicLearningRate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DynamicLearningRate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DynamicLearningRate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DynamicLearningRate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DynamicLearningRate& from) {
    DynamicLearningRate::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DynamicLearningRate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.DynamicLearningRate";
  }
  protected:
  explicit DynamicLearningRate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTagFieldNumber = 1,
  };
  // int32 tag = 1;
  void clear_tag();
  int32_t tag() const;
  void set_tag(int32_t value);
  private:
  int32_t _internal_tag() const;
  void _internal_set_tag(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.DynamicLearningRate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int32_t tag_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class LearningRate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.LearningRate) */ {
 public:
  inline LearningRate() : LearningRate(nullptr) {}
  ~LearningRate() override;
  explicit PROTOBUF_CONSTEXPR LearningRate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LearningRate(const LearningRate& from);
  LearningRate(LearningRate&& from) noexcept
    : LearningRate() {
    *this = ::std::move(from);
  }

  inline LearningRate& operator=(const LearningRate& from) {
    CopyFrom(from);
    return *this;
  }
  inline LearningRate& operator=(LearningRate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LearningRate& default_instance() {
    return *internal_default_instance();
  }
  enum LearningRateCase {
    kConstant = 1,
    kDynamic = 2,
    LEARNING_RATE_NOT_SET = 0,
  };

  static inline const LearningRate* internal_default_instance() {
    return reinterpret_cast<const LearningRate*>(
               &_LearningRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LearningRate& a, LearningRate& b) {
    a.Swap(&b);
  }
  inline void Swap(LearningRate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LearningRate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LearningRate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LearningRate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LearningRate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LearningRate& from) {
    LearningRate::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LearningRate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.LearningRate";
  }
  protected:
  explicit LearningRate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConstantFieldNumber = 1,
    kDynamicFieldNumber = 2,
  };
  // float constant = 1;
  bool has_constant() const;
  private:
  bool _internal_has_constant() const;
  public:
  void clear_constant();
  float constant() const;
  void set_constant(float value);
  private:
  float _internal_constant() const;
  void _internal_set_constant(float value);
  public:

  // .tensorflow.tpu.DynamicLearningRate dynamic = 2;
  bool has_dynamic() const;
  private:
  bool _internal_has_dynamic() const;
  public:
  void clear_dynamic();
  const ::tensorflow::tpu::DynamicLearningRate& dynamic() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::DynamicLearningRate* release_dynamic();
  ::tensorflow::tpu::DynamicLearningRate* mutable_dynamic();
  void set_allocated_dynamic(::tensorflow::tpu::DynamicLearningRate* dynamic);
  private:
  const ::tensorflow::tpu::DynamicLearningRate& _internal_dynamic() const;
  ::tensorflow::tpu::DynamicLearningRate* _internal_mutable_dynamic();
  public:
  void unsafe_arena_set_allocated_dynamic(
      ::tensorflow::tpu::DynamicLearningRate* dynamic);
  ::tensorflow::tpu::DynamicLearningRate* unsafe_arena_release_dynamic();

  void clear_learning_rate();
  LearningRateCase learning_rate_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.LearningRate)
 private:
  class _Internal;
  void set_has_constant();
  void set_has_dynamic();

  inline bool has_learning_rate() const;
  inline void clear_has_learning_rate();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union LearningRateUnion {
      constexpr LearningRateUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      float constant_;
      ::tensorflow::tpu::DynamicLearningRate* dynamic_;
    } learning_rate_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdagradParameters final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdagradParameters) */ {
 public:
  inline AdagradParameters() : AdagradParameters(nullptr) {}
  explicit PROTOBUF_CONSTEXPR AdagradParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdagradParameters(const AdagradParameters& from);
  AdagradParameters(AdagradParameters&& from) noexcept
    : AdagradParameters() {
    *this = ::std::move(from);
  }

  inline AdagradParameters& operator=(const AdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdagradParameters& operator=(AdagradParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdagradParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdagradParameters* internal_default_instance() {
    return reinterpret_cast<const AdagradParameters*>(
               &_AdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AdagradParameters& a, AdagradParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdagradParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdagradParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdagradParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdagradParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const AdagradParameters& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const AdagradParameters& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdagradParameters";
  }
  protected:
  explicit AdagradParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdagradParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdagradMomentumParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdagradMomentumParameters) */ {
 public:
  inline AdagradMomentumParameters() : AdagradMomentumParameters(nullptr) {}
  ~AdagradMomentumParameters() override;
  explicit PROTOBUF_CONSTEXPR AdagradMomentumParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdagradMomentumParameters(const AdagradMomentumParameters& from);
  AdagradMomentumParameters(AdagradMomentumParameters&& from) noexcept
    : AdagradMomentumParameters() {
    *this = ::std::move(from);
  }

  inline AdagradMomentumParameters& operator=(const AdagradMomentumParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdagradMomentumParameters& operator=(AdagradMomentumParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdagradMomentumParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdagradMomentumParameters* internal_default_instance() {
    return reinterpret_cast<const AdagradMomentumParameters*>(
               &_AdagradMomentumParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AdagradMomentumParameters& a, AdagradMomentumParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdagradMomentumParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdagradMomentumParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdagradMomentumParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdagradMomentumParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdagradMomentumParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdagradMomentumParameters& from) {
    AdagradMomentumParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdagradMomentumParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdagradMomentumParameters";
  }
  protected:
  explicit AdagradMomentumParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMomentumFieldNumber = 1,
    kUseNesterovFieldNumber = 2,
    kExponentFieldNumber = 3,
    kBeta2FieldNumber = 4,
    kEpsilonFieldNumber = 5,
  };
  // float momentum = 1;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);
  private:
  float _internal_momentum() const;
  void _internal_set_momentum(float value);
  public:

  // bool use_nesterov = 2;
  void clear_use_nesterov();
  bool use_nesterov() const;
  void set_use_nesterov(bool value);
  private:
  bool _internal_use_nesterov() const;
  void _internal_set_use_nesterov(bool value);
  public:

  // float exponent = 3;
  void clear_exponent();
  float exponent() const;
  void set_exponent(float value);
  private:
  float _internal_exponent() const;
  void _internal_set_exponent(float value);
  public:

  // float beta2 = 4;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);
  private:
  float _internal_beta2() const;
  void _internal_set_beta2(float value);
  public:

  // float epsilon = 5;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);
  private:
  float _internal_epsilon() const;
  void _internal_set_epsilon(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdagradMomentumParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float momentum_;
    bool use_nesterov_;
    float exponent_;
    float beta2_;
    float epsilon_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class BoundedAdagradParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.BoundedAdagradParameters) */ {
 public:
  inline BoundedAdagradParameters() : BoundedAdagradParameters(nullptr) {}
  ~BoundedAdagradParameters() override;
  explicit PROTOBUF_CONSTEXPR BoundedAdagradParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BoundedAdagradParameters(const BoundedAdagradParameters& from);
  BoundedAdagradParameters(BoundedAdagradParameters&& from) noexcept
    : BoundedAdagradParameters() {
    *this = ::std::move(from);
  }

  inline BoundedAdagradParameters& operator=(const BoundedAdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline BoundedAdagradParameters& operator=(BoundedAdagradParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BoundedAdagradParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const BoundedAdagradParameters* internal_default_instance() {
    return reinterpret_cast<const BoundedAdagradParameters*>(
               &_BoundedAdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(BoundedAdagradParameters& a, BoundedAdagradParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(BoundedAdagradParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BoundedAdagradParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BoundedAdagradParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BoundedAdagradParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BoundedAdagradParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BoundedAdagradParameters& from) {
    BoundedAdagradParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BoundedAdagradParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.BoundedAdagradParameters";
  }
  protected:
  explicit BoundedAdagradParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUpdateAccumulatorFirstFieldNumber = 1,
    kMaxVarUpdateFieldNumber = 2,
    kMaxAccumulatorFieldNumber = 3,
  };
  // bool update_accumulator_first = 1;
  void clear_update_accumulator_first();
  bool update_accumulator_first() const;
  void set_update_accumulator_first(bool value);
  private:
  bool _internal_update_accumulator_first() const;
  void _internal_set_update_accumulator_first(bool value);
  public:

  // float max_var_update = 2;
  void clear_max_var_update();
  float max_var_update() const;
  void set_max_var_update(float value);
  private:
  float _internal_max_var_update() const;
  void _internal_set_max_var_update(float value);
  public:

  // float max_accumulator = 3;
  void clear_max_accumulator();
  float max_accumulator() const;
  void set_max_accumulator(float value);
  private:
  float _internal_max_accumulator() const;
  void _internal_set_max_accumulator(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.BoundedAdagradParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool update_accumulator_first_;
    float max_var_update_;
    float max_accumulator_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StochasticGradientDescentParameters final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StochasticGradientDescentParameters) */ {
 public:
  inline StochasticGradientDescentParameters() : StochasticGradientDescentParameters(nullptr) {}
  explicit PROTOBUF_CONSTEXPR StochasticGradientDescentParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StochasticGradientDescentParameters(const StochasticGradientDescentParameters& from);
  StochasticGradientDescentParameters(StochasticGradientDescentParameters&& from) noexcept
    : StochasticGradientDescentParameters() {
    *this = ::std::move(from);
  }

  inline StochasticGradientDescentParameters& operator=(const StochasticGradientDescentParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline StochasticGradientDescentParameters& operator=(StochasticGradientDescentParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StochasticGradientDescentParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const StochasticGradientDescentParameters* internal_default_instance() {
    return reinterpret_cast<const StochasticGradientDescentParameters*>(
               &_StochasticGradientDescentParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(StochasticGradientDescentParameters& a, StochasticGradientDescentParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(StochasticGradientDescentParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StochasticGradientDescentParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StochasticGradientDescentParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StochasticGradientDescentParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const StochasticGradientDescentParameters& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const StochasticGradientDescentParameters& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StochasticGradientDescentParameters";
  }
  protected:
  explicit StochasticGradientDescentParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StochasticGradientDescentParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class FtrlParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.FtrlParameters) */ {
 public:
  inline FtrlParameters() : FtrlParameters(nullptr) {}
  ~FtrlParameters() override;
  explicit PROTOBUF_CONSTEXPR FtrlParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FtrlParameters(const FtrlParameters& from);
  FtrlParameters(FtrlParameters&& from) noexcept
    : FtrlParameters() {
    *this = ::std::move(from);
  }

  inline FtrlParameters& operator=(const FtrlParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline FtrlParameters& operator=(FtrlParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FtrlParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const FtrlParameters* internal_default_instance() {
    return reinterpret_cast<const FtrlParameters*>(
               &_FtrlParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(FtrlParameters& a, FtrlParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(FtrlParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FtrlParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FtrlParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FtrlParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FtrlParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FtrlParameters& from) {
    FtrlParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FtrlParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.FtrlParameters";
  }
  protected:
  explicit FtrlParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
    kLrPowerFieldNumber = 3,
    kBetaFieldNumber = 7,
    kMultiplyLinearByLrFieldNumber = 6,
    kAllowZeroAccumulatorFieldNumber = 8,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);
  private:
  float _internal_l1() const;
  void _internal_set_l1(float value);
  public:

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);
  private:
  float _internal_l2() const;
  void _internal_set_l2(float value);
  public:

  // float lr_power = 3;
  void clear_lr_power();
  float lr_power() const;
  void set_lr_power(float value);
  private:
  float _internal_lr_power() const;
  void _internal_set_lr_power(float value);
  public:

  // float beta = 7;
  void clear_beta();
  float beta() const;
  void set_beta(float value);
  private:
  float _internal_beta() const;
  void _internal_set_beta(float value);
  public:

  // bool multiply_linear_by_lr = 6;
  void clear_multiply_linear_by_lr();
  bool multiply_linear_by_lr() const;
  void set_multiply_linear_by_lr(bool value);
  private:
  bool _internal_multiply_linear_by_lr() const;
  void _internal_set_multiply_linear_by_lr(bool value);
  public:

  // bool allow_zero_accumulator = 8 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_allow_zero_accumulator();
  PROTOBUF_DEPRECATED bool allow_zero_accumulator() const;
  PROTOBUF_DEPRECATED void set_allow_zero_accumulator(bool value);
  private:
  bool _internal_allow_zero_accumulator() const;
  void _internal_set_allow_zero_accumulator(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.FtrlParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float l1_;
    float l2_;
    float lr_power_;
    float beta_;
    bool multiply_linear_by_lr_;
    bool allow_zero_accumulator_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdamParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdamParameters) */ {
 public:
  inline AdamParameters() : AdamParameters(nullptr) {}
  ~AdamParameters() override;
  explicit PROTOBUF_CONSTEXPR AdamParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdamParameters(const AdamParameters& from);
  AdamParameters(AdamParameters&& from) noexcept
    : AdamParameters() {
    *this = ::std::move(from);
  }

  inline AdamParameters& operator=(const AdamParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdamParameters& operator=(AdamParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdamParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdamParameters* internal_default_instance() {
    return reinterpret_cast<const AdamParameters*>(
               &_AdamParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(AdamParameters& a, AdamParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdamParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdamParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdamParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdamParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdamParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdamParameters& from) {
    AdamParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdamParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdamParameters";
  }
  protected:
  explicit AdamParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBeta1FieldNumber = 3,
    kBeta2FieldNumber = 4,
    kEpsilonFieldNumber = 5,
    kUseNonLazyAdamFieldNumber = 8,
    kUseSumInsideSqrtFieldNumber = 10,
  };
  // float beta1 = 3;
  void clear_beta1();
  float beta1() const;
  void set_beta1(float value);
  private:
  float _internal_beta1() const;
  void _internal_set_beta1(float value);
  public:

  // float beta2 = 4;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);
  private:
  float _internal_beta2() const;
  void _internal_set_beta2(float value);
  public:

  // float epsilon = 5;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);
  private:
  float _internal_epsilon() const;
  void _internal_set_epsilon(float value);
  public:

  // bool use_non_lazy_adam = 8;
  void clear_use_non_lazy_adam();
  bool use_non_lazy_adam() const;
  void set_use_non_lazy_adam(bool value);
  private:
  bool _internal_use_non_lazy_adam() const;
  void _internal_set_use_non_lazy_adam(bool value);
  public:

  // bool use_sum_inside_sqrt = 10;
  void clear_use_sum_inside_sqrt();
  bool use_sum_inside_sqrt() const;
  void set_use_sum_inside_sqrt(bool value);
  private:
  bool _internal_use_sum_inside_sqrt() const;
  void _internal_set_use_sum_inside_sqrt(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdamParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float beta1_;
    float beta2_;
    float epsilon_;
    bool use_non_lazy_adam_;
    bool use_sum_inside_sqrt_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class MomentumParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.MomentumParameters) */ {
 public:
  inline MomentumParameters() : MomentumParameters(nullptr) {}
  ~MomentumParameters() override;
  explicit PROTOBUF_CONSTEXPR MomentumParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MomentumParameters(const MomentumParameters& from);
  MomentumParameters(MomentumParameters&& from) noexcept
    : MomentumParameters() {
    *this = ::std::move(from);
  }

  inline MomentumParameters& operator=(const MomentumParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline MomentumParameters& operator=(MomentumParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MomentumParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const MomentumParameters* internal_default_instance() {
    return reinterpret_cast<const MomentumParameters*>(
               &_MomentumParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(MomentumParameters& a, MomentumParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(MomentumParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MomentumParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MomentumParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MomentumParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MomentumParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MomentumParameters& from) {
    MomentumParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MomentumParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.MomentumParameters";
  }
  protected:
  explicit MomentumParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMomentumFieldNumber = 1,
    kUseNesterovFieldNumber = 2,
  };
  // float momentum = 1;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);
  private:
  float _internal_momentum() const;
  void _internal_set_momentum(float value);
  public:

  // bool use_nesterov = 2;
  void clear_use_nesterov();
  bool use_nesterov() const;
  void set_use_nesterov(bool value);
  private:
  bool _internal_use_nesterov() const;
  void _internal_set_use_nesterov(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.MomentumParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float momentum_;
    bool use_nesterov_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class LionParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.LionParameters) */ {
 public:
  inline LionParameters() : LionParameters(nullptr) {}
  ~LionParameters() override;
  explicit PROTOBUF_CONSTEXPR LionParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LionParameters(const LionParameters& from);
  LionParameters(LionParameters&& from) noexcept
    : LionParameters() {
    *this = ::std::move(from);
  }

  inline LionParameters& operator=(const LionParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline LionParameters& operator=(LionParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LionParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const LionParameters* internal_default_instance() {
    return reinterpret_cast<const LionParameters*>(
               &_LionParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(LionParameters& a, LionParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(LionParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LionParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LionParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LionParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LionParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LionParameters& from) {
    LionParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LionParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.LionParameters";
  }
  protected:
  explicit LionParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBeta1FieldNumber = 1,
    kBeta2FieldNumber = 2,
    kUseNonLazyLionFieldNumber = 3,
  };
  // float beta1 = 1;
  void clear_beta1();
  float beta1() const;
  void set_beta1(float value);
  private:
  float _internal_beta1() const;
  void _internal_set_beta1(float value);
  public:

  // float beta2 = 2;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);
  private:
  float _internal_beta2() const;
  void _internal_set_beta2(float value);
  public:

  // bool use_non_lazy_lion = 3;
  void clear_use_non_lazy_lion();
  bool use_non_lazy_lion() const;
  void set_use_non_lazy_lion(bool value);
  private:
  bool _internal_use_non_lazy_lion() const;
  void _internal_set_use_non_lazy_lion(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.LionParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float beta1_;
    float beta2_;
    bool use_non_lazy_lion_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class RmsPropParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.RmsPropParameters) */ {
 public:
  inline RmsPropParameters() : RmsPropParameters(nullptr) {}
  ~RmsPropParameters() override;
  explicit PROTOBUF_CONSTEXPR RmsPropParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RmsPropParameters(const RmsPropParameters& from);
  RmsPropParameters(RmsPropParameters&& from) noexcept
    : RmsPropParameters() {
    *this = ::std::move(from);
  }

  inline RmsPropParameters& operator=(const RmsPropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline RmsPropParameters& operator=(RmsPropParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RmsPropParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const RmsPropParameters* internal_default_instance() {
    return reinterpret_cast<const RmsPropParameters*>(
               &_RmsPropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(RmsPropParameters& a, RmsPropParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(RmsPropParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RmsPropParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RmsPropParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RmsPropParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RmsPropParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const RmsPropParameters& from) {
    RmsPropParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RmsPropParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.RmsPropParameters";
  }
  protected:
  explicit RmsPropParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRhoFieldNumber = 1,
    kMomentumFieldNumber = 2,
    kEpsilonFieldNumber = 3,
  };
  // float rho = 1;
  void clear_rho();
  float rho() const;
  void set_rho(float value);
  private:
  float _internal_rho() const;
  void _internal_set_rho(float value);
  public:

  // float momentum = 2;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);
  private:
  float _internal_momentum() const;
  void _internal_set_momentum(float value);
  public:

  // float epsilon = 3;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);
  private:
  float _internal_epsilon() const;
  void _internal_set_epsilon(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.RmsPropParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float rho_;
    float momentum_;
    float epsilon_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class CenteredRmsPropParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.CenteredRmsPropParameters) */ {
 public:
  inline CenteredRmsPropParameters() : CenteredRmsPropParameters(nullptr) {}
  ~CenteredRmsPropParameters() override;
  explicit PROTOBUF_CONSTEXPR CenteredRmsPropParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CenteredRmsPropParameters(const CenteredRmsPropParameters& from);
  CenteredRmsPropParameters(CenteredRmsPropParameters&& from) noexcept
    : CenteredRmsPropParameters() {
    *this = ::std::move(from);
  }

  inline CenteredRmsPropParameters& operator=(const CenteredRmsPropParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline CenteredRmsPropParameters& operator=(CenteredRmsPropParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CenteredRmsPropParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const CenteredRmsPropParameters* internal_default_instance() {
    return reinterpret_cast<const CenteredRmsPropParameters*>(
               &_CenteredRmsPropParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(CenteredRmsPropParameters& a, CenteredRmsPropParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(CenteredRmsPropParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CenteredRmsPropParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CenteredRmsPropParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CenteredRmsPropParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CenteredRmsPropParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const CenteredRmsPropParameters& from) {
    CenteredRmsPropParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CenteredRmsPropParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.CenteredRmsPropParameters";
  }
  protected:
  explicit CenteredRmsPropParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRhoFieldNumber = 1,
    kMomentumFieldNumber = 2,
    kEpsilonFieldNumber = 3,
  };
  // float rho = 1;
  void clear_rho();
  float rho() const;
  void set_rho(float value);
  private:
  float _internal_rho() const;
  void _internal_set_rho(float value);
  public:

  // float momentum = 2;
  void clear_momentum();
  float momentum() const;
  void set_momentum(float value);
  private:
  float _internal_momentum() const;
  void _internal_set_momentum(float value);
  public:

  // float epsilon = 3;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);
  private:
  float _internal_epsilon() const;
  void _internal_set_epsilon(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.CenteredRmsPropParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float rho_;
    float momentum_;
    float epsilon_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class MdlAdagradLightParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.MdlAdagradLightParameters) */ {
 public:
  inline MdlAdagradLightParameters() : MdlAdagradLightParameters(nullptr) {}
  ~MdlAdagradLightParameters() override;
  explicit PROTOBUF_CONSTEXPR MdlAdagradLightParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MdlAdagradLightParameters(const MdlAdagradLightParameters& from);
  MdlAdagradLightParameters(MdlAdagradLightParameters&& from) noexcept
    : MdlAdagradLightParameters() {
    *this = ::std::move(from);
  }

  inline MdlAdagradLightParameters& operator=(const MdlAdagradLightParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline MdlAdagradLightParameters& operator=(MdlAdagradLightParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MdlAdagradLightParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const MdlAdagradLightParameters* internal_default_instance() {
    return reinterpret_cast<const MdlAdagradLightParameters*>(
               &_MdlAdagradLightParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(MdlAdagradLightParameters& a, MdlAdagradLightParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(MdlAdagradLightParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MdlAdagradLightParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MdlAdagradLightParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MdlAdagradLightParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MdlAdagradLightParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const MdlAdagradLightParameters& from) {
    MdlAdagradLightParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MdlAdagradLightParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.MdlAdagradLightParameters";
  }
  protected:
  explicit MdlAdagradLightParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL2FieldNumber = 1,
    kLrPowerFieldNumber = 2,
    kMinServableMdlBenefitFieldNumber = 3,
    kMdlMixInMarginFieldNumber = 4,
    kMdlBenefitRampupCoeffFieldNumber = 5,
    kMdlMinWeightFieldNumber = 6,
    kBenefitRevisitScaleFieldNumber = 7,
    kMaxEventBenefitFieldNumber = 8,
    kMaxTotalBenefitFieldNumber = 9,
    kMdlHardLimitFieldNumber = 10,
    kHardLimitMinBenefitFieldNumber = 11,
    kMdlRegularizeFieldNumber = 12,
  };
  // float l2 = 1;
  void clear_l2();
  float l2() const;
  void set_l2(float value);
  private:
  float _internal_l2() const;
  void _internal_set_l2(float value);
  public:

  // float lr_power = 2;
  void clear_lr_power();
  float lr_power() const;
  void set_lr_power(float value);
  private:
  float _internal_lr_power() const;
  void _internal_set_lr_power(float value);
  public:

  // float min_servable_mdl_benefit = 3;
  void clear_min_servable_mdl_benefit();
  float min_servable_mdl_benefit() const;
  void set_min_servable_mdl_benefit(float value);
  private:
  float _internal_min_servable_mdl_benefit() const;
  void _internal_set_min_servable_mdl_benefit(float value);
  public:

  // float mdl_mix_in_margin = 4;
  void clear_mdl_mix_in_margin();
  float mdl_mix_in_margin() const;
  void set_mdl_mix_in_margin(float value);
  private:
  float _internal_mdl_mix_in_margin() const;
  void _internal_set_mdl_mix_in_margin(float value);
  public:

  // float mdl_benefit_rampup_coeff = 5;
  void clear_mdl_benefit_rampup_coeff();
  float mdl_benefit_rampup_coeff() const;
  void set_mdl_benefit_rampup_coeff(float value);
  private:
  float _internal_mdl_benefit_rampup_coeff() const;
  void _internal_set_mdl_benefit_rampup_coeff(float value);
  public:

  // float mdl_min_weight = 6;
  void clear_mdl_min_weight();
  float mdl_min_weight() const;
  void set_mdl_min_weight(float value);
  private:
  float _internal_mdl_min_weight() const;
  void _internal_set_mdl_min_weight(float value);
  public:

  // float benefit_revisit_scale = 7;
  void clear_benefit_revisit_scale();
  float benefit_revisit_scale() const;
  void set_benefit_revisit_scale(float value);
  private:
  float _internal_benefit_revisit_scale() const;
  void _internal_set_benefit_revisit_scale(float value);
  public:

  // float max_event_benefit = 8;
  void clear_max_event_benefit();
  float max_event_benefit() const;
  void set_max_event_benefit(float value);
  private:
  float _internal_max_event_benefit() const;
  void _internal_set_max_event_benefit(float value);
  public:

  // float max_total_benefit = 9;
  void clear_max_total_benefit();
  float max_total_benefit() const;
  void set_max_total_benefit(float value);
  private:
  float _internal_max_total_benefit() const;
  void _internal_set_max_total_benefit(float value);
  public:

  // float mdl_hard_limit = 10;
  void clear_mdl_hard_limit();
  float mdl_hard_limit() const;
  void set_mdl_hard_limit(float value);
  private:
  float _internal_mdl_hard_limit() const;
  void _internal_set_mdl_hard_limit(float value);
  public:

  // bool hard_limit_min_benefit = 11;
  void clear_hard_limit_min_benefit();
  bool hard_limit_min_benefit() const;
  void set_hard_limit_min_benefit(bool value);
  private:
  bool _internal_hard_limit_min_benefit() const;
  void _internal_set_hard_limit_min_benefit(bool value);
  public:

  // bool mdl_regularize = 12;
  void clear_mdl_regularize();
  bool mdl_regularize() const;
  void set_mdl_regularize(bool value);
  private:
  bool _internal_mdl_regularize() const;
  void _internal_set_mdl_regularize(bool value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.MdlAdagradLightParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float l2_;
    float lr_power_;
    float min_servable_mdl_benefit_;
    float mdl_mix_in_margin_;
    float mdl_benefit_rampup_coeff_;
    float mdl_min_weight_;
    float benefit_revisit_scale_;
    float max_event_benefit_;
    float max_total_benefit_;
    float mdl_hard_limit_;
    bool hard_limit_min_benefit_;
    bool mdl_regularize_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AdadeltaParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AdadeltaParameters) */ {
 public:
  inline AdadeltaParameters() : AdadeltaParameters(nullptr) {}
  ~AdadeltaParameters() override;
  explicit PROTOBUF_CONSTEXPR AdadeltaParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AdadeltaParameters(const AdadeltaParameters& from);
  AdadeltaParameters(AdadeltaParameters&& from) noexcept
    : AdadeltaParameters() {
    *this = ::std::move(from);
  }

  inline AdadeltaParameters& operator=(const AdadeltaParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AdadeltaParameters& operator=(AdadeltaParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AdadeltaParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const AdadeltaParameters* internal_default_instance() {
    return reinterpret_cast<const AdadeltaParameters*>(
               &_AdadeltaParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(AdadeltaParameters& a, AdadeltaParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AdadeltaParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AdadeltaParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AdadeltaParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AdadeltaParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AdadeltaParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const AdadeltaParameters& from) {
    AdadeltaParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AdadeltaParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AdadeltaParameters";
  }
  protected:
  explicit AdadeltaParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRhoFieldNumber = 1,
    kEpsilonFieldNumber = 2,
  };
  // float rho = 1;
  void clear_rho();
  float rho() const;
  void set_rho(float value);
  private:
  float _internal_rho() const;
  void _internal_set_rho(float value);
  public:

  // float epsilon = 2;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);
  private:
  float _internal_epsilon() const;
  void _internal_set_epsilon(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AdadeltaParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float rho_;
    float epsilon_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class ProximalAdagradParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ProximalAdagradParameters) */ {
 public:
  inline ProximalAdagradParameters() : ProximalAdagradParameters(nullptr) {}
  ~ProximalAdagradParameters() override;
  explicit PROTOBUF_CONSTEXPR ProximalAdagradParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProximalAdagradParameters(const ProximalAdagradParameters& from);
  ProximalAdagradParameters(ProximalAdagradParameters&& from) noexcept
    : ProximalAdagradParameters() {
    *this = ::std::move(from);
  }

  inline ProximalAdagradParameters& operator=(const ProximalAdagradParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProximalAdagradParameters& operator=(ProximalAdagradParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProximalAdagradParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProximalAdagradParameters* internal_default_instance() {
    return reinterpret_cast<const ProximalAdagradParameters*>(
               &_ProximalAdagradParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(ProximalAdagradParameters& a, ProximalAdagradParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(ProximalAdagradParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProximalAdagradParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProximalAdagradParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProximalAdagradParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProximalAdagradParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProximalAdagradParameters& from) {
    ProximalAdagradParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProximalAdagradParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.ProximalAdagradParameters";
  }
  protected:
  explicit ProximalAdagradParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);
  private:
  float _internal_l1() const;
  void _internal_set_l1(float value);
  public:

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);
  private:
  float _internal_l2() const;
  void _internal_set_l2(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalAdagradParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float l1_;
    float l2_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class OnlineYogiParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.OnlineYogiParameters) */ {
 public:
  inline OnlineYogiParameters() : OnlineYogiParameters(nullptr) {}
  ~OnlineYogiParameters() override;
  explicit PROTOBUF_CONSTEXPR OnlineYogiParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OnlineYogiParameters(const OnlineYogiParameters& from);
  OnlineYogiParameters(OnlineYogiParameters&& from) noexcept
    : OnlineYogiParameters() {
    *this = ::std::move(from);
  }

  inline OnlineYogiParameters& operator=(const OnlineYogiParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline OnlineYogiParameters& operator=(OnlineYogiParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OnlineYogiParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const OnlineYogiParameters* internal_default_instance() {
    return reinterpret_cast<const OnlineYogiParameters*>(
               &_OnlineYogiParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(OnlineYogiParameters& a, OnlineYogiParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(OnlineYogiParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OnlineYogiParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OnlineYogiParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OnlineYogiParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OnlineYogiParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OnlineYogiParameters& from) {
    OnlineYogiParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OnlineYogiParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.OnlineYogiParameters";
  }
  protected:
  explicit OnlineYogiParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
    kBeta2FieldNumber = 3,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);
  private:
  float _internal_l1() const;
  void _internal_set_l1(float value);
  public:

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);
  private:
  float _internal_l2() const;
  void _internal_set_l2(float value);
  public:

  // float beta2 = 3;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);
  private:
  float _internal_beta2() const;
  void _internal_set_beta2(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.OnlineYogiParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float l1_;
    float l2_;
    float beta2_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class ProximalYogiParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.ProximalYogiParameters) */ {
 public:
  inline ProximalYogiParameters() : ProximalYogiParameters(nullptr) {}
  ~ProximalYogiParameters() override;
  explicit PROTOBUF_CONSTEXPR ProximalYogiParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProximalYogiParameters(const ProximalYogiParameters& from);
  ProximalYogiParameters(ProximalYogiParameters&& from) noexcept
    : ProximalYogiParameters() {
    *this = ::std::move(from);
  }

  inline ProximalYogiParameters& operator=(const ProximalYogiParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProximalYogiParameters& operator=(ProximalYogiParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProximalYogiParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProximalYogiParameters* internal_default_instance() {
    return reinterpret_cast<const ProximalYogiParameters*>(
               &_ProximalYogiParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ProximalYogiParameters& a, ProximalYogiParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(ProximalYogiParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProximalYogiParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProximalYogiParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProximalYogiParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProximalYogiParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ProximalYogiParameters& from) {
    ProximalYogiParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProximalYogiParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.ProximalYogiParameters";
  }
  protected:
  explicit ProximalYogiParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kL1FieldNumber = 1,
    kL2FieldNumber = 2,
    kBeta1FieldNumber = 3,
    kBeta2FieldNumber = 4,
    kEpsilonFieldNumber = 5,
  };
  // float l1 = 1;
  void clear_l1();
  float l1() const;
  void set_l1(float value);
  private:
  float _internal_l1() const;
  void _internal_set_l1(float value);
  public:

  // float l2 = 2;
  void clear_l2();
  float l2() const;
  void set_l2(float value);
  private:
  float _internal_l2() const;
  void _internal_set_l2(float value);
  public:

  // float beta1 = 3;
  void clear_beta1();
  float beta1() const;
  void set_beta1(float value);
  private:
  float _internal_beta1() const;
  void _internal_set_beta1(float value);
  public:

  // float beta2 = 4;
  void clear_beta2();
  float beta2() const;
  void set_beta2(float value);
  private:
  float _internal_beta2() const;
  void _internal_set_beta2(float value);
  public:

  // float epsilon = 5;
  void clear_epsilon();
  float epsilon() const;
  void set_epsilon(float value);
  private:
  float _internal_epsilon() const;
  void _internal_set_epsilon(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.ProximalYogiParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float l1_;
    float l2_;
    float beta1_;
    float beta2_;
    float epsilon_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class FrequencyEstimatorParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.FrequencyEstimatorParameters) */ {
 public:
  inline FrequencyEstimatorParameters() : FrequencyEstimatorParameters(nullptr) {}
  ~FrequencyEstimatorParameters() override;
  explicit PROTOBUF_CONSTEXPR FrequencyEstimatorParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FrequencyEstimatorParameters(const FrequencyEstimatorParameters& from);
  FrequencyEstimatorParameters(FrequencyEstimatorParameters&& from) noexcept
    : FrequencyEstimatorParameters() {
    *this = ::std::move(from);
  }

  inline FrequencyEstimatorParameters& operator=(const FrequencyEstimatorParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline FrequencyEstimatorParameters& operator=(FrequencyEstimatorParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FrequencyEstimatorParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const FrequencyEstimatorParameters* internal_default_instance() {
    return reinterpret_cast<const FrequencyEstimatorParameters*>(
               &_FrequencyEstimatorParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(FrequencyEstimatorParameters& a, FrequencyEstimatorParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(FrequencyEstimatorParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FrequencyEstimatorParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FrequencyEstimatorParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FrequencyEstimatorParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FrequencyEstimatorParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const FrequencyEstimatorParameters& from) {
    FrequencyEstimatorParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FrequencyEstimatorParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.FrequencyEstimatorParameters";
  }
  protected:
  explicit FrequencyEstimatorParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTauFieldNumber = 1,
    kMaxDeltaFieldNumber = 2,
    kOutlierThresholdFieldNumber = 3,
    kWeightExponentFieldNumber = 4,
  };
  // float tau = 1;
  void clear_tau();
  float tau() const;
  void set_tau(float value);
  private:
  float _internal_tau() const;
  void _internal_set_tau(float value);
  public:

  // float max_delta = 2;
  void clear_max_delta();
  float max_delta() const;
  void set_max_delta(float value);
  private:
  float _internal_max_delta() const;
  void _internal_set_max_delta(float value);
  public:

  // float outlier_threshold = 3;
  void clear_outlier_threshold();
  float outlier_threshold() const;
  void set_outlier_threshold(float value);
  private:
  float _internal_outlier_threshold() const;
  void _internal_set_outlier_threshold(float value);
  public:

  // float weight_exponent = 4;
  void clear_weight_exponent();
  float weight_exponent() const;
  void set_weight_exponent(float value);
  private:
  float _internal_weight_exponent() const;
  void _internal_set_weight_exponent(float value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.FrequencyEstimatorParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float tau_;
    float max_delta_;
    float outlier_threshold_;
    float weight_exponent_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class UserDefinedProgramParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.UserDefinedProgramParameters) */ {
 public:
  inline UserDefinedProgramParameters() : UserDefinedProgramParameters(nullptr) {}
  ~UserDefinedProgramParameters() override;
  explicit PROTOBUF_CONSTEXPR UserDefinedProgramParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UserDefinedProgramParameters(const UserDefinedProgramParameters& from);
  UserDefinedProgramParameters(UserDefinedProgramParameters&& from) noexcept
    : UserDefinedProgramParameters() {
    *this = ::std::move(from);
  }

  inline UserDefinedProgramParameters& operator=(const UserDefinedProgramParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline UserDefinedProgramParameters& operator=(UserDefinedProgramParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UserDefinedProgramParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const UserDefinedProgramParameters* internal_default_instance() {
    return reinterpret_cast<const UserDefinedProgramParameters*>(
               &_UserDefinedProgramParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(UserDefinedProgramParameters& a, UserDefinedProgramParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(UserDefinedProgramParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UserDefinedProgramParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UserDefinedProgramParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UserDefinedProgramParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UserDefinedProgramParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const UserDefinedProgramParameters& from) {
    UserDefinedProgramParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UserDefinedProgramParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.UserDefinedProgramParameters";
  }
  protected:
  explicit UserDefinedProgramParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kProgramFieldNumber = 1,
  };
  // .xla.HloModuleProto program = 1;
  bool has_program() const;
  private:
  bool _internal_has_program() const;
  public:
  void clear_program();
  const ::xla::HloModuleProto& program() const;
  PROTOBUF_NODISCARD ::xla::HloModuleProto* release_program();
  ::xla::HloModuleProto* mutable_program();
  void set_allocated_program(::xla::HloModuleProto* program);
  private:
  const ::xla::HloModuleProto& _internal_program() const;
  ::xla::HloModuleProto* _internal_mutable_program();
  public:
  void unsafe_arena_set_allocated_program(
      ::xla::HloModuleProto* program);
  ::xla::HloModuleProto* unsafe_arena_release_program();

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.UserDefinedProgramParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::xla::HloModuleProto* program_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class AssignParameters final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.AssignParameters) */ {
 public:
  inline AssignParameters() : AssignParameters(nullptr) {}
  explicit PROTOBUF_CONSTEXPR AssignParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AssignParameters(const AssignParameters& from);
  AssignParameters(AssignParameters&& from) noexcept
    : AssignParameters() {
    *this = ::std::move(from);
  }

  inline AssignParameters& operator=(const AssignParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline AssignParameters& operator=(AssignParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AssignParameters& default_instance() {
    return *internal_default_instance();
  }
  static inline const AssignParameters* internal_default_instance() {
    return reinterpret_cast<const AssignParameters*>(
               &_AssignParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(AssignParameters& a, AssignParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(AssignParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AssignParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AssignParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AssignParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const AssignParameters& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const AssignParameters& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.AssignParameters";
  }
  protected:
  explicit AssignParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.AssignParameters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class GradientAccumulationStatus final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.GradientAccumulationStatus) */ {
 public:
  inline GradientAccumulationStatus() : GradientAccumulationStatus(nullptr) {}
  explicit PROTOBUF_CONSTEXPR GradientAccumulationStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GradientAccumulationStatus(const GradientAccumulationStatus& from);
  GradientAccumulationStatus(GradientAccumulationStatus&& from) noexcept
    : GradientAccumulationStatus() {
    *this = ::std::move(from);
  }

  inline GradientAccumulationStatus& operator=(const GradientAccumulationStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline GradientAccumulationStatus& operator=(GradientAccumulationStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GradientAccumulationStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const GradientAccumulationStatus* internal_default_instance() {
    return reinterpret_cast<const GradientAccumulationStatus*>(
               &_GradientAccumulationStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(GradientAccumulationStatus& a, GradientAccumulationStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(GradientAccumulationStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GradientAccumulationStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GradientAccumulationStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GradientAccumulationStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const GradientAccumulationStatus& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const GradientAccumulationStatus& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.GradientAccumulationStatus";
  }
  protected:
  explicit GradientAccumulationStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef GradientAccumulationStatus_Status Status;
  static constexpr Status UNSPECIFIED =
    GradientAccumulationStatus_Status_UNSPECIFIED;
  static constexpr Status ENABLED =
    GradientAccumulationStatus_Status_ENABLED;
  static constexpr Status DISABLED =
    GradientAccumulationStatus_Status_DISABLED;
  static inline bool Status_IsValid(int value) {
    return GradientAccumulationStatus_Status_IsValid(value);
  }
  static constexpr Status Status_MIN =
    GradientAccumulationStatus_Status_Status_MIN;
  static constexpr Status Status_MAX =
    GradientAccumulationStatus_Status_Status_MAX;
  static constexpr int Status_ARRAYSIZE =
    GradientAccumulationStatus_Status_Status_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Status_descriptor() {
    return GradientAccumulationStatus_Status_descriptor();
  }
  template<typename T>
  static inline const std::string& Status_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Status>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Status_Name.");
    return GradientAccumulationStatus_Status_Name(enum_t_value);
  }
  static inline bool Status_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Status* value) {
    return GradientAccumulationStatus_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.GradientAccumulationStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class LowDimensionalPackingStatus final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.LowDimensionalPackingStatus) */ {
 public:
  inline LowDimensionalPackingStatus() : LowDimensionalPackingStatus(nullptr) {}
  explicit PROTOBUF_CONSTEXPR LowDimensionalPackingStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LowDimensionalPackingStatus(const LowDimensionalPackingStatus& from);
  LowDimensionalPackingStatus(LowDimensionalPackingStatus&& from) noexcept
    : LowDimensionalPackingStatus() {
    *this = ::std::move(from);
  }

  inline LowDimensionalPackingStatus& operator=(const LowDimensionalPackingStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline LowDimensionalPackingStatus& operator=(LowDimensionalPackingStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LowDimensionalPackingStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const LowDimensionalPackingStatus* internal_default_instance() {
    return reinterpret_cast<const LowDimensionalPackingStatus*>(
               &_LowDimensionalPackingStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(LowDimensionalPackingStatus& a, LowDimensionalPackingStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(LowDimensionalPackingStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LowDimensionalPackingStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LowDimensionalPackingStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LowDimensionalPackingStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const LowDimensionalPackingStatus& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const LowDimensionalPackingStatus& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.LowDimensionalPackingStatus";
  }
  protected:
  explicit LowDimensionalPackingStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef LowDimensionalPackingStatus_Status Status;
  static constexpr Status UNSPECIFIED =
    LowDimensionalPackingStatus_Status_UNSPECIFIED;
  static constexpr Status ENABLED =
    LowDimensionalPackingStatus_Status_ENABLED;
  static constexpr Status DISABLED =
    LowDimensionalPackingStatus_Status_DISABLED;
  static inline bool Status_IsValid(int value) {
    return LowDimensionalPackingStatus_Status_IsValid(value);
  }
  static constexpr Status Status_MIN =
    LowDimensionalPackingStatus_Status_Status_MIN;
  static constexpr Status Status_MAX =
    LowDimensionalPackingStatus_Status_Status_MAX;
  static constexpr int Status_ARRAYSIZE =
    LowDimensionalPackingStatus_Status_Status_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Status_descriptor() {
    return LowDimensionalPackingStatus_Status_descriptor();
  }
  template<typename T>
  static inline const std::string& Status_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Status>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Status_Name.");
    return LowDimensionalPackingStatus_Status_Name(enum_t_value);
  }
  static inline bool Status_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Status* value) {
    return LowDimensionalPackingStatus_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.LowDimensionalPackingStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class HotIdReplicationConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.HotIdReplicationConfiguration) */ {
 public:
  inline HotIdReplicationConfiguration() : HotIdReplicationConfiguration(nullptr) {}
  ~HotIdReplicationConfiguration() override;
  explicit PROTOBUF_CONSTEXPR HotIdReplicationConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HotIdReplicationConfiguration(const HotIdReplicationConfiguration& from);
  HotIdReplicationConfiguration(HotIdReplicationConfiguration&& from) noexcept
    : HotIdReplicationConfiguration() {
    *this = ::std::move(from);
  }

  inline HotIdReplicationConfiguration& operator=(const HotIdReplicationConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline HotIdReplicationConfiguration& operator=(HotIdReplicationConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HotIdReplicationConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const HotIdReplicationConfiguration* internal_default_instance() {
    return reinterpret_cast<const HotIdReplicationConfiguration*>(
               &_HotIdReplicationConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(HotIdReplicationConfiguration& a, HotIdReplicationConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(HotIdReplicationConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HotIdReplicationConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HotIdReplicationConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HotIdReplicationConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HotIdReplicationConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const HotIdReplicationConfiguration& from) {
    HotIdReplicationConfiguration::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HotIdReplicationConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.HotIdReplicationConfiguration";
  }
  protected:
  explicit HotIdReplicationConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef HotIdReplicationConfiguration_Status Status;
  static constexpr Status UNSPECIFIED =
    HotIdReplicationConfiguration_Status_UNSPECIFIED;
  static constexpr Status ENABLED =
    HotIdReplicationConfiguration_Status_ENABLED;
  static constexpr Status DISABLED =
    HotIdReplicationConfiguration_Status_DISABLED;
  static constexpr Status MIGRATION_ONLY =
    HotIdReplicationConfiguration_Status_MIGRATION_ONLY;
  static inline bool Status_IsValid(int value) {
    return HotIdReplicationConfiguration_Status_IsValid(value);
  }
  static constexpr Status Status_MIN =
    HotIdReplicationConfiguration_Status_Status_MIN;
  static constexpr Status Status_MAX =
    HotIdReplicationConfiguration_Status_Status_MAX;
  static constexpr int Status_ARRAYSIZE =
    HotIdReplicationConfiguration_Status_Status_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Status_descriptor() {
    return HotIdReplicationConfiguration_Status_descriptor();
  }
  template<typename T>
  static inline const std::string& Status_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Status>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Status_Name.");
    return HotIdReplicationConfiguration_Status_Name(enum_t_value);
  }
  static inline bool Status_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Status* value) {
    return HotIdReplicationConfiguration_Status_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kStatusFieldNumber = 1,
  };
  // .tensorflow.tpu.HotIdReplicationConfiguration.Status status = 1;
  void clear_status();
  ::tensorflow::tpu::HotIdReplicationConfiguration_Status status() const;
  void set_status(::tensorflow::tpu::HotIdReplicationConfiguration_Status value);
  private:
  ::tensorflow::tpu::HotIdReplicationConfiguration_Status _internal_status() const;
  void _internal_set_status(::tensorflow::tpu::HotIdReplicationConfiguration_Status value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.HotIdReplicationConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    int status_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class OptimizationParameters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.OptimizationParameters) */ {
 public:
  inline OptimizationParameters() : OptimizationParameters(nullptr) {}
  ~OptimizationParameters() override;
  explicit PROTOBUF_CONSTEXPR OptimizationParameters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  OptimizationParameters(const OptimizationParameters& from);
  OptimizationParameters(OptimizationParameters&& from) noexcept
    : OptimizationParameters() {
    *this = ::std::move(from);
  }

  inline OptimizationParameters& operator=(const OptimizationParameters& from) {
    CopyFrom(from);
    return *this;
  }
  inline OptimizationParameters& operator=(OptimizationParameters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const OptimizationParameters& default_instance() {
    return *internal_default_instance();
  }
  enum ParametersCase {
    kAdagrad = 3,
    kAdagradMomentum = 26,
    kBoundedAdagrad = 19,
    kStochasticGradientDescent = 4,
    kFtrl = 5,
    kAdam = 6,
    kMomentum = 8,
    kLion = 29,
    kRmsProp = 9,
    kCenteredRmsProp = 10,
    kMdlAdagradLight = 11,
    kAdadelta = 12,
    kProximalAdagrad = 14,
    kOnlineYogi = 20,
    kProximalYogi = 21,
    kFrequencyEstimator = 23,
    kUserDefinedProgram = 24,
    kAssign = 25,
    PARAMETERS_NOT_SET = 0,
  };

  static inline const OptimizationParameters* internal_default_instance() {
    return reinterpret_cast<const OptimizationParameters*>(
               &_OptimizationParameters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(OptimizationParameters& a, OptimizationParameters& b) {
    a.Swap(&b);
  }
  inline void Swap(OptimizationParameters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(OptimizationParameters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  OptimizationParameters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<OptimizationParameters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const OptimizationParameters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const OptimizationParameters& from) {
    OptimizationParameters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(OptimizationParameters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.OptimizationParameters";
  }
  protected:
  explicit OptimizationParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kClippingLimitsFieldNumber = 2,
    kGradientClippingLimitsFieldNumber = 7,
    kLearningRateFieldNumber = 13,
    kHotIdReplicationConfigurationFieldNumber = 18,
    kSimulatedQuantizationFieldNumber = 27,
    kWeightDecayFactorFieldNumber = 16,
    kGradientAccumulationStatusFieldNumber = 17,
    kMultiplyWeightDecayFactorByLearningRateFieldNumber = 22,
    kLowDimensionalPackingStatusFieldNumber = 28,
    kAdagradFieldNumber = 3,
    kAdagradMomentumFieldNumber = 26,
    kBoundedAdagradFieldNumber = 19,
    kStochasticGradientDescentFieldNumber = 4,
    kFtrlFieldNumber = 5,
    kAdamFieldNumber = 6,
    kMomentumFieldNumber = 8,
    kLionFieldNumber = 29,
    kRmsPropFieldNumber = 9,
    kCenteredRmsPropFieldNumber = 10,
    kMdlAdagradLightFieldNumber = 11,
    kAdadeltaFieldNumber = 12,
    kProximalAdagradFieldNumber = 14,
    kOnlineYogiFieldNumber = 20,
    kProximalYogiFieldNumber = 21,
    kFrequencyEstimatorFieldNumber = 23,
    kUserDefinedProgramFieldNumber = 24,
    kAssignFieldNumber = 25,
  };
  // .tensorflow.tpu.ClippingLimits clipping_limits = 2;
  bool has_clipping_limits() const;
  private:
  bool _internal_has_clipping_limits() const;
  public:
  void clear_clipping_limits();
  const ::tensorflow::tpu::ClippingLimits& clipping_limits() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::ClippingLimits* release_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_clipping_limits();
  void set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits);
  private:
  const ::tensorflow::tpu::ClippingLimits& _internal_clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* _internal_mutable_clipping_limits();
  public:
  void unsafe_arena_set_allocated_clipping_limits(
      ::tensorflow::tpu::ClippingLimits* clipping_limits);
  ::tensorflow::tpu::ClippingLimits* unsafe_arena_release_clipping_limits();

  // .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
  bool has_gradient_clipping_limits() const;
  private:
  bool _internal_has_gradient_clipping_limits() const;
  public:
  void clear_gradient_clipping_limits();
  const ::tensorflow::tpu::ClippingLimits& gradient_clipping_limits() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::ClippingLimits* release_gradient_clipping_limits();
  ::tensorflow::tpu::ClippingLimits* mutable_gradient_clipping_limits();
  void set_allocated_gradient_clipping_limits(::tensorflow::tpu::ClippingLimits* gradient_clipping_limits);
  private:
  const ::tensorflow::tpu::ClippingLimits& _internal_gradient_clipping_limits() const;
  ::tensorflow::tpu::ClippingLimits* _internal_mutable_gradient_clipping_limits();
  public:
  void unsafe_arena_set_allocated_gradient_clipping_limits(
      ::tensorflow::tpu::ClippingLimits* gradient_clipping_limits);
  ::tensorflow::tpu::ClippingLimits* unsafe_arena_release_gradient_clipping_limits();

  // .tensorflow.tpu.LearningRate learning_rate = 13;
  bool has_learning_rate() const;
  private:
  bool _internal_has_learning_rate() const;
  public:
  void clear_learning_rate();
  const ::tensorflow::tpu::LearningRate& learning_rate() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::LearningRate* release_learning_rate();
  ::tensorflow::tpu::LearningRate* mutable_learning_rate();
  void set_allocated_learning_rate(::tensorflow::tpu::LearningRate* learning_rate);
  private:
  const ::tensorflow::tpu::LearningRate& _internal_learning_rate() const;
  ::tensorflow::tpu::LearningRate* _internal_mutable_learning_rate();
  public:
  void unsafe_arena_set_allocated_learning_rate(
      ::tensorflow::tpu::LearningRate* learning_rate);
  ::tensorflow::tpu::LearningRate* unsafe_arena_release_learning_rate();

  // .tensorflow.tpu.HotIdReplicationConfiguration hot_id_replication_configuration = 18;
  bool has_hot_id_replication_configuration() const;
  private:
  bool _internal_has_hot_id_replication_configuration() const;
  public:
  void clear_hot_id_replication_configuration();
  const ::tensorflow::tpu::HotIdReplicationConfiguration& hot_id_replication_configuration() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::HotIdReplicationConfiguration* release_hot_id_replication_configuration();
  ::tensorflow::tpu::HotIdReplicationConfiguration* mutable_hot_id_replication_configuration();
  void set_allocated_hot_id_replication_configuration(::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration);
  private:
  const ::tensorflow::tpu::HotIdReplicationConfiguration& _internal_hot_id_replication_configuration() const;
  ::tensorflow::tpu::HotIdReplicationConfiguration* _internal_mutable_hot_id_replication_configuration();
  public:
  void unsafe_arena_set_allocated_hot_id_replication_configuration(
      ::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration);
  ::tensorflow::tpu::HotIdReplicationConfiguration* unsafe_arena_release_hot_id_replication_configuration();

  // .tensorflow.tpu.SimulatedQuantization simulated_quantization = 27;
  bool has_simulated_quantization() const;
  private:
  bool _internal_has_simulated_quantization() const;
  public:
  void clear_simulated_quantization();
  const ::tensorflow::tpu::SimulatedQuantization& simulated_quantization() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::SimulatedQuantization* release_simulated_quantization();
  ::tensorflow::tpu::SimulatedQuantization* mutable_simulated_quantization();
  void set_allocated_simulated_quantization(::tensorflow::tpu::SimulatedQuantization* simulated_quantization);
  private:
  const ::tensorflow::tpu::SimulatedQuantization& _internal_simulated_quantization() const;
  ::tensorflow::tpu::SimulatedQuantization* _internal_mutable_simulated_quantization();
  public:
  void unsafe_arena_set_allocated_simulated_quantization(
      ::tensorflow::tpu::SimulatedQuantization* simulated_quantization);
  ::tensorflow::tpu::SimulatedQuantization* unsafe_arena_release_simulated_quantization();

  // float weight_decay_factor = 16;
  void clear_weight_decay_factor();
  float weight_decay_factor() const;
  void set_weight_decay_factor(float value);
  private:
  float _internal_weight_decay_factor() const;
  void _internal_set_weight_decay_factor(float value);
  public:

  // .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
  void clear_gradient_accumulation_status();
  ::tensorflow::tpu::GradientAccumulationStatus_Status gradient_accumulation_status() const;
  void set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value);
  private:
  ::tensorflow::tpu::GradientAccumulationStatus_Status _internal_gradient_accumulation_status() const;
  void _internal_set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value);
  public:

  // bool multiply_weight_decay_factor_by_learning_rate = 22;
  void clear_multiply_weight_decay_factor_by_learning_rate();
  bool multiply_weight_decay_factor_by_learning_rate() const;
  void set_multiply_weight_decay_factor_by_learning_rate(bool value);
  private:
  bool _internal_multiply_weight_decay_factor_by_learning_rate() const;
  void _internal_set_multiply_weight_decay_factor_by_learning_rate(bool value);
  public:

  // .tensorflow.tpu.LowDimensionalPackingStatus.Status low_dimensional_packing_status = 28;
  void clear_low_dimensional_packing_status();
  ::tensorflow::tpu::LowDimensionalPackingStatus_Status low_dimensional_packing_status() const;
  void set_low_dimensional_packing_status(::tensorflow::tpu::LowDimensionalPackingStatus_Status value);
  private:
  ::tensorflow::tpu::LowDimensionalPackingStatus_Status _internal_low_dimensional_packing_status() const;
  void _internal_set_low_dimensional_packing_status(::tensorflow::tpu::LowDimensionalPackingStatus_Status value);
  public:

  // .tensorflow.tpu.AdagradParameters adagrad = 3;
  bool has_adagrad() const;
  private:
  bool _internal_has_adagrad() const;
  public:
  void clear_adagrad();
  const ::tensorflow::tpu::AdagradParameters& adagrad() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::AdagradParameters* release_adagrad();
  ::tensorflow::tpu::AdagradParameters* mutable_adagrad();
  void set_allocated_adagrad(::tensorflow::tpu::AdagradParameters* adagrad);
  private:
  const ::tensorflow::tpu::AdagradParameters& _internal_adagrad() const;
  ::tensorflow::tpu::AdagradParameters* _internal_mutable_adagrad();
  public:
  void unsafe_arena_set_allocated_adagrad(
      ::tensorflow::tpu::AdagradParameters* adagrad);
  ::tensorflow::tpu::AdagradParameters* unsafe_arena_release_adagrad();

  // .tensorflow.tpu.AdagradMomentumParameters adagrad_momentum = 26;
  bool has_adagrad_momentum() const;
  private:
  bool _internal_has_adagrad_momentum() const;
  public:
  void clear_adagrad_momentum();
  const ::tensorflow::tpu::AdagradMomentumParameters& adagrad_momentum() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::AdagradMomentumParameters* release_adagrad_momentum();
  ::tensorflow::tpu::AdagradMomentumParameters* mutable_adagrad_momentum();
  void set_allocated_adagrad_momentum(::tensorflow::tpu::AdagradMomentumParameters* adagrad_momentum);
  private:
  const ::tensorflow::tpu::AdagradMomentumParameters& _internal_adagrad_momentum() const;
  ::tensorflow::tpu::AdagradMomentumParameters* _internal_mutable_adagrad_momentum();
  public:
  void unsafe_arena_set_allocated_adagrad_momentum(
      ::tensorflow::tpu::AdagradMomentumParameters* adagrad_momentum);
  ::tensorflow::tpu::AdagradMomentumParameters* unsafe_arena_release_adagrad_momentum();

  // .tensorflow.tpu.BoundedAdagradParameters bounded_adagrad = 19;
  bool has_bounded_adagrad() const;
  private:
  bool _internal_has_bounded_adagrad() const;
  public:
  void clear_bounded_adagrad();
  const ::tensorflow::tpu::BoundedAdagradParameters& bounded_adagrad() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::BoundedAdagradParameters* release_bounded_adagrad();
  ::tensorflow::tpu::BoundedAdagradParameters* mutable_bounded_adagrad();
  void set_allocated_bounded_adagrad(::tensorflow::tpu::BoundedAdagradParameters* bounded_adagrad);
  private:
  const ::tensorflow::tpu::BoundedAdagradParameters& _internal_bounded_adagrad() const;
  ::tensorflow::tpu::BoundedAdagradParameters* _internal_mutable_bounded_adagrad();
  public:
  void unsafe_arena_set_allocated_bounded_adagrad(
      ::tensorflow::tpu::BoundedAdagradParameters* bounded_adagrad);
  ::tensorflow::tpu::BoundedAdagradParameters* unsafe_arena_release_bounded_adagrad();

  // .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
  bool has_stochastic_gradient_descent() const;
  private:
  bool _internal_has_stochastic_gradient_descent() const;
  public:
  void clear_stochastic_gradient_descent();
  const ::tensorflow::tpu::StochasticGradientDescentParameters& stochastic_gradient_descent() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::StochasticGradientDescentParameters* release_stochastic_gradient_descent();
  ::tensorflow::tpu::StochasticGradientDescentParameters* mutable_stochastic_gradient_descent();
  void set_allocated_stochastic_gradient_descent(::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent);
  private:
  const ::tensorflow::tpu::StochasticGradientDescentParameters& _internal_stochastic_gradient_descent() const;
  ::tensorflow::tpu::StochasticGradientDescentParameters* _internal_mutable_stochastic_gradient_descent();
  public:
  void unsafe_arena_set_allocated_stochastic_gradient_descent(
      ::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent);
  ::tensorflow::tpu::StochasticGradientDescentParameters* unsafe_arena_release_stochastic_gradient_descent();

  // .tensorflow.tpu.FtrlParameters ftrl = 5;
  bool has_ftrl() const;
  private:
  bool _internal_has_ftrl() const;
  public:
  void clear_ftrl();
  const ::tensorflow::tpu::FtrlParameters& ftrl() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::FtrlParameters* release_ftrl();
  ::tensorflow::tpu::FtrlParameters* mutable_ftrl();
  void set_allocated_ftrl(::tensorflow::tpu::FtrlParameters* ftrl);
  private:
  const ::tensorflow::tpu::FtrlParameters& _internal_ftrl() const;
  ::tensorflow::tpu::FtrlParameters* _internal_mutable_ftrl();
  public:
  void unsafe_arena_set_allocated_ftrl(
      ::tensorflow::tpu::FtrlParameters* ftrl);
  ::tensorflow::tpu::FtrlParameters* unsafe_arena_release_ftrl();

  // .tensorflow.tpu.AdamParameters adam = 6;
  bool has_adam() const;
  private:
  bool _internal_has_adam() const;
  public:
  void clear_adam();
  const ::tensorflow::tpu::AdamParameters& adam() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::AdamParameters* release_adam();
  ::tensorflow::tpu::AdamParameters* mutable_adam();
  void set_allocated_adam(::tensorflow::tpu::AdamParameters* adam);
  private:
  const ::tensorflow::tpu::AdamParameters& _internal_adam() const;
  ::tensorflow::tpu::AdamParameters* _internal_mutable_adam();
  public:
  void unsafe_arena_set_allocated_adam(
      ::tensorflow::tpu::AdamParameters* adam);
  ::tensorflow::tpu::AdamParameters* unsafe_arena_release_adam();

  // .tensorflow.tpu.MomentumParameters momentum = 8;
  bool has_momentum() const;
  private:
  bool _internal_has_momentum() const;
  public:
  void clear_momentum();
  const ::tensorflow::tpu::MomentumParameters& momentum() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::MomentumParameters* release_momentum();
  ::tensorflow::tpu::MomentumParameters* mutable_momentum();
  void set_allocated_momentum(::tensorflow::tpu::MomentumParameters* momentum);
  private:
  const ::tensorflow::tpu::MomentumParameters& _internal_momentum() const;
  ::tensorflow::tpu::MomentumParameters* _internal_mutable_momentum();
  public:
  void unsafe_arena_set_allocated_momentum(
      ::tensorflow::tpu::MomentumParameters* momentum);
  ::tensorflow::tpu::MomentumParameters* unsafe_arena_release_momentum();

  // .tensorflow.tpu.LionParameters lion = 29;
  bool has_lion() const;
  private:
  bool _internal_has_lion() const;
  public:
  void clear_lion();
  const ::tensorflow::tpu::LionParameters& lion() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::LionParameters* release_lion();
  ::tensorflow::tpu::LionParameters* mutable_lion();
  void set_allocated_lion(::tensorflow::tpu::LionParameters* lion);
  private:
  const ::tensorflow::tpu::LionParameters& _internal_lion() const;
  ::tensorflow::tpu::LionParameters* _internal_mutable_lion();
  public:
  void unsafe_arena_set_allocated_lion(
      ::tensorflow::tpu::LionParameters* lion);
  ::tensorflow::tpu::LionParameters* unsafe_arena_release_lion();

  // .tensorflow.tpu.RmsPropParameters rms_prop = 9;
  bool has_rms_prop() const;
  private:
  bool _internal_has_rms_prop() const;
  public:
  void clear_rms_prop();
  const ::tensorflow::tpu::RmsPropParameters& rms_prop() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::RmsPropParameters* release_rms_prop();
  ::tensorflow::tpu::RmsPropParameters* mutable_rms_prop();
  void set_allocated_rms_prop(::tensorflow::tpu::RmsPropParameters* rms_prop);
  private:
  const ::tensorflow::tpu::RmsPropParameters& _internal_rms_prop() const;
  ::tensorflow::tpu::RmsPropParameters* _internal_mutable_rms_prop();
  public:
  void unsafe_arena_set_allocated_rms_prop(
      ::tensorflow::tpu::RmsPropParameters* rms_prop);
  ::tensorflow::tpu::RmsPropParameters* unsafe_arena_release_rms_prop();

  // .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
  bool has_centered_rms_prop() const;
  private:
  bool _internal_has_centered_rms_prop() const;
  public:
  void clear_centered_rms_prop();
  const ::tensorflow::tpu::CenteredRmsPropParameters& centered_rms_prop() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::CenteredRmsPropParameters* release_centered_rms_prop();
  ::tensorflow::tpu::CenteredRmsPropParameters* mutable_centered_rms_prop();
  void set_allocated_centered_rms_prop(::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop);
  private:
  const ::tensorflow::tpu::CenteredRmsPropParameters& _internal_centered_rms_prop() const;
  ::tensorflow::tpu::CenteredRmsPropParameters* _internal_mutable_centered_rms_prop();
  public:
  void unsafe_arena_set_allocated_centered_rms_prop(
      ::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop);
  ::tensorflow::tpu::CenteredRmsPropParameters* unsafe_arena_release_centered_rms_prop();

  // .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
  bool has_mdl_adagrad_light() const;
  private:
  bool _internal_has_mdl_adagrad_light() const;
  public:
  void clear_mdl_adagrad_light();
  const ::tensorflow::tpu::MdlAdagradLightParameters& mdl_adagrad_light() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::MdlAdagradLightParameters* release_mdl_adagrad_light();
  ::tensorflow::tpu::MdlAdagradLightParameters* mutable_mdl_adagrad_light();
  void set_allocated_mdl_adagrad_light(::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light);
  private:
  const ::tensorflow::tpu::MdlAdagradLightParameters& _internal_mdl_adagrad_light() const;
  ::tensorflow::tpu::MdlAdagradLightParameters* _internal_mutable_mdl_adagrad_light();
  public:
  void unsafe_arena_set_allocated_mdl_adagrad_light(
      ::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light);
  ::tensorflow::tpu::MdlAdagradLightParameters* unsafe_arena_release_mdl_adagrad_light();

  // .tensorflow.tpu.AdadeltaParameters adadelta = 12;
  bool has_adadelta() const;
  private:
  bool _internal_has_adadelta() const;
  public:
  void clear_adadelta();
  const ::tensorflow::tpu::AdadeltaParameters& adadelta() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::AdadeltaParameters* release_adadelta();
  ::tensorflow::tpu::AdadeltaParameters* mutable_adadelta();
  void set_allocated_adadelta(::tensorflow::tpu::AdadeltaParameters* adadelta);
  private:
  const ::tensorflow::tpu::AdadeltaParameters& _internal_adadelta() const;
  ::tensorflow::tpu::AdadeltaParameters* _internal_mutable_adadelta();
  public:
  void unsafe_arena_set_allocated_adadelta(
      ::tensorflow::tpu::AdadeltaParameters* adadelta);
  ::tensorflow::tpu::AdadeltaParameters* unsafe_arena_release_adadelta();

  // .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
  bool has_proximal_adagrad() const;
  private:
  bool _internal_has_proximal_adagrad() const;
  public:
  void clear_proximal_adagrad();
  const ::tensorflow::tpu::ProximalAdagradParameters& proximal_adagrad() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::ProximalAdagradParameters* release_proximal_adagrad();
  ::tensorflow::tpu::ProximalAdagradParameters* mutable_proximal_adagrad();
  void set_allocated_proximal_adagrad(::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad);
  private:
  const ::tensorflow::tpu::ProximalAdagradParameters& _internal_proximal_adagrad() const;
  ::tensorflow::tpu::ProximalAdagradParameters* _internal_mutable_proximal_adagrad();
  public:
  void unsafe_arena_set_allocated_proximal_adagrad(
      ::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad);
  ::tensorflow::tpu::ProximalAdagradParameters* unsafe_arena_release_proximal_adagrad();

  // .tensorflow.tpu.OnlineYogiParameters online_yogi = 20;
  bool has_online_yogi() const;
  private:
  bool _internal_has_online_yogi() const;
  public:
  void clear_online_yogi();
  const ::tensorflow::tpu::OnlineYogiParameters& online_yogi() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::OnlineYogiParameters* release_online_yogi();
  ::tensorflow::tpu::OnlineYogiParameters* mutable_online_yogi();
  void set_allocated_online_yogi(::tensorflow::tpu::OnlineYogiParameters* online_yogi);
  private:
  const ::tensorflow::tpu::OnlineYogiParameters& _internal_online_yogi() const;
  ::tensorflow::tpu::OnlineYogiParameters* _internal_mutable_online_yogi();
  public:
  void unsafe_arena_set_allocated_online_yogi(
      ::tensorflow::tpu::OnlineYogiParameters* online_yogi);
  ::tensorflow::tpu::OnlineYogiParameters* unsafe_arena_release_online_yogi();

  // .tensorflow.tpu.ProximalYogiParameters proximal_yogi = 21;
  bool has_proximal_yogi() const;
  private:
  bool _internal_has_proximal_yogi() const;
  public:
  void clear_proximal_yogi();
  const ::tensorflow::tpu::ProximalYogiParameters& proximal_yogi() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::ProximalYogiParameters* release_proximal_yogi();
  ::tensorflow::tpu::ProximalYogiParameters* mutable_proximal_yogi();
  void set_allocated_proximal_yogi(::tensorflow::tpu::ProximalYogiParameters* proximal_yogi);
  private:
  const ::tensorflow::tpu::ProximalYogiParameters& _internal_proximal_yogi() const;
  ::tensorflow::tpu::ProximalYogiParameters* _internal_mutable_proximal_yogi();
  public:
  void unsafe_arena_set_allocated_proximal_yogi(
      ::tensorflow::tpu::ProximalYogiParameters* proximal_yogi);
  ::tensorflow::tpu::ProximalYogiParameters* unsafe_arena_release_proximal_yogi();

  // .tensorflow.tpu.FrequencyEstimatorParameters frequency_estimator = 23;
  bool has_frequency_estimator() const;
  private:
  bool _internal_has_frequency_estimator() const;
  public:
  void clear_frequency_estimator();
  const ::tensorflow::tpu::FrequencyEstimatorParameters& frequency_estimator() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::FrequencyEstimatorParameters* release_frequency_estimator();
  ::tensorflow::tpu::FrequencyEstimatorParameters* mutable_frequency_estimator();
  void set_allocated_frequency_estimator(::tensorflow::tpu::FrequencyEstimatorParameters* frequency_estimator);
  private:
  const ::tensorflow::tpu::FrequencyEstimatorParameters& _internal_frequency_estimator() const;
  ::tensorflow::tpu::FrequencyEstimatorParameters* _internal_mutable_frequency_estimator();
  public:
  void unsafe_arena_set_allocated_frequency_estimator(
      ::tensorflow::tpu::FrequencyEstimatorParameters* frequency_estimator);
  ::tensorflow::tpu::FrequencyEstimatorParameters* unsafe_arena_release_frequency_estimator();

  // .tensorflow.tpu.UserDefinedProgramParameters user_defined_program = 24;
  bool has_user_defined_program() const;
  private:
  bool _internal_has_user_defined_program() const;
  public:
  void clear_user_defined_program();
  const ::tensorflow::tpu::UserDefinedProgramParameters& user_defined_program() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::UserDefinedProgramParameters* release_user_defined_program();
  ::tensorflow::tpu::UserDefinedProgramParameters* mutable_user_defined_program();
  void set_allocated_user_defined_program(::tensorflow::tpu::UserDefinedProgramParameters* user_defined_program);
  private:
  const ::tensorflow::tpu::UserDefinedProgramParameters& _internal_user_defined_program() const;
  ::tensorflow::tpu::UserDefinedProgramParameters* _internal_mutable_user_defined_program();
  public:
  void unsafe_arena_set_allocated_user_defined_program(
      ::tensorflow::tpu::UserDefinedProgramParameters* user_defined_program);
  ::tensorflow::tpu::UserDefinedProgramParameters* unsafe_arena_release_user_defined_program();

  // .tensorflow.tpu.AssignParameters assign = 25;
  bool has_assign() const;
  private:
  bool _internal_has_assign() const;
  public:
  void clear_assign();
  const ::tensorflow::tpu::AssignParameters& assign() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::AssignParameters* release_assign();
  ::tensorflow::tpu::AssignParameters* mutable_assign();
  void set_allocated_assign(::tensorflow::tpu::AssignParameters* assign);
  private:
  const ::tensorflow::tpu::AssignParameters& _internal_assign() const;
  ::tensorflow::tpu::AssignParameters* _internal_mutable_assign();
  public:
  void unsafe_arena_set_allocated_assign(
      ::tensorflow::tpu::AssignParameters* assign);
  ::tensorflow::tpu::AssignParameters* unsafe_arena_release_assign();

  void clear_parameters();
  ParametersCase parameters_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.OptimizationParameters)
 private:
  class _Internal;
  void set_has_adagrad();
  void set_has_adagrad_momentum();
  void set_has_bounded_adagrad();
  void set_has_stochastic_gradient_descent();
  void set_has_ftrl();
  void set_has_adam();
  void set_has_momentum();
  void set_has_lion();
  void set_has_rms_prop();
  void set_has_centered_rms_prop();
  void set_has_mdl_adagrad_light();
  void set_has_adadelta();
  void set_has_proximal_adagrad();
  void set_has_online_yogi();
  void set_has_proximal_yogi();
  void set_has_frequency_estimator();
  void set_has_user_defined_program();
  void set_has_assign();

  inline bool has_parameters() const;
  inline void clear_has_parameters();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::tensorflow::tpu::ClippingLimits* clipping_limits_;
    ::tensorflow::tpu::ClippingLimits* gradient_clipping_limits_;
    ::tensorflow::tpu::LearningRate* learning_rate_;
    ::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration_;
    ::tensorflow::tpu::SimulatedQuantization* simulated_quantization_;
    float weight_decay_factor_;
    int gradient_accumulation_status_;
    bool multiply_weight_decay_factor_by_learning_rate_;
    int low_dimensional_packing_status_;
    union ParametersUnion {
      constexpr ParametersUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::tpu::AdagradParameters* adagrad_;
      ::tensorflow::tpu::AdagradMomentumParameters* adagrad_momentum_;
      ::tensorflow::tpu::BoundedAdagradParameters* bounded_adagrad_;
      ::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent_;
      ::tensorflow::tpu::FtrlParameters* ftrl_;
      ::tensorflow::tpu::AdamParameters* adam_;
      ::tensorflow::tpu::MomentumParameters* momentum_;
      ::tensorflow::tpu::LionParameters* lion_;
      ::tensorflow::tpu::RmsPropParameters* rms_prop_;
      ::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop_;
      ::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light_;
      ::tensorflow::tpu::AdadeltaParameters* adadelta_;
      ::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad_;
      ::tensorflow::tpu::OnlineYogiParameters* online_yogi_;
      ::tensorflow::tpu::ProximalYogiParameters* proximal_yogi_;
      ::tensorflow::tpu::FrequencyEstimatorParameters* frequency_estimator_;
      ::tensorflow::tpu::UserDefinedProgramParameters* user_defined_program_;
      ::tensorflow::tpu::AssignParameters* assign_;
    } parameters_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StateVariableSpecification_UserDefined final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification.UserDefined) */ {
 public:
  inline StateVariableSpecification_UserDefined() : StateVariableSpecification_UserDefined(nullptr) {}
  explicit PROTOBUF_CONSTEXPR StateVariableSpecification_UserDefined(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StateVariableSpecification_UserDefined(const StateVariableSpecification_UserDefined& from);
  StateVariableSpecification_UserDefined(StateVariableSpecification_UserDefined&& from) noexcept
    : StateVariableSpecification_UserDefined() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification_UserDefined& operator=(const StateVariableSpecification_UserDefined& from) {
    CopyFrom(from);
    return *this;
  }
  inline StateVariableSpecification_UserDefined& operator=(StateVariableSpecification_UserDefined&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StateVariableSpecification_UserDefined& default_instance() {
    return *internal_default_instance();
  }
  static inline const StateVariableSpecification_UserDefined* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification_UserDefined*>(
               &_StateVariableSpecification_UserDefined_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    26;

  friend void swap(StateVariableSpecification_UserDefined& a, StateVariableSpecification_UserDefined& b) {
    a.Swap(&b);
  }
  inline void Swap(StateVariableSpecification_UserDefined* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StateVariableSpecification_UserDefined* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StateVariableSpecification_UserDefined* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StateVariableSpecification_UserDefined>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const StateVariableSpecification_UserDefined& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const StateVariableSpecification_UserDefined& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StateVariableSpecification.UserDefined";
  }
  protected:
  explicit StateVariableSpecification_UserDefined(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.UserDefined)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StateVariableSpecification_FillWithConstant final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification.FillWithConstant) */ {
 public:
  inline StateVariableSpecification_FillWithConstant() : StateVariableSpecification_FillWithConstant(nullptr) {}
  ~StateVariableSpecification_FillWithConstant() override;
  explicit PROTOBUF_CONSTEXPR StateVariableSpecification_FillWithConstant(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StateVariableSpecification_FillWithConstant(const StateVariableSpecification_FillWithConstant& from);
  StateVariableSpecification_FillWithConstant(StateVariableSpecification_FillWithConstant&& from) noexcept
    : StateVariableSpecification_FillWithConstant() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification_FillWithConstant& operator=(const StateVariableSpecification_FillWithConstant& from) {
    CopyFrom(from);
    return *this;
  }
  inline StateVariableSpecification_FillWithConstant& operator=(StateVariableSpecification_FillWithConstant&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StateVariableSpecification_FillWithConstant& default_instance() {
    return *internal_default_instance();
  }
  static inline const StateVariableSpecification_FillWithConstant* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification_FillWithConstant*>(
               &_StateVariableSpecification_FillWithConstant_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(StateVariableSpecification_FillWithConstant& a, StateVariableSpecification_FillWithConstant& b) {
    a.Swap(&b);
  }
  inline void Swap(StateVariableSpecification_FillWithConstant* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StateVariableSpecification_FillWithConstant* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StateVariableSpecification_FillWithConstant* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StateVariableSpecification_FillWithConstant>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StateVariableSpecification_FillWithConstant& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StateVariableSpecification_FillWithConstant& from) {
    StateVariableSpecification_FillWithConstant::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification_FillWithConstant* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StateVariableSpecification.FillWithConstant";
  }
  protected:
  explicit StateVariableSpecification_FillWithConstant(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInitialValueFieldNumber = 1,
  };
  // double initial_value = 1;
  void clear_initial_value();
  double initial_value() const;
  void set_initial_value(double value);
  private:
  double _internal_initial_value() const;
  void _internal_set_initial_value(double value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification.FillWithConstant)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double initial_value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// -------------------------------------------------------------------

class StateVariableSpecification final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.tpu.StateVariableSpecification) */ {
 public:
  inline StateVariableSpecification() : StateVariableSpecification(nullptr) {}
  ~StateVariableSpecification() override;
  explicit PROTOBUF_CONSTEXPR StateVariableSpecification(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StateVariableSpecification(const StateVariableSpecification& from);
  StateVariableSpecification(StateVariableSpecification&& from) noexcept
    : StateVariableSpecification() {
    *this = ::std::move(from);
  }

  inline StateVariableSpecification& operator=(const StateVariableSpecification& from) {
    CopyFrom(from);
    return *this;
  }
  inline StateVariableSpecification& operator=(StateVariableSpecification&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StateVariableSpecification& default_instance() {
    return *internal_default_instance();
  }
  enum UsageCase {
    kUserDefined = 2,
    kFillWithConstant = 3,
    USAGE_NOT_SET = 0,
  };

  static inline const StateVariableSpecification* internal_default_instance() {
    return reinterpret_cast<const StateVariableSpecification*>(
               &_StateVariableSpecification_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(StateVariableSpecification& a, StateVariableSpecification& b) {
    a.Swap(&b);
  }
  inline void Swap(StateVariableSpecification* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StateVariableSpecification* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StateVariableSpecification* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StateVariableSpecification>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StateVariableSpecification& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StateVariableSpecification& from) {
    StateVariableSpecification::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StateVariableSpecification* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.tpu.StateVariableSpecification";
  }
  protected:
  explicit StateVariableSpecification(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef StateVariableSpecification_UserDefined UserDefined;
  typedef StateVariableSpecification_FillWithConstant FillWithConstant;

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kUserDefinedFieldNumber = 2,
    kFillWithConstantFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
  bool has_user_defined() const;
  private:
  bool _internal_has_user_defined() const;
  public:
  void clear_user_defined();
  const ::tensorflow::tpu::StateVariableSpecification_UserDefined& user_defined() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::StateVariableSpecification_UserDefined* release_user_defined();
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* mutable_user_defined();
  void set_allocated_user_defined(::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined);
  private:
  const ::tensorflow::tpu::StateVariableSpecification_UserDefined& _internal_user_defined() const;
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* _internal_mutable_user_defined();
  public:
  void unsafe_arena_set_allocated_user_defined(
      ::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined);
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* unsafe_arena_release_user_defined();

  // .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
  bool has_fill_with_constant() const;
  private:
  bool _internal_has_fill_with_constant() const;
  public:
  void clear_fill_with_constant();
  const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& fill_with_constant() const;
  PROTOBUF_NODISCARD ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* release_fill_with_constant();
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* mutable_fill_with_constant();
  void set_allocated_fill_with_constant(::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant);
  private:
  const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& _internal_fill_with_constant() const;
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* _internal_mutable_fill_with_constant();
  public:
  void unsafe_arena_set_allocated_fill_with_constant(
      ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant);
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* unsafe_arena_release_fill_with_constant();

  void clear_usage();
  UsageCase usage_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.tpu.StateVariableSpecification)
 private:
  class _Internal;
  void set_has_user_defined();
  void set_has_fill_with_constant();

  inline bool has_usage() const;
  inline void clear_has_usage();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    union UsageUnion {
      constexpr UsageUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined_;
      ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant_;
    } usage_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ClippingLimits

// .google.protobuf.FloatValue lower = 1;
inline bool ClippingLimits::_internal_has_lower() const {
  return this != internal_default_instance() && _impl_.lower_ != nullptr;
}
inline bool ClippingLimits::has_lower() const {
  return _internal_has_lower();
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& ClippingLimits::_internal_lower() const {
  const ::PROTOBUF_NAMESPACE_ID::FloatValue* p = _impl_.lower_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::FloatValue&>(
      ::PROTOBUF_NAMESPACE_ID::_FloatValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& ClippingLimits::lower() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ClippingLimits.lower)
  return _internal_lower();
}
inline void ClippingLimits::unsafe_arena_set_allocated_lower(
    ::PROTOBUF_NAMESPACE_ID::FloatValue* lower) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.lower_);
  }
  _impl_.lower_ = lower;
  if (lower) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.ClippingLimits.lower)
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::release_lower() {
  
  ::PROTOBUF_NAMESPACE_ID::FloatValue* temp = _impl_.lower_;
  _impl_.lower_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::unsafe_arena_release_lower() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.ClippingLimits.lower)
  
  ::PROTOBUF_NAMESPACE_ID::FloatValue* temp = _impl_.lower_;
  _impl_.lower_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::_internal_mutable_lower() {
  
  if (_impl_.lower_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::FloatValue>(GetArenaForAllocation());
    _impl_.lower_ = p;
  }
  return _impl_.lower_;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::mutable_lower() {
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _msg = _internal_mutable_lower();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.ClippingLimits.lower)
  return _msg;
}
inline void ClippingLimits::set_allocated_lower(::PROTOBUF_NAMESPACE_ID::FloatValue* lower) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.lower_);
  }
  if (lower) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(lower));
    if (message_arena != submessage_arena) {
      lower = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, lower, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.lower_ = lower;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.ClippingLimits.lower)
}

// .google.protobuf.FloatValue upper = 2;
inline bool ClippingLimits::_internal_has_upper() const {
  return this != internal_default_instance() && _impl_.upper_ != nullptr;
}
inline bool ClippingLimits::has_upper() const {
  return _internal_has_upper();
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& ClippingLimits::_internal_upper() const {
  const ::PROTOBUF_NAMESPACE_ID::FloatValue* p = _impl_.upper_;
  return p != nullptr ? *p : reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::FloatValue&>(
      ::PROTOBUF_NAMESPACE_ID::_FloatValue_default_instance_);
}
inline const ::PROTOBUF_NAMESPACE_ID::FloatValue& ClippingLimits::upper() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ClippingLimits.upper)
  return _internal_upper();
}
inline void ClippingLimits::unsafe_arena_set_allocated_upper(
    ::PROTOBUF_NAMESPACE_ID::FloatValue* upper) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.upper_);
  }
  _impl_.upper_ = upper;
  if (upper) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.ClippingLimits.upper)
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::release_upper() {
  
  ::PROTOBUF_NAMESPACE_ID::FloatValue* temp = _impl_.upper_;
  _impl_.upper_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::unsafe_arena_release_upper() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.ClippingLimits.upper)
  
  ::PROTOBUF_NAMESPACE_ID::FloatValue* temp = _impl_.upper_;
  _impl_.upper_ = nullptr;
  return temp;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::_internal_mutable_upper() {
  
  if (_impl_.upper_ == nullptr) {
    auto* p = CreateMaybeMessage<::PROTOBUF_NAMESPACE_ID::FloatValue>(GetArenaForAllocation());
    _impl_.upper_ = p;
  }
  return _impl_.upper_;
}
inline ::PROTOBUF_NAMESPACE_ID::FloatValue* ClippingLimits::mutable_upper() {
  ::PROTOBUF_NAMESPACE_ID::FloatValue* _msg = _internal_mutable_upper();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.ClippingLimits.upper)
  return _msg;
}
inline void ClippingLimits::set_allocated_upper(::PROTOBUF_NAMESPACE_ID::FloatValue* upper) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.upper_);
  }
  if (upper) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(upper));
    if (message_arena != submessage_arena) {
      upper = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, upper, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.upper_ = upper;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.ClippingLimits.upper)
}

// -------------------------------------------------------------------

// SimulatedQuantization

// bool enabled = 1;
inline void SimulatedQuantization::clear_enabled() {
  _impl_.enabled_ = false;
}
inline bool SimulatedQuantization::_internal_enabled() const {
  return _impl_.enabled_;
}
inline bool SimulatedQuantization::enabled() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.SimulatedQuantization.enabled)
  return _internal_enabled();
}
inline void SimulatedQuantization::_internal_set_enabled(bool value) {
  
  _impl_.enabled_ = value;
}
inline void SimulatedQuantization::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.SimulatedQuantization.enabled)
}

// .tensorflow.tpu.ClippingLimits clipping_limits = 2;
inline bool SimulatedQuantization::_internal_has_clipping_limits() const {
  return this != internal_default_instance() && _impl_.clipping_limits_ != nullptr;
}
inline bool SimulatedQuantization::has_clipping_limits() const {
  return _internal_has_clipping_limits();
}
inline void SimulatedQuantization::clear_clipping_limits() {
  if (GetArenaForAllocation() == nullptr && _impl_.clipping_limits_ != nullptr) {
    delete _impl_.clipping_limits_;
  }
  _impl_.clipping_limits_ = nullptr;
}
inline const ::tensorflow::tpu::ClippingLimits& SimulatedQuantization::_internal_clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = _impl_.clipping_limits_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::ClippingLimits&>(
      ::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline const ::tensorflow::tpu::ClippingLimits& SimulatedQuantization::clipping_limits() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.SimulatedQuantization.clipping_limits)
  return _internal_clipping_limits();
}
inline void SimulatedQuantization::unsafe_arena_set_allocated_clipping_limits(
    ::tensorflow::tpu::ClippingLimits* clipping_limits) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.clipping_limits_);
  }
  _impl_.clipping_limits_ = clipping_limits;
  if (clipping_limits) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.SimulatedQuantization.clipping_limits)
}
inline ::tensorflow::tpu::ClippingLimits* SimulatedQuantization::release_clipping_limits() {
  
  ::tensorflow::tpu::ClippingLimits* temp = _impl_.clipping_limits_;
  _impl_.clipping_limits_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* SimulatedQuantization::unsafe_arena_release_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.SimulatedQuantization.clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = _impl_.clipping_limits_;
  _impl_.clipping_limits_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* SimulatedQuantization::_internal_mutable_clipping_limits() {
  
  if (_impl_.clipping_limits_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaForAllocation());
    _impl_.clipping_limits_ = p;
  }
  return _impl_.clipping_limits_;
}
inline ::tensorflow::tpu::ClippingLimits* SimulatedQuantization::mutable_clipping_limits() {
  ::tensorflow::tpu::ClippingLimits* _msg = _internal_mutable_clipping_limits();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.SimulatedQuantization.clipping_limits)
  return _msg;
}
inline void SimulatedQuantization::set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.clipping_limits_;
  }
  if (clipping_limits) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(clipping_limits);
    if (message_arena != submessage_arena) {
      clipping_limits = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.clipping_limits_ = clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.SimulatedQuantization.clipping_limits)
}

// int32 num_buckets = 3;
inline void SimulatedQuantization::clear_num_buckets() {
  _impl_.num_buckets_ = 0;
}
inline int32_t SimulatedQuantization::_internal_num_buckets() const {
  return _impl_.num_buckets_;
}
inline int32_t SimulatedQuantization::num_buckets() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.SimulatedQuantization.num_buckets)
  return _internal_num_buckets();
}
inline void SimulatedQuantization::_internal_set_num_buckets(int32_t value) {
  
  _impl_.num_buckets_ = value;
}
inline void SimulatedQuantization::set_num_buckets(int32_t value) {
  _internal_set_num_buckets(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.SimulatedQuantization.num_buckets)
}

// -------------------------------------------------------------------

// DynamicLearningRate

// int32 tag = 1;
inline void DynamicLearningRate::clear_tag() {
  _impl_.tag_ = 0;
}
inline int32_t DynamicLearningRate::_internal_tag() const {
  return _impl_.tag_;
}
inline int32_t DynamicLearningRate::tag() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.DynamicLearningRate.tag)
  return _internal_tag();
}
inline void DynamicLearningRate::_internal_set_tag(int32_t value) {
  
  _impl_.tag_ = value;
}
inline void DynamicLearningRate::set_tag(int32_t value) {
  _internal_set_tag(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.DynamicLearningRate.tag)
}

// -------------------------------------------------------------------

// LearningRate

// float constant = 1;
inline bool LearningRate::_internal_has_constant() const {
  return learning_rate_case() == kConstant;
}
inline bool LearningRate::has_constant() const {
  return _internal_has_constant();
}
inline void LearningRate::set_has_constant() {
  _impl_._oneof_case_[0] = kConstant;
}
inline void LearningRate::clear_constant() {
  if (_internal_has_constant()) {
    _impl_.learning_rate_.constant_ = 0;
    clear_has_learning_rate();
  }
}
inline float LearningRate::_internal_constant() const {
  if (_internal_has_constant()) {
    return _impl_.learning_rate_.constant_;
  }
  return 0;
}
inline void LearningRate::_internal_set_constant(float value) {
  if (!_internal_has_constant()) {
    clear_learning_rate();
    set_has_constant();
  }
  _impl_.learning_rate_.constant_ = value;
}
inline float LearningRate::constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LearningRate.constant)
  return _internal_constant();
}
inline void LearningRate::set_constant(float value) {
  _internal_set_constant(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.LearningRate.constant)
}

// .tensorflow.tpu.DynamicLearningRate dynamic = 2;
inline bool LearningRate::_internal_has_dynamic() const {
  return learning_rate_case() == kDynamic;
}
inline bool LearningRate::has_dynamic() const {
  return _internal_has_dynamic();
}
inline void LearningRate::set_has_dynamic() {
  _impl_._oneof_case_[0] = kDynamic;
}
inline void LearningRate::clear_dynamic() {
  if (_internal_has_dynamic()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.learning_rate_.dynamic_;
    }
    clear_has_learning_rate();
  }
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::release_dynamic() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.LearningRate.dynamic)
  if (_internal_has_dynamic()) {
    clear_has_learning_rate();
    ::tensorflow::tpu::DynamicLearningRate* temp = _impl_.learning_rate_.dynamic_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.learning_rate_.dynamic_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::DynamicLearningRate& LearningRate::_internal_dynamic() const {
  return _internal_has_dynamic()
      ? *_impl_.learning_rate_.dynamic_
      : reinterpret_cast< ::tensorflow::tpu::DynamicLearningRate&>(::tensorflow::tpu::_DynamicLearningRate_default_instance_);
}
inline const ::tensorflow::tpu::DynamicLearningRate& LearningRate::dynamic() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LearningRate.dynamic)
  return _internal_dynamic();
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::unsafe_arena_release_dynamic() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.LearningRate.dynamic)
  if (_internal_has_dynamic()) {
    clear_has_learning_rate();
    ::tensorflow::tpu::DynamicLearningRate* temp = _impl_.learning_rate_.dynamic_;
    _impl_.learning_rate_.dynamic_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void LearningRate::unsafe_arena_set_allocated_dynamic(::tensorflow::tpu::DynamicLearningRate* dynamic) {
  clear_learning_rate();
  if (dynamic) {
    set_has_dynamic();
    _impl_.learning_rate_.dynamic_ = dynamic;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.LearningRate.dynamic)
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::_internal_mutable_dynamic() {
  if (!_internal_has_dynamic()) {
    clear_learning_rate();
    set_has_dynamic();
    _impl_.learning_rate_.dynamic_ = CreateMaybeMessage< ::tensorflow::tpu::DynamicLearningRate >(GetArenaForAllocation());
  }
  return _impl_.learning_rate_.dynamic_;
}
inline ::tensorflow::tpu::DynamicLearningRate* LearningRate::mutable_dynamic() {
  ::tensorflow::tpu::DynamicLearningRate* _msg = _internal_mutable_dynamic();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.LearningRate.dynamic)
  return _msg;
}

inline bool LearningRate::has_learning_rate() const {
  return learning_rate_case() != LEARNING_RATE_NOT_SET;
}
inline void LearningRate::clear_has_learning_rate() {
  _impl_._oneof_case_[0] = LEARNING_RATE_NOT_SET;
}
inline LearningRate::LearningRateCase LearningRate::learning_rate_case() const {
  return LearningRate::LearningRateCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// AdagradParameters

// -------------------------------------------------------------------

// AdagradMomentumParameters

// float momentum = 1;
inline void AdagradMomentumParameters::clear_momentum() {
  _impl_.momentum_ = 0;
}
inline float AdagradMomentumParameters::_internal_momentum() const {
  return _impl_.momentum_;
}
inline float AdagradMomentumParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdagradMomentumParameters.momentum)
  return _internal_momentum();
}
inline void AdagradMomentumParameters::_internal_set_momentum(float value) {
  
  _impl_.momentum_ = value;
}
inline void AdagradMomentumParameters::set_momentum(float value) {
  _internal_set_momentum(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdagradMomentumParameters.momentum)
}

// bool use_nesterov = 2;
inline void AdagradMomentumParameters::clear_use_nesterov() {
  _impl_.use_nesterov_ = false;
}
inline bool AdagradMomentumParameters::_internal_use_nesterov() const {
  return _impl_.use_nesterov_;
}
inline bool AdagradMomentumParameters::use_nesterov() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdagradMomentumParameters.use_nesterov)
  return _internal_use_nesterov();
}
inline void AdagradMomentumParameters::_internal_set_use_nesterov(bool value) {
  
  _impl_.use_nesterov_ = value;
}
inline void AdagradMomentumParameters::set_use_nesterov(bool value) {
  _internal_set_use_nesterov(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdagradMomentumParameters.use_nesterov)
}

// float exponent = 3;
inline void AdagradMomentumParameters::clear_exponent() {
  _impl_.exponent_ = 0;
}
inline float AdagradMomentumParameters::_internal_exponent() const {
  return _impl_.exponent_;
}
inline float AdagradMomentumParameters::exponent() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdagradMomentumParameters.exponent)
  return _internal_exponent();
}
inline void AdagradMomentumParameters::_internal_set_exponent(float value) {
  
  _impl_.exponent_ = value;
}
inline void AdagradMomentumParameters::set_exponent(float value) {
  _internal_set_exponent(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdagradMomentumParameters.exponent)
}

// float beta2 = 4;
inline void AdagradMomentumParameters::clear_beta2() {
  _impl_.beta2_ = 0;
}
inline float AdagradMomentumParameters::_internal_beta2() const {
  return _impl_.beta2_;
}
inline float AdagradMomentumParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdagradMomentumParameters.beta2)
  return _internal_beta2();
}
inline void AdagradMomentumParameters::_internal_set_beta2(float value) {
  
  _impl_.beta2_ = value;
}
inline void AdagradMomentumParameters::set_beta2(float value) {
  _internal_set_beta2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdagradMomentumParameters.beta2)
}

// float epsilon = 5;
inline void AdagradMomentumParameters::clear_epsilon() {
  _impl_.epsilon_ = 0;
}
inline float AdagradMomentumParameters::_internal_epsilon() const {
  return _impl_.epsilon_;
}
inline float AdagradMomentumParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdagradMomentumParameters.epsilon)
  return _internal_epsilon();
}
inline void AdagradMomentumParameters::_internal_set_epsilon(float value) {
  
  _impl_.epsilon_ = value;
}
inline void AdagradMomentumParameters::set_epsilon(float value) {
  _internal_set_epsilon(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdagradMomentumParameters.epsilon)
}

// -------------------------------------------------------------------

// BoundedAdagradParameters

// bool update_accumulator_first = 1;
inline void BoundedAdagradParameters::clear_update_accumulator_first() {
  _impl_.update_accumulator_first_ = false;
}
inline bool BoundedAdagradParameters::_internal_update_accumulator_first() const {
  return _impl_.update_accumulator_first_;
}
inline bool BoundedAdagradParameters::update_accumulator_first() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.BoundedAdagradParameters.update_accumulator_first)
  return _internal_update_accumulator_first();
}
inline void BoundedAdagradParameters::_internal_set_update_accumulator_first(bool value) {
  
  _impl_.update_accumulator_first_ = value;
}
inline void BoundedAdagradParameters::set_update_accumulator_first(bool value) {
  _internal_set_update_accumulator_first(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.BoundedAdagradParameters.update_accumulator_first)
}

// float max_var_update = 2;
inline void BoundedAdagradParameters::clear_max_var_update() {
  _impl_.max_var_update_ = 0;
}
inline float BoundedAdagradParameters::_internal_max_var_update() const {
  return _impl_.max_var_update_;
}
inline float BoundedAdagradParameters::max_var_update() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.BoundedAdagradParameters.max_var_update)
  return _internal_max_var_update();
}
inline void BoundedAdagradParameters::_internal_set_max_var_update(float value) {
  
  _impl_.max_var_update_ = value;
}
inline void BoundedAdagradParameters::set_max_var_update(float value) {
  _internal_set_max_var_update(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.BoundedAdagradParameters.max_var_update)
}

// float max_accumulator = 3;
inline void BoundedAdagradParameters::clear_max_accumulator() {
  _impl_.max_accumulator_ = 0;
}
inline float BoundedAdagradParameters::_internal_max_accumulator() const {
  return _impl_.max_accumulator_;
}
inline float BoundedAdagradParameters::max_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.BoundedAdagradParameters.max_accumulator)
  return _internal_max_accumulator();
}
inline void BoundedAdagradParameters::_internal_set_max_accumulator(float value) {
  
  _impl_.max_accumulator_ = value;
}
inline void BoundedAdagradParameters::set_max_accumulator(float value) {
  _internal_set_max_accumulator(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.BoundedAdagradParameters.max_accumulator)
}

// -------------------------------------------------------------------

// StochasticGradientDescentParameters

// -------------------------------------------------------------------

// FtrlParameters

// float l1 = 1;
inline void FtrlParameters::clear_l1() {
  _impl_.l1_ = 0;
}
inline float FtrlParameters::_internal_l1() const {
  return _impl_.l1_;
}
inline float FtrlParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.l1)
  return _internal_l1();
}
inline void FtrlParameters::_internal_set_l1(float value) {
  
  _impl_.l1_ = value;
}
inline void FtrlParameters::set_l1(float value) {
  _internal_set_l1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.l1)
}

// float l2 = 2;
inline void FtrlParameters::clear_l2() {
  _impl_.l2_ = 0;
}
inline float FtrlParameters::_internal_l2() const {
  return _impl_.l2_;
}
inline float FtrlParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.l2)
  return _internal_l2();
}
inline void FtrlParameters::_internal_set_l2(float value) {
  
  _impl_.l2_ = value;
}
inline void FtrlParameters::set_l2(float value) {
  _internal_set_l2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.l2)
}

// float lr_power = 3;
inline void FtrlParameters::clear_lr_power() {
  _impl_.lr_power_ = 0;
}
inline float FtrlParameters::_internal_lr_power() const {
  return _impl_.lr_power_;
}
inline float FtrlParameters::lr_power() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.lr_power)
  return _internal_lr_power();
}
inline void FtrlParameters::_internal_set_lr_power(float value) {
  
  _impl_.lr_power_ = value;
}
inline void FtrlParameters::set_lr_power(float value) {
  _internal_set_lr_power(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.lr_power)
}

// float beta = 7;
inline void FtrlParameters::clear_beta() {
  _impl_.beta_ = 0;
}
inline float FtrlParameters::_internal_beta() const {
  return _impl_.beta_;
}
inline float FtrlParameters::beta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.beta)
  return _internal_beta();
}
inline void FtrlParameters::_internal_set_beta(float value) {
  
  _impl_.beta_ = value;
}
inline void FtrlParameters::set_beta(float value) {
  _internal_set_beta(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.beta)
}

// bool multiply_linear_by_lr = 6;
inline void FtrlParameters::clear_multiply_linear_by_lr() {
  _impl_.multiply_linear_by_lr_ = false;
}
inline bool FtrlParameters::_internal_multiply_linear_by_lr() const {
  return _impl_.multiply_linear_by_lr_;
}
inline bool FtrlParameters::multiply_linear_by_lr() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.multiply_linear_by_lr)
  return _internal_multiply_linear_by_lr();
}
inline void FtrlParameters::_internal_set_multiply_linear_by_lr(bool value) {
  
  _impl_.multiply_linear_by_lr_ = value;
}
inline void FtrlParameters::set_multiply_linear_by_lr(bool value) {
  _internal_set_multiply_linear_by_lr(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.multiply_linear_by_lr)
}

// bool allow_zero_accumulator = 8 [deprecated = true];
inline void FtrlParameters::clear_allow_zero_accumulator() {
  _impl_.allow_zero_accumulator_ = false;
}
inline bool FtrlParameters::_internal_allow_zero_accumulator() const {
  return _impl_.allow_zero_accumulator_;
}
inline bool FtrlParameters::allow_zero_accumulator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FtrlParameters.allow_zero_accumulator)
  return _internal_allow_zero_accumulator();
}
inline void FtrlParameters::_internal_set_allow_zero_accumulator(bool value) {
  
  _impl_.allow_zero_accumulator_ = value;
}
inline void FtrlParameters::set_allow_zero_accumulator(bool value) {
  _internal_set_allow_zero_accumulator(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FtrlParameters.allow_zero_accumulator)
}

// -------------------------------------------------------------------

// AdamParameters

// float beta1 = 3;
inline void AdamParameters::clear_beta1() {
  _impl_.beta1_ = 0;
}
inline float AdamParameters::_internal_beta1() const {
  return _impl_.beta1_;
}
inline float AdamParameters::beta1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.beta1)
  return _internal_beta1();
}
inline void AdamParameters::_internal_set_beta1(float value) {
  
  _impl_.beta1_ = value;
}
inline void AdamParameters::set_beta1(float value) {
  _internal_set_beta1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.beta1)
}

// float beta2 = 4;
inline void AdamParameters::clear_beta2() {
  _impl_.beta2_ = 0;
}
inline float AdamParameters::_internal_beta2() const {
  return _impl_.beta2_;
}
inline float AdamParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.beta2)
  return _internal_beta2();
}
inline void AdamParameters::_internal_set_beta2(float value) {
  
  _impl_.beta2_ = value;
}
inline void AdamParameters::set_beta2(float value) {
  _internal_set_beta2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.beta2)
}

// float epsilon = 5;
inline void AdamParameters::clear_epsilon() {
  _impl_.epsilon_ = 0;
}
inline float AdamParameters::_internal_epsilon() const {
  return _impl_.epsilon_;
}
inline float AdamParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.epsilon)
  return _internal_epsilon();
}
inline void AdamParameters::_internal_set_epsilon(float value) {
  
  _impl_.epsilon_ = value;
}
inline void AdamParameters::set_epsilon(float value) {
  _internal_set_epsilon(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.epsilon)
}

// bool use_non_lazy_adam = 8;
inline void AdamParameters::clear_use_non_lazy_adam() {
  _impl_.use_non_lazy_adam_ = false;
}
inline bool AdamParameters::_internal_use_non_lazy_adam() const {
  return _impl_.use_non_lazy_adam_;
}
inline bool AdamParameters::use_non_lazy_adam() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.use_non_lazy_adam)
  return _internal_use_non_lazy_adam();
}
inline void AdamParameters::_internal_set_use_non_lazy_adam(bool value) {
  
  _impl_.use_non_lazy_adam_ = value;
}
inline void AdamParameters::set_use_non_lazy_adam(bool value) {
  _internal_set_use_non_lazy_adam(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.use_non_lazy_adam)
}

// bool use_sum_inside_sqrt = 10;
inline void AdamParameters::clear_use_sum_inside_sqrt() {
  _impl_.use_sum_inside_sqrt_ = false;
}
inline bool AdamParameters::_internal_use_sum_inside_sqrt() const {
  return _impl_.use_sum_inside_sqrt_;
}
inline bool AdamParameters::use_sum_inside_sqrt() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdamParameters.use_sum_inside_sqrt)
  return _internal_use_sum_inside_sqrt();
}
inline void AdamParameters::_internal_set_use_sum_inside_sqrt(bool value) {
  
  _impl_.use_sum_inside_sqrt_ = value;
}
inline void AdamParameters::set_use_sum_inside_sqrt(bool value) {
  _internal_set_use_sum_inside_sqrt(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdamParameters.use_sum_inside_sqrt)
}

// -------------------------------------------------------------------

// MomentumParameters

// float momentum = 1;
inline void MomentumParameters::clear_momentum() {
  _impl_.momentum_ = 0;
}
inline float MomentumParameters::_internal_momentum() const {
  return _impl_.momentum_;
}
inline float MomentumParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.momentum)
  return _internal_momentum();
}
inline void MomentumParameters::_internal_set_momentum(float value) {
  
  _impl_.momentum_ = value;
}
inline void MomentumParameters::set_momentum(float value) {
  _internal_set_momentum(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.momentum)
}

// bool use_nesterov = 2;
inline void MomentumParameters::clear_use_nesterov() {
  _impl_.use_nesterov_ = false;
}
inline bool MomentumParameters::_internal_use_nesterov() const {
  return _impl_.use_nesterov_;
}
inline bool MomentumParameters::use_nesterov() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MomentumParameters.use_nesterov)
  return _internal_use_nesterov();
}
inline void MomentumParameters::_internal_set_use_nesterov(bool value) {
  
  _impl_.use_nesterov_ = value;
}
inline void MomentumParameters::set_use_nesterov(bool value) {
  _internal_set_use_nesterov(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MomentumParameters.use_nesterov)
}

// -------------------------------------------------------------------

// LionParameters

// float beta1 = 1;
inline void LionParameters::clear_beta1() {
  _impl_.beta1_ = 0;
}
inline float LionParameters::_internal_beta1() const {
  return _impl_.beta1_;
}
inline float LionParameters::beta1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LionParameters.beta1)
  return _internal_beta1();
}
inline void LionParameters::_internal_set_beta1(float value) {
  
  _impl_.beta1_ = value;
}
inline void LionParameters::set_beta1(float value) {
  _internal_set_beta1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.LionParameters.beta1)
}

// float beta2 = 2;
inline void LionParameters::clear_beta2() {
  _impl_.beta2_ = 0;
}
inline float LionParameters::_internal_beta2() const {
  return _impl_.beta2_;
}
inline float LionParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LionParameters.beta2)
  return _internal_beta2();
}
inline void LionParameters::_internal_set_beta2(float value) {
  
  _impl_.beta2_ = value;
}
inline void LionParameters::set_beta2(float value) {
  _internal_set_beta2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.LionParameters.beta2)
}

// bool use_non_lazy_lion = 3;
inline void LionParameters::clear_use_non_lazy_lion() {
  _impl_.use_non_lazy_lion_ = false;
}
inline bool LionParameters::_internal_use_non_lazy_lion() const {
  return _impl_.use_non_lazy_lion_;
}
inline bool LionParameters::use_non_lazy_lion() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.LionParameters.use_non_lazy_lion)
  return _internal_use_non_lazy_lion();
}
inline void LionParameters::_internal_set_use_non_lazy_lion(bool value) {
  
  _impl_.use_non_lazy_lion_ = value;
}
inline void LionParameters::set_use_non_lazy_lion(bool value) {
  _internal_set_use_non_lazy_lion(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.LionParameters.use_non_lazy_lion)
}

// -------------------------------------------------------------------

// RmsPropParameters

// float rho = 1;
inline void RmsPropParameters::clear_rho() {
  _impl_.rho_ = 0;
}
inline float RmsPropParameters::_internal_rho() const {
  return _impl_.rho_;
}
inline float RmsPropParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.rho)
  return _internal_rho();
}
inline void RmsPropParameters::_internal_set_rho(float value) {
  
  _impl_.rho_ = value;
}
inline void RmsPropParameters::set_rho(float value) {
  _internal_set_rho(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.rho)
}

// float momentum = 2;
inline void RmsPropParameters::clear_momentum() {
  _impl_.momentum_ = 0;
}
inline float RmsPropParameters::_internal_momentum() const {
  return _impl_.momentum_;
}
inline float RmsPropParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.momentum)
  return _internal_momentum();
}
inline void RmsPropParameters::_internal_set_momentum(float value) {
  
  _impl_.momentum_ = value;
}
inline void RmsPropParameters::set_momentum(float value) {
  _internal_set_momentum(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.momentum)
}

// float epsilon = 3;
inline void RmsPropParameters::clear_epsilon() {
  _impl_.epsilon_ = 0;
}
inline float RmsPropParameters::_internal_epsilon() const {
  return _impl_.epsilon_;
}
inline float RmsPropParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.RmsPropParameters.epsilon)
  return _internal_epsilon();
}
inline void RmsPropParameters::_internal_set_epsilon(float value) {
  
  _impl_.epsilon_ = value;
}
inline void RmsPropParameters::set_epsilon(float value) {
  _internal_set_epsilon(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.RmsPropParameters.epsilon)
}

// -------------------------------------------------------------------

// CenteredRmsPropParameters

// float rho = 1;
inline void CenteredRmsPropParameters::clear_rho() {
  _impl_.rho_ = 0;
}
inline float CenteredRmsPropParameters::_internal_rho() const {
  return _impl_.rho_;
}
inline float CenteredRmsPropParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.rho)
  return _internal_rho();
}
inline void CenteredRmsPropParameters::_internal_set_rho(float value) {
  
  _impl_.rho_ = value;
}
inline void CenteredRmsPropParameters::set_rho(float value) {
  _internal_set_rho(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.rho)
}

// float momentum = 2;
inline void CenteredRmsPropParameters::clear_momentum() {
  _impl_.momentum_ = 0;
}
inline float CenteredRmsPropParameters::_internal_momentum() const {
  return _impl_.momentum_;
}
inline float CenteredRmsPropParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.momentum)
  return _internal_momentum();
}
inline void CenteredRmsPropParameters::_internal_set_momentum(float value) {
  
  _impl_.momentum_ = value;
}
inline void CenteredRmsPropParameters::set_momentum(float value) {
  _internal_set_momentum(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.momentum)
}

// float epsilon = 3;
inline void CenteredRmsPropParameters::clear_epsilon() {
  _impl_.epsilon_ = 0;
}
inline float CenteredRmsPropParameters::_internal_epsilon() const {
  return _impl_.epsilon_;
}
inline float CenteredRmsPropParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.CenteredRmsPropParameters.epsilon)
  return _internal_epsilon();
}
inline void CenteredRmsPropParameters::_internal_set_epsilon(float value) {
  
  _impl_.epsilon_ = value;
}
inline void CenteredRmsPropParameters::set_epsilon(float value) {
  _internal_set_epsilon(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.CenteredRmsPropParameters.epsilon)
}

// -------------------------------------------------------------------

// MdlAdagradLightParameters

// float l2 = 1;
inline void MdlAdagradLightParameters::clear_l2() {
  _impl_.l2_ = 0;
}
inline float MdlAdagradLightParameters::_internal_l2() const {
  return _impl_.l2_;
}
inline float MdlAdagradLightParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.l2)
  return _internal_l2();
}
inline void MdlAdagradLightParameters::_internal_set_l2(float value) {
  
  _impl_.l2_ = value;
}
inline void MdlAdagradLightParameters::set_l2(float value) {
  _internal_set_l2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.l2)
}

// float lr_power = 2;
inline void MdlAdagradLightParameters::clear_lr_power() {
  _impl_.lr_power_ = 0;
}
inline float MdlAdagradLightParameters::_internal_lr_power() const {
  return _impl_.lr_power_;
}
inline float MdlAdagradLightParameters::lr_power() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.lr_power)
  return _internal_lr_power();
}
inline void MdlAdagradLightParameters::_internal_set_lr_power(float value) {
  
  _impl_.lr_power_ = value;
}
inline void MdlAdagradLightParameters::set_lr_power(float value) {
  _internal_set_lr_power(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.lr_power)
}

// float min_servable_mdl_benefit = 3;
inline void MdlAdagradLightParameters::clear_min_servable_mdl_benefit() {
  _impl_.min_servable_mdl_benefit_ = 0;
}
inline float MdlAdagradLightParameters::_internal_min_servable_mdl_benefit() const {
  return _impl_.min_servable_mdl_benefit_;
}
inline float MdlAdagradLightParameters::min_servable_mdl_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit)
  return _internal_min_servable_mdl_benefit();
}
inline void MdlAdagradLightParameters::_internal_set_min_servable_mdl_benefit(float value) {
  
  _impl_.min_servable_mdl_benefit_ = value;
}
inline void MdlAdagradLightParameters::set_min_servable_mdl_benefit(float value) {
  _internal_set_min_servable_mdl_benefit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.min_servable_mdl_benefit)
}

// float mdl_mix_in_margin = 4;
inline void MdlAdagradLightParameters::clear_mdl_mix_in_margin() {
  _impl_.mdl_mix_in_margin_ = 0;
}
inline float MdlAdagradLightParameters::_internal_mdl_mix_in_margin() const {
  return _impl_.mdl_mix_in_margin_;
}
inline float MdlAdagradLightParameters::mdl_mix_in_margin() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin)
  return _internal_mdl_mix_in_margin();
}
inline void MdlAdagradLightParameters::_internal_set_mdl_mix_in_margin(float value) {
  
  _impl_.mdl_mix_in_margin_ = value;
}
inline void MdlAdagradLightParameters::set_mdl_mix_in_margin(float value) {
  _internal_set_mdl_mix_in_margin(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_mix_in_margin)
}

// float mdl_benefit_rampup_coeff = 5;
inline void MdlAdagradLightParameters::clear_mdl_benefit_rampup_coeff() {
  _impl_.mdl_benefit_rampup_coeff_ = 0;
}
inline float MdlAdagradLightParameters::_internal_mdl_benefit_rampup_coeff() const {
  return _impl_.mdl_benefit_rampup_coeff_;
}
inline float MdlAdagradLightParameters::mdl_benefit_rampup_coeff() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff)
  return _internal_mdl_benefit_rampup_coeff();
}
inline void MdlAdagradLightParameters::_internal_set_mdl_benefit_rampup_coeff(float value) {
  
  _impl_.mdl_benefit_rampup_coeff_ = value;
}
inline void MdlAdagradLightParameters::set_mdl_benefit_rampup_coeff(float value) {
  _internal_set_mdl_benefit_rampup_coeff(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_benefit_rampup_coeff)
}

// float mdl_min_weight = 6;
inline void MdlAdagradLightParameters::clear_mdl_min_weight() {
  _impl_.mdl_min_weight_ = 0;
}
inline float MdlAdagradLightParameters::_internal_mdl_min_weight() const {
  return _impl_.mdl_min_weight_;
}
inline float MdlAdagradLightParameters::mdl_min_weight() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight)
  return _internal_mdl_min_weight();
}
inline void MdlAdagradLightParameters::_internal_set_mdl_min_weight(float value) {
  
  _impl_.mdl_min_weight_ = value;
}
inline void MdlAdagradLightParameters::set_mdl_min_weight(float value) {
  _internal_set_mdl_min_weight(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_min_weight)
}

// float benefit_revisit_scale = 7;
inline void MdlAdagradLightParameters::clear_benefit_revisit_scale() {
  _impl_.benefit_revisit_scale_ = 0;
}
inline float MdlAdagradLightParameters::_internal_benefit_revisit_scale() const {
  return _impl_.benefit_revisit_scale_;
}
inline float MdlAdagradLightParameters::benefit_revisit_scale() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale)
  return _internal_benefit_revisit_scale();
}
inline void MdlAdagradLightParameters::_internal_set_benefit_revisit_scale(float value) {
  
  _impl_.benefit_revisit_scale_ = value;
}
inline void MdlAdagradLightParameters::set_benefit_revisit_scale(float value) {
  _internal_set_benefit_revisit_scale(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.benefit_revisit_scale)
}

// float max_event_benefit = 8;
inline void MdlAdagradLightParameters::clear_max_event_benefit() {
  _impl_.max_event_benefit_ = 0;
}
inline float MdlAdagradLightParameters::_internal_max_event_benefit() const {
  return _impl_.max_event_benefit_;
}
inline float MdlAdagradLightParameters::max_event_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit)
  return _internal_max_event_benefit();
}
inline void MdlAdagradLightParameters::_internal_set_max_event_benefit(float value) {
  
  _impl_.max_event_benefit_ = value;
}
inline void MdlAdagradLightParameters::set_max_event_benefit(float value) {
  _internal_set_max_event_benefit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.max_event_benefit)
}

// float max_total_benefit = 9;
inline void MdlAdagradLightParameters::clear_max_total_benefit() {
  _impl_.max_total_benefit_ = 0;
}
inline float MdlAdagradLightParameters::_internal_max_total_benefit() const {
  return _impl_.max_total_benefit_;
}
inline float MdlAdagradLightParameters::max_total_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit)
  return _internal_max_total_benefit();
}
inline void MdlAdagradLightParameters::_internal_set_max_total_benefit(float value) {
  
  _impl_.max_total_benefit_ = value;
}
inline void MdlAdagradLightParameters::set_max_total_benefit(float value) {
  _internal_set_max_total_benefit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.max_total_benefit)
}

// float mdl_hard_limit = 10;
inline void MdlAdagradLightParameters::clear_mdl_hard_limit() {
  _impl_.mdl_hard_limit_ = 0;
}
inline float MdlAdagradLightParameters::_internal_mdl_hard_limit() const {
  return _impl_.mdl_hard_limit_;
}
inline float MdlAdagradLightParameters::mdl_hard_limit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit)
  return _internal_mdl_hard_limit();
}
inline void MdlAdagradLightParameters::_internal_set_mdl_hard_limit(float value) {
  
  _impl_.mdl_hard_limit_ = value;
}
inline void MdlAdagradLightParameters::set_mdl_hard_limit(float value) {
  _internal_set_mdl_hard_limit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_hard_limit)
}

// bool hard_limit_min_benefit = 11;
inline void MdlAdagradLightParameters::clear_hard_limit_min_benefit() {
  _impl_.hard_limit_min_benefit_ = false;
}
inline bool MdlAdagradLightParameters::_internal_hard_limit_min_benefit() const {
  return _impl_.hard_limit_min_benefit_;
}
inline bool MdlAdagradLightParameters::hard_limit_min_benefit() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit)
  return _internal_hard_limit_min_benefit();
}
inline void MdlAdagradLightParameters::_internal_set_hard_limit_min_benefit(bool value) {
  
  _impl_.hard_limit_min_benefit_ = value;
}
inline void MdlAdagradLightParameters::set_hard_limit_min_benefit(bool value) {
  _internal_set_hard_limit_min_benefit(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.hard_limit_min_benefit)
}

// bool mdl_regularize = 12;
inline void MdlAdagradLightParameters::clear_mdl_regularize() {
  _impl_.mdl_regularize_ = false;
}
inline bool MdlAdagradLightParameters::_internal_mdl_regularize() const {
  return _impl_.mdl_regularize_;
}
inline bool MdlAdagradLightParameters::mdl_regularize() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize)
  return _internal_mdl_regularize();
}
inline void MdlAdagradLightParameters::_internal_set_mdl_regularize(bool value) {
  
  _impl_.mdl_regularize_ = value;
}
inline void MdlAdagradLightParameters::set_mdl_regularize(bool value) {
  _internal_set_mdl_regularize(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.MdlAdagradLightParameters.mdl_regularize)
}

// -------------------------------------------------------------------

// AdadeltaParameters

// float rho = 1;
inline void AdadeltaParameters::clear_rho() {
  _impl_.rho_ = 0;
}
inline float AdadeltaParameters::_internal_rho() const {
  return _impl_.rho_;
}
inline float AdadeltaParameters::rho() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.rho)
  return _internal_rho();
}
inline void AdadeltaParameters::_internal_set_rho(float value) {
  
  _impl_.rho_ = value;
}
inline void AdadeltaParameters::set_rho(float value) {
  _internal_set_rho(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.rho)
}

// float epsilon = 2;
inline void AdadeltaParameters::clear_epsilon() {
  _impl_.epsilon_ = 0;
}
inline float AdadeltaParameters::_internal_epsilon() const {
  return _impl_.epsilon_;
}
inline float AdadeltaParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.AdadeltaParameters.epsilon)
  return _internal_epsilon();
}
inline void AdadeltaParameters::_internal_set_epsilon(float value) {
  
  _impl_.epsilon_ = value;
}
inline void AdadeltaParameters::set_epsilon(float value) {
  _internal_set_epsilon(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.AdadeltaParameters.epsilon)
}

// -------------------------------------------------------------------

// ProximalAdagradParameters

// float l1 = 1;
inline void ProximalAdagradParameters::clear_l1() {
  _impl_.l1_ = 0;
}
inline float ProximalAdagradParameters::_internal_l1() const {
  return _impl_.l1_;
}
inline float ProximalAdagradParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.l1)
  return _internal_l1();
}
inline void ProximalAdagradParameters::_internal_set_l1(float value) {
  
  _impl_.l1_ = value;
}
inline void ProximalAdagradParameters::set_l1(float value) {
  _internal_set_l1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.l1)
}

// float l2 = 2;
inline void ProximalAdagradParameters::clear_l2() {
  _impl_.l2_ = 0;
}
inline float ProximalAdagradParameters::_internal_l2() const {
  return _impl_.l2_;
}
inline float ProximalAdagradParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalAdagradParameters.l2)
  return _internal_l2();
}
inline void ProximalAdagradParameters::_internal_set_l2(float value) {
  
  _impl_.l2_ = value;
}
inline void ProximalAdagradParameters::set_l2(float value) {
  _internal_set_l2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalAdagradParameters.l2)
}

// -------------------------------------------------------------------

// OnlineYogiParameters

// float l1 = 1;
inline void OnlineYogiParameters::clear_l1() {
  _impl_.l1_ = 0;
}
inline float OnlineYogiParameters::_internal_l1() const {
  return _impl_.l1_;
}
inline float OnlineYogiParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OnlineYogiParameters.l1)
  return _internal_l1();
}
inline void OnlineYogiParameters::_internal_set_l1(float value) {
  
  _impl_.l1_ = value;
}
inline void OnlineYogiParameters::set_l1(float value) {
  _internal_set_l1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OnlineYogiParameters.l1)
}

// float l2 = 2;
inline void OnlineYogiParameters::clear_l2() {
  _impl_.l2_ = 0;
}
inline float OnlineYogiParameters::_internal_l2() const {
  return _impl_.l2_;
}
inline float OnlineYogiParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OnlineYogiParameters.l2)
  return _internal_l2();
}
inline void OnlineYogiParameters::_internal_set_l2(float value) {
  
  _impl_.l2_ = value;
}
inline void OnlineYogiParameters::set_l2(float value) {
  _internal_set_l2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OnlineYogiParameters.l2)
}

// float beta2 = 3;
inline void OnlineYogiParameters::clear_beta2() {
  _impl_.beta2_ = 0;
}
inline float OnlineYogiParameters::_internal_beta2() const {
  return _impl_.beta2_;
}
inline float OnlineYogiParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OnlineYogiParameters.beta2)
  return _internal_beta2();
}
inline void OnlineYogiParameters::_internal_set_beta2(float value) {
  
  _impl_.beta2_ = value;
}
inline void OnlineYogiParameters::set_beta2(float value) {
  _internal_set_beta2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OnlineYogiParameters.beta2)
}

// -------------------------------------------------------------------

// ProximalYogiParameters

// float l1 = 1;
inline void ProximalYogiParameters::clear_l1() {
  _impl_.l1_ = 0;
}
inline float ProximalYogiParameters::_internal_l1() const {
  return _impl_.l1_;
}
inline float ProximalYogiParameters::l1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.l1)
  return _internal_l1();
}
inline void ProximalYogiParameters::_internal_set_l1(float value) {
  
  _impl_.l1_ = value;
}
inline void ProximalYogiParameters::set_l1(float value) {
  _internal_set_l1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.l1)
}

// float l2 = 2;
inline void ProximalYogiParameters::clear_l2() {
  _impl_.l2_ = 0;
}
inline float ProximalYogiParameters::_internal_l2() const {
  return _impl_.l2_;
}
inline float ProximalYogiParameters::l2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.l2)
  return _internal_l2();
}
inline void ProximalYogiParameters::_internal_set_l2(float value) {
  
  _impl_.l2_ = value;
}
inline void ProximalYogiParameters::set_l2(float value) {
  _internal_set_l2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.l2)
}

// float beta1 = 3;
inline void ProximalYogiParameters::clear_beta1() {
  _impl_.beta1_ = 0;
}
inline float ProximalYogiParameters::_internal_beta1() const {
  return _impl_.beta1_;
}
inline float ProximalYogiParameters::beta1() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.beta1)
  return _internal_beta1();
}
inline void ProximalYogiParameters::_internal_set_beta1(float value) {
  
  _impl_.beta1_ = value;
}
inline void ProximalYogiParameters::set_beta1(float value) {
  _internal_set_beta1(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.beta1)
}

// float beta2 = 4;
inline void ProximalYogiParameters::clear_beta2() {
  _impl_.beta2_ = 0;
}
inline float ProximalYogiParameters::_internal_beta2() const {
  return _impl_.beta2_;
}
inline float ProximalYogiParameters::beta2() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.beta2)
  return _internal_beta2();
}
inline void ProximalYogiParameters::_internal_set_beta2(float value) {
  
  _impl_.beta2_ = value;
}
inline void ProximalYogiParameters::set_beta2(float value) {
  _internal_set_beta2(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.beta2)
}

// float epsilon = 5;
inline void ProximalYogiParameters::clear_epsilon() {
  _impl_.epsilon_ = 0;
}
inline float ProximalYogiParameters::_internal_epsilon() const {
  return _impl_.epsilon_;
}
inline float ProximalYogiParameters::epsilon() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.ProximalYogiParameters.epsilon)
  return _internal_epsilon();
}
inline void ProximalYogiParameters::_internal_set_epsilon(float value) {
  
  _impl_.epsilon_ = value;
}
inline void ProximalYogiParameters::set_epsilon(float value) {
  _internal_set_epsilon(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.ProximalYogiParameters.epsilon)
}

// -------------------------------------------------------------------

// FrequencyEstimatorParameters

// float tau = 1;
inline void FrequencyEstimatorParameters::clear_tau() {
  _impl_.tau_ = 0;
}
inline float FrequencyEstimatorParameters::_internal_tau() const {
  return _impl_.tau_;
}
inline float FrequencyEstimatorParameters::tau() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.tau)
  return _internal_tau();
}
inline void FrequencyEstimatorParameters::_internal_set_tau(float value) {
  
  _impl_.tau_ = value;
}
inline void FrequencyEstimatorParameters::set_tau(float value) {
  _internal_set_tau(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.tau)
}

// float max_delta = 2;
inline void FrequencyEstimatorParameters::clear_max_delta() {
  _impl_.max_delta_ = 0;
}
inline float FrequencyEstimatorParameters::_internal_max_delta() const {
  return _impl_.max_delta_;
}
inline float FrequencyEstimatorParameters::max_delta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.max_delta)
  return _internal_max_delta();
}
inline void FrequencyEstimatorParameters::_internal_set_max_delta(float value) {
  
  _impl_.max_delta_ = value;
}
inline void FrequencyEstimatorParameters::set_max_delta(float value) {
  _internal_set_max_delta(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.max_delta)
}

// float outlier_threshold = 3;
inline void FrequencyEstimatorParameters::clear_outlier_threshold() {
  _impl_.outlier_threshold_ = 0;
}
inline float FrequencyEstimatorParameters::_internal_outlier_threshold() const {
  return _impl_.outlier_threshold_;
}
inline float FrequencyEstimatorParameters::outlier_threshold() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.outlier_threshold)
  return _internal_outlier_threshold();
}
inline void FrequencyEstimatorParameters::_internal_set_outlier_threshold(float value) {
  
  _impl_.outlier_threshold_ = value;
}
inline void FrequencyEstimatorParameters::set_outlier_threshold(float value) {
  _internal_set_outlier_threshold(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.outlier_threshold)
}

// float weight_exponent = 4;
inline void FrequencyEstimatorParameters::clear_weight_exponent() {
  _impl_.weight_exponent_ = 0;
}
inline float FrequencyEstimatorParameters::_internal_weight_exponent() const {
  return _impl_.weight_exponent_;
}
inline float FrequencyEstimatorParameters::weight_exponent() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.FrequencyEstimatorParameters.weight_exponent)
  return _internal_weight_exponent();
}
inline void FrequencyEstimatorParameters::_internal_set_weight_exponent(float value) {
  
  _impl_.weight_exponent_ = value;
}
inline void FrequencyEstimatorParameters::set_weight_exponent(float value) {
  _internal_set_weight_exponent(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.FrequencyEstimatorParameters.weight_exponent)
}

// -------------------------------------------------------------------

// UserDefinedProgramParameters

// .xla.HloModuleProto program = 1;
inline bool UserDefinedProgramParameters::_internal_has_program() const {
  return this != internal_default_instance() && _impl_.program_ != nullptr;
}
inline bool UserDefinedProgramParameters::has_program() const {
  return _internal_has_program();
}
inline const ::xla::HloModuleProto& UserDefinedProgramParameters::_internal_program() const {
  const ::xla::HloModuleProto* p = _impl_.program_;
  return p != nullptr ? *p : reinterpret_cast<const ::xla::HloModuleProto&>(
      ::xla::_HloModuleProto_default_instance_);
}
inline const ::xla::HloModuleProto& UserDefinedProgramParameters::program() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.UserDefinedProgramParameters.program)
  return _internal_program();
}
inline void UserDefinedProgramParameters::unsafe_arena_set_allocated_program(
    ::xla::HloModuleProto* program) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.program_);
  }
  _impl_.program_ = program;
  if (program) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.UserDefinedProgramParameters.program)
}
inline ::xla::HloModuleProto* UserDefinedProgramParameters::release_program() {
  
  ::xla::HloModuleProto* temp = _impl_.program_;
  _impl_.program_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::xla::HloModuleProto* UserDefinedProgramParameters::unsafe_arena_release_program() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.UserDefinedProgramParameters.program)
  
  ::xla::HloModuleProto* temp = _impl_.program_;
  _impl_.program_ = nullptr;
  return temp;
}
inline ::xla::HloModuleProto* UserDefinedProgramParameters::_internal_mutable_program() {
  
  if (_impl_.program_ == nullptr) {
    auto* p = CreateMaybeMessage<::xla::HloModuleProto>(GetArenaForAllocation());
    _impl_.program_ = p;
  }
  return _impl_.program_;
}
inline ::xla::HloModuleProto* UserDefinedProgramParameters::mutable_program() {
  ::xla::HloModuleProto* _msg = _internal_mutable_program();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.UserDefinedProgramParameters.program)
  return _msg;
}
inline void UserDefinedProgramParameters::set_allocated_program(::xla::HloModuleProto* program) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.program_);
  }
  if (program) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(program));
    if (message_arena != submessage_arena) {
      program = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, program, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.program_ = program;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.UserDefinedProgramParameters.program)
}

// -------------------------------------------------------------------

// AssignParameters

// -------------------------------------------------------------------

// GradientAccumulationStatus

// -------------------------------------------------------------------

// LowDimensionalPackingStatus

// -------------------------------------------------------------------

// HotIdReplicationConfiguration

// .tensorflow.tpu.HotIdReplicationConfiguration.Status status = 1;
inline void HotIdReplicationConfiguration::clear_status() {
  _impl_.status_ = 0;
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration_Status HotIdReplicationConfiguration::_internal_status() const {
  return static_cast< ::tensorflow::tpu::HotIdReplicationConfiguration_Status >(_impl_.status_);
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration_Status HotIdReplicationConfiguration::status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.HotIdReplicationConfiguration.status)
  return _internal_status();
}
inline void HotIdReplicationConfiguration::_internal_set_status(::tensorflow::tpu::HotIdReplicationConfiguration_Status value) {
  
  _impl_.status_ = value;
}
inline void HotIdReplicationConfiguration::set_status(::tensorflow::tpu::HotIdReplicationConfiguration_Status value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.HotIdReplicationConfiguration.status)
}

// -------------------------------------------------------------------

// OptimizationParameters

// .tensorflow.tpu.LearningRate learning_rate = 13;
inline bool OptimizationParameters::_internal_has_learning_rate() const {
  return this != internal_default_instance() && _impl_.learning_rate_ != nullptr;
}
inline bool OptimizationParameters::has_learning_rate() const {
  return _internal_has_learning_rate();
}
inline void OptimizationParameters::clear_learning_rate() {
  if (GetArenaForAllocation() == nullptr && _impl_.learning_rate_ != nullptr) {
    delete _impl_.learning_rate_;
  }
  _impl_.learning_rate_ = nullptr;
}
inline const ::tensorflow::tpu::LearningRate& OptimizationParameters::_internal_learning_rate() const {
  const ::tensorflow::tpu::LearningRate* p = _impl_.learning_rate_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::LearningRate&>(
      ::tensorflow::tpu::_LearningRate_default_instance_);
}
inline const ::tensorflow::tpu::LearningRate& OptimizationParameters::learning_rate() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.learning_rate)
  return _internal_learning_rate();
}
inline void OptimizationParameters::unsafe_arena_set_allocated_learning_rate(
    ::tensorflow::tpu::LearningRate* learning_rate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.learning_rate_);
  }
  _impl_.learning_rate_ = learning_rate;
  if (learning_rate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.learning_rate)
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::release_learning_rate() {
  
  ::tensorflow::tpu::LearningRate* temp = _impl_.learning_rate_;
  _impl_.learning_rate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::unsafe_arena_release_learning_rate() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.learning_rate)
  
  ::tensorflow::tpu::LearningRate* temp = _impl_.learning_rate_;
  _impl_.learning_rate_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::_internal_mutable_learning_rate() {
  
  if (_impl_.learning_rate_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::LearningRate>(GetArenaForAllocation());
    _impl_.learning_rate_ = p;
  }
  return _impl_.learning_rate_;
}
inline ::tensorflow::tpu::LearningRate* OptimizationParameters::mutable_learning_rate() {
  ::tensorflow::tpu::LearningRate* _msg = _internal_mutable_learning_rate();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.learning_rate)
  return _msg;
}
inline void OptimizationParameters::set_allocated_learning_rate(::tensorflow::tpu::LearningRate* learning_rate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.learning_rate_;
  }
  if (learning_rate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(learning_rate);
    if (message_arena != submessage_arena) {
      learning_rate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, learning_rate, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.learning_rate_ = learning_rate;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.learning_rate)
}

// .tensorflow.tpu.ClippingLimits clipping_limits = 2;
inline bool OptimizationParameters::_internal_has_clipping_limits() const {
  return this != internal_default_instance() && _impl_.clipping_limits_ != nullptr;
}
inline bool OptimizationParameters::has_clipping_limits() const {
  return _internal_has_clipping_limits();
}
inline void OptimizationParameters::clear_clipping_limits() {
  if (GetArenaForAllocation() == nullptr && _impl_.clipping_limits_ != nullptr) {
    delete _impl_.clipping_limits_;
  }
  _impl_.clipping_limits_ = nullptr;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::_internal_clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = _impl_.clipping_limits_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::ClippingLimits&>(
      ::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::clipping_limits() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.clipping_limits)
  return _internal_clipping_limits();
}
inline void OptimizationParameters::unsafe_arena_set_allocated_clipping_limits(
    ::tensorflow::tpu::ClippingLimits* clipping_limits) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.clipping_limits_);
  }
  _impl_.clipping_limits_ = clipping_limits;
  if (clipping_limits) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.clipping_limits)
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::release_clipping_limits() {
  
  ::tensorflow::tpu::ClippingLimits* temp = _impl_.clipping_limits_;
  _impl_.clipping_limits_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::unsafe_arena_release_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = _impl_.clipping_limits_;
  _impl_.clipping_limits_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::_internal_mutable_clipping_limits() {
  
  if (_impl_.clipping_limits_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaForAllocation());
    _impl_.clipping_limits_ = p;
  }
  return _impl_.clipping_limits_;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::mutable_clipping_limits() {
  ::tensorflow::tpu::ClippingLimits* _msg = _internal_mutable_clipping_limits();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.clipping_limits)
  return _msg;
}
inline void OptimizationParameters::set_allocated_clipping_limits(::tensorflow::tpu::ClippingLimits* clipping_limits) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.clipping_limits_;
  }
  if (clipping_limits) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(clipping_limits);
    if (message_arena != submessage_arena) {
      clipping_limits = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.clipping_limits_ = clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.clipping_limits)
}

// .tensorflow.tpu.ClippingLimits gradient_clipping_limits = 7;
inline bool OptimizationParameters::_internal_has_gradient_clipping_limits() const {
  return this != internal_default_instance() && _impl_.gradient_clipping_limits_ != nullptr;
}
inline bool OptimizationParameters::has_gradient_clipping_limits() const {
  return _internal_has_gradient_clipping_limits();
}
inline void OptimizationParameters::clear_gradient_clipping_limits() {
  if (GetArenaForAllocation() == nullptr && _impl_.gradient_clipping_limits_ != nullptr) {
    delete _impl_.gradient_clipping_limits_;
  }
  _impl_.gradient_clipping_limits_ = nullptr;
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::_internal_gradient_clipping_limits() const {
  const ::tensorflow::tpu::ClippingLimits* p = _impl_.gradient_clipping_limits_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::ClippingLimits&>(
      ::tensorflow::tpu::_ClippingLimits_default_instance_);
}
inline const ::tensorflow::tpu::ClippingLimits& OptimizationParameters::gradient_clipping_limits() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  return _internal_gradient_clipping_limits();
}
inline void OptimizationParameters::unsafe_arena_set_allocated_gradient_clipping_limits(
    ::tensorflow::tpu::ClippingLimits* gradient_clipping_limits) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.gradient_clipping_limits_);
  }
  _impl_.gradient_clipping_limits_ = gradient_clipping_limits;
  if (gradient_clipping_limits) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::release_gradient_clipping_limits() {
  
  ::tensorflow::tpu::ClippingLimits* temp = _impl_.gradient_clipping_limits_;
  _impl_.gradient_clipping_limits_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::unsafe_arena_release_gradient_clipping_limits() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  
  ::tensorflow::tpu::ClippingLimits* temp = _impl_.gradient_clipping_limits_;
  _impl_.gradient_clipping_limits_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::_internal_mutable_gradient_clipping_limits() {
  
  if (_impl_.gradient_clipping_limits_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::ClippingLimits>(GetArenaForAllocation());
    _impl_.gradient_clipping_limits_ = p;
  }
  return _impl_.gradient_clipping_limits_;
}
inline ::tensorflow::tpu::ClippingLimits* OptimizationParameters::mutable_gradient_clipping_limits() {
  ::tensorflow::tpu::ClippingLimits* _msg = _internal_mutable_gradient_clipping_limits();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
  return _msg;
}
inline void OptimizationParameters::set_allocated_gradient_clipping_limits(::tensorflow::tpu::ClippingLimits* gradient_clipping_limits) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.gradient_clipping_limits_;
  }
  if (gradient_clipping_limits) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(gradient_clipping_limits);
    if (message_arena != submessage_arena) {
      gradient_clipping_limits = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, gradient_clipping_limits, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.gradient_clipping_limits_ = gradient_clipping_limits;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.gradient_clipping_limits)
}

// float weight_decay_factor = 16;
inline void OptimizationParameters::clear_weight_decay_factor() {
  _impl_.weight_decay_factor_ = 0;
}
inline float OptimizationParameters::_internal_weight_decay_factor() const {
  return _impl_.weight_decay_factor_;
}
inline float OptimizationParameters::weight_decay_factor() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.weight_decay_factor)
  return _internal_weight_decay_factor();
}
inline void OptimizationParameters::_internal_set_weight_decay_factor(float value) {
  
  _impl_.weight_decay_factor_ = value;
}
inline void OptimizationParameters::set_weight_decay_factor(float value) {
  _internal_set_weight_decay_factor(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.weight_decay_factor)
}

// bool multiply_weight_decay_factor_by_learning_rate = 22;
inline void OptimizationParameters::clear_multiply_weight_decay_factor_by_learning_rate() {
  _impl_.multiply_weight_decay_factor_by_learning_rate_ = false;
}
inline bool OptimizationParameters::_internal_multiply_weight_decay_factor_by_learning_rate() const {
  return _impl_.multiply_weight_decay_factor_by_learning_rate_;
}
inline bool OptimizationParameters::multiply_weight_decay_factor_by_learning_rate() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.multiply_weight_decay_factor_by_learning_rate)
  return _internal_multiply_weight_decay_factor_by_learning_rate();
}
inline void OptimizationParameters::_internal_set_multiply_weight_decay_factor_by_learning_rate(bool value) {
  
  _impl_.multiply_weight_decay_factor_by_learning_rate_ = value;
}
inline void OptimizationParameters::set_multiply_weight_decay_factor_by_learning_rate(bool value) {
  _internal_set_multiply_weight_decay_factor_by_learning_rate(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.multiply_weight_decay_factor_by_learning_rate)
}

// .tensorflow.tpu.SimulatedQuantization simulated_quantization = 27;
inline bool OptimizationParameters::_internal_has_simulated_quantization() const {
  return this != internal_default_instance() && _impl_.simulated_quantization_ != nullptr;
}
inline bool OptimizationParameters::has_simulated_quantization() const {
  return _internal_has_simulated_quantization();
}
inline void OptimizationParameters::clear_simulated_quantization() {
  if (GetArenaForAllocation() == nullptr && _impl_.simulated_quantization_ != nullptr) {
    delete _impl_.simulated_quantization_;
  }
  _impl_.simulated_quantization_ = nullptr;
}
inline const ::tensorflow::tpu::SimulatedQuantization& OptimizationParameters::_internal_simulated_quantization() const {
  const ::tensorflow::tpu::SimulatedQuantization* p = _impl_.simulated_quantization_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::SimulatedQuantization&>(
      ::tensorflow::tpu::_SimulatedQuantization_default_instance_);
}
inline const ::tensorflow::tpu::SimulatedQuantization& OptimizationParameters::simulated_quantization() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.simulated_quantization)
  return _internal_simulated_quantization();
}
inline void OptimizationParameters::unsafe_arena_set_allocated_simulated_quantization(
    ::tensorflow::tpu::SimulatedQuantization* simulated_quantization) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.simulated_quantization_);
  }
  _impl_.simulated_quantization_ = simulated_quantization;
  if (simulated_quantization) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.simulated_quantization)
}
inline ::tensorflow::tpu::SimulatedQuantization* OptimizationParameters::release_simulated_quantization() {
  
  ::tensorflow::tpu::SimulatedQuantization* temp = _impl_.simulated_quantization_;
  _impl_.simulated_quantization_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::SimulatedQuantization* OptimizationParameters::unsafe_arena_release_simulated_quantization() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.simulated_quantization)
  
  ::tensorflow::tpu::SimulatedQuantization* temp = _impl_.simulated_quantization_;
  _impl_.simulated_quantization_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::SimulatedQuantization* OptimizationParameters::_internal_mutable_simulated_quantization() {
  
  if (_impl_.simulated_quantization_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::SimulatedQuantization>(GetArenaForAllocation());
    _impl_.simulated_quantization_ = p;
  }
  return _impl_.simulated_quantization_;
}
inline ::tensorflow::tpu::SimulatedQuantization* OptimizationParameters::mutable_simulated_quantization() {
  ::tensorflow::tpu::SimulatedQuantization* _msg = _internal_mutable_simulated_quantization();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.simulated_quantization)
  return _msg;
}
inline void OptimizationParameters::set_allocated_simulated_quantization(::tensorflow::tpu::SimulatedQuantization* simulated_quantization) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.simulated_quantization_;
  }
  if (simulated_quantization) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(simulated_quantization);
    if (message_arena != submessage_arena) {
      simulated_quantization = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, simulated_quantization, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.simulated_quantization_ = simulated_quantization;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.simulated_quantization)
}

// .tensorflow.tpu.GradientAccumulationStatus.Status gradient_accumulation_status = 17;
inline void OptimizationParameters::clear_gradient_accumulation_status() {
  _impl_.gradient_accumulation_status_ = 0;
}
inline ::tensorflow::tpu::GradientAccumulationStatus_Status OptimizationParameters::_internal_gradient_accumulation_status() const {
  return static_cast< ::tensorflow::tpu::GradientAccumulationStatus_Status >(_impl_.gradient_accumulation_status_);
}
inline ::tensorflow::tpu::GradientAccumulationStatus_Status OptimizationParameters::gradient_accumulation_status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.gradient_accumulation_status)
  return _internal_gradient_accumulation_status();
}
inline void OptimizationParameters::_internal_set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value) {
  
  _impl_.gradient_accumulation_status_ = value;
}
inline void OptimizationParameters::set_gradient_accumulation_status(::tensorflow::tpu::GradientAccumulationStatus_Status value) {
  _internal_set_gradient_accumulation_status(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.gradient_accumulation_status)
}

// .tensorflow.tpu.LowDimensionalPackingStatus.Status low_dimensional_packing_status = 28;
inline void OptimizationParameters::clear_low_dimensional_packing_status() {
  _impl_.low_dimensional_packing_status_ = 0;
}
inline ::tensorflow::tpu::LowDimensionalPackingStatus_Status OptimizationParameters::_internal_low_dimensional_packing_status() const {
  return static_cast< ::tensorflow::tpu::LowDimensionalPackingStatus_Status >(_impl_.low_dimensional_packing_status_);
}
inline ::tensorflow::tpu::LowDimensionalPackingStatus_Status OptimizationParameters::low_dimensional_packing_status() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.low_dimensional_packing_status)
  return _internal_low_dimensional_packing_status();
}
inline void OptimizationParameters::_internal_set_low_dimensional_packing_status(::tensorflow::tpu::LowDimensionalPackingStatus_Status value) {
  
  _impl_.low_dimensional_packing_status_ = value;
}
inline void OptimizationParameters::set_low_dimensional_packing_status(::tensorflow::tpu::LowDimensionalPackingStatus_Status value) {
  _internal_set_low_dimensional_packing_status(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.OptimizationParameters.low_dimensional_packing_status)
}

// .tensorflow.tpu.HotIdReplicationConfiguration hot_id_replication_configuration = 18;
inline bool OptimizationParameters::_internal_has_hot_id_replication_configuration() const {
  return this != internal_default_instance() && _impl_.hot_id_replication_configuration_ != nullptr;
}
inline bool OptimizationParameters::has_hot_id_replication_configuration() const {
  return _internal_has_hot_id_replication_configuration();
}
inline void OptimizationParameters::clear_hot_id_replication_configuration() {
  if (GetArenaForAllocation() == nullptr && _impl_.hot_id_replication_configuration_ != nullptr) {
    delete _impl_.hot_id_replication_configuration_;
  }
  _impl_.hot_id_replication_configuration_ = nullptr;
}
inline const ::tensorflow::tpu::HotIdReplicationConfiguration& OptimizationParameters::_internal_hot_id_replication_configuration() const {
  const ::tensorflow::tpu::HotIdReplicationConfiguration* p = _impl_.hot_id_replication_configuration_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::tpu::HotIdReplicationConfiguration&>(
      ::tensorflow::tpu::_HotIdReplicationConfiguration_default_instance_);
}
inline const ::tensorflow::tpu::HotIdReplicationConfiguration& OptimizationParameters::hot_id_replication_configuration() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
  return _internal_hot_id_replication_configuration();
}
inline void OptimizationParameters::unsafe_arena_set_allocated_hot_id_replication_configuration(
    ::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hot_id_replication_configuration_);
  }
  _impl_.hot_id_replication_configuration_ = hot_id_replication_configuration;
  if (hot_id_replication_configuration) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration* OptimizationParameters::release_hot_id_replication_configuration() {
  
  ::tensorflow::tpu::HotIdReplicationConfiguration* temp = _impl_.hot_id_replication_configuration_;
  _impl_.hot_id_replication_configuration_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration* OptimizationParameters::unsafe_arena_release_hot_id_replication_configuration() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
  
  ::tensorflow::tpu::HotIdReplicationConfiguration* temp = _impl_.hot_id_replication_configuration_;
  _impl_.hot_id_replication_configuration_ = nullptr;
  return temp;
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration* OptimizationParameters::_internal_mutable_hot_id_replication_configuration() {
  
  if (_impl_.hot_id_replication_configuration_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::tpu::HotIdReplicationConfiguration>(GetArenaForAllocation());
    _impl_.hot_id_replication_configuration_ = p;
  }
  return _impl_.hot_id_replication_configuration_;
}
inline ::tensorflow::tpu::HotIdReplicationConfiguration* OptimizationParameters::mutable_hot_id_replication_configuration() {
  ::tensorflow::tpu::HotIdReplicationConfiguration* _msg = _internal_mutable_hot_id_replication_configuration();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
  return _msg;
}
inline void OptimizationParameters::set_allocated_hot_id_replication_configuration(::tensorflow::tpu::HotIdReplicationConfiguration* hot_id_replication_configuration) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.hot_id_replication_configuration_;
  }
  if (hot_id_replication_configuration) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(hot_id_replication_configuration);
    if (message_arena != submessage_arena) {
      hot_id_replication_configuration = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hot_id_replication_configuration, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.hot_id_replication_configuration_ = hot_id_replication_configuration;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.OptimizationParameters.hot_id_replication_configuration)
}

// .tensorflow.tpu.AdagradParameters adagrad = 3;
inline bool OptimizationParameters::_internal_has_adagrad() const {
  return parameters_case() == kAdagrad;
}
inline bool OptimizationParameters::has_adagrad() const {
  return _internal_has_adagrad();
}
inline void OptimizationParameters::set_has_adagrad() {
  _impl_._oneof_case_[0] = kAdagrad;
}
inline void OptimizationParameters::clear_adagrad() {
  if (_internal_has_adagrad()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.adagrad_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::release_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adagrad)
  if (_internal_has_adagrad()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdagradParameters* temp = _impl_.parameters_.adagrad_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdagradParameters& OptimizationParameters::_internal_adagrad() const {
  return _internal_has_adagrad()
      ? *_impl_.parameters_.adagrad_
      : reinterpret_cast< ::tensorflow::tpu::AdagradParameters&>(::tensorflow::tpu::_AdagradParameters_default_instance_);
}
inline const ::tensorflow::tpu::AdagradParameters& OptimizationParameters::adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adagrad)
  return _internal_adagrad();
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::unsafe_arena_release_adagrad() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.adagrad)
  if (_internal_has_adagrad()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdagradParameters* temp = _impl_.parameters_.adagrad_;
    _impl_.parameters_.adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_adagrad(::tensorflow::tpu::AdagradParameters* adagrad) {
  clear_parameters();
  if (adagrad) {
    set_has_adagrad();
    _impl_.parameters_.adagrad_ = adagrad;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.adagrad)
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::_internal_mutable_adagrad() {
  if (!_internal_has_adagrad()) {
    clear_parameters();
    set_has_adagrad();
    _impl_.parameters_.adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::AdagradParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.adagrad_;
}
inline ::tensorflow::tpu::AdagradParameters* OptimizationParameters::mutable_adagrad() {
  ::tensorflow::tpu::AdagradParameters* _msg = _internal_mutable_adagrad();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adagrad)
  return _msg;
}

// .tensorflow.tpu.AdagradMomentumParameters adagrad_momentum = 26;
inline bool OptimizationParameters::_internal_has_adagrad_momentum() const {
  return parameters_case() == kAdagradMomentum;
}
inline bool OptimizationParameters::has_adagrad_momentum() const {
  return _internal_has_adagrad_momentum();
}
inline void OptimizationParameters::set_has_adagrad_momentum() {
  _impl_._oneof_case_[0] = kAdagradMomentum;
}
inline void OptimizationParameters::clear_adagrad_momentum() {
  if (_internal_has_adagrad_momentum()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.adagrad_momentum_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdagradMomentumParameters* OptimizationParameters::release_adagrad_momentum() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adagrad_momentum)
  if (_internal_has_adagrad_momentum()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdagradMomentumParameters* temp = _impl_.parameters_.adagrad_momentum_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.adagrad_momentum_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdagradMomentumParameters& OptimizationParameters::_internal_adagrad_momentum() const {
  return _internal_has_adagrad_momentum()
      ? *_impl_.parameters_.adagrad_momentum_
      : reinterpret_cast< ::tensorflow::tpu::AdagradMomentumParameters&>(::tensorflow::tpu::_AdagradMomentumParameters_default_instance_);
}
inline const ::tensorflow::tpu::AdagradMomentumParameters& OptimizationParameters::adagrad_momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adagrad_momentum)
  return _internal_adagrad_momentum();
}
inline ::tensorflow::tpu::AdagradMomentumParameters* OptimizationParameters::unsafe_arena_release_adagrad_momentum() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.adagrad_momentum)
  if (_internal_has_adagrad_momentum()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdagradMomentumParameters* temp = _impl_.parameters_.adagrad_momentum_;
    _impl_.parameters_.adagrad_momentum_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_adagrad_momentum(::tensorflow::tpu::AdagradMomentumParameters* adagrad_momentum) {
  clear_parameters();
  if (adagrad_momentum) {
    set_has_adagrad_momentum();
    _impl_.parameters_.adagrad_momentum_ = adagrad_momentum;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.adagrad_momentum)
}
inline ::tensorflow::tpu::AdagradMomentumParameters* OptimizationParameters::_internal_mutable_adagrad_momentum() {
  if (!_internal_has_adagrad_momentum()) {
    clear_parameters();
    set_has_adagrad_momentum();
    _impl_.parameters_.adagrad_momentum_ = CreateMaybeMessage< ::tensorflow::tpu::AdagradMomentumParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.adagrad_momentum_;
}
inline ::tensorflow::tpu::AdagradMomentumParameters* OptimizationParameters::mutable_adagrad_momentum() {
  ::tensorflow::tpu::AdagradMomentumParameters* _msg = _internal_mutable_adagrad_momentum();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adagrad_momentum)
  return _msg;
}

// .tensorflow.tpu.BoundedAdagradParameters bounded_adagrad = 19;
inline bool OptimizationParameters::_internal_has_bounded_adagrad() const {
  return parameters_case() == kBoundedAdagrad;
}
inline bool OptimizationParameters::has_bounded_adagrad() const {
  return _internal_has_bounded_adagrad();
}
inline void OptimizationParameters::set_has_bounded_adagrad() {
  _impl_._oneof_case_[0] = kBoundedAdagrad;
}
inline void OptimizationParameters::clear_bounded_adagrad() {
  if (_internal_has_bounded_adagrad()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.bounded_adagrad_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::BoundedAdagradParameters* OptimizationParameters::release_bounded_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  if (_internal_has_bounded_adagrad()) {
    clear_has_parameters();
    ::tensorflow::tpu::BoundedAdagradParameters* temp = _impl_.parameters_.bounded_adagrad_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.bounded_adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::BoundedAdagradParameters& OptimizationParameters::_internal_bounded_adagrad() const {
  return _internal_has_bounded_adagrad()
      ? *_impl_.parameters_.bounded_adagrad_
      : reinterpret_cast< ::tensorflow::tpu::BoundedAdagradParameters&>(::tensorflow::tpu::_BoundedAdagradParameters_default_instance_);
}
inline const ::tensorflow::tpu::BoundedAdagradParameters& OptimizationParameters::bounded_adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  return _internal_bounded_adagrad();
}
inline ::tensorflow::tpu::BoundedAdagradParameters* OptimizationParameters::unsafe_arena_release_bounded_adagrad() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  if (_internal_has_bounded_adagrad()) {
    clear_has_parameters();
    ::tensorflow::tpu::BoundedAdagradParameters* temp = _impl_.parameters_.bounded_adagrad_;
    _impl_.parameters_.bounded_adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_bounded_adagrad(::tensorflow::tpu::BoundedAdagradParameters* bounded_adagrad) {
  clear_parameters();
  if (bounded_adagrad) {
    set_has_bounded_adagrad();
    _impl_.parameters_.bounded_adagrad_ = bounded_adagrad;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
}
inline ::tensorflow::tpu::BoundedAdagradParameters* OptimizationParameters::_internal_mutable_bounded_adagrad() {
  if (!_internal_has_bounded_adagrad()) {
    clear_parameters();
    set_has_bounded_adagrad();
    _impl_.parameters_.bounded_adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::BoundedAdagradParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.bounded_adagrad_;
}
inline ::tensorflow::tpu::BoundedAdagradParameters* OptimizationParameters::mutable_bounded_adagrad() {
  ::tensorflow::tpu::BoundedAdagradParameters* _msg = _internal_mutable_bounded_adagrad();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.bounded_adagrad)
  return _msg;
}

// .tensorflow.tpu.StochasticGradientDescentParameters stochastic_gradient_descent = 4;
inline bool OptimizationParameters::_internal_has_stochastic_gradient_descent() const {
  return parameters_case() == kStochasticGradientDescent;
}
inline bool OptimizationParameters::has_stochastic_gradient_descent() const {
  return _internal_has_stochastic_gradient_descent();
}
inline void OptimizationParameters::set_has_stochastic_gradient_descent() {
  _impl_._oneof_case_[0] = kStochasticGradientDescent;
}
inline void OptimizationParameters::clear_stochastic_gradient_descent() {
  if (_internal_has_stochastic_gradient_descent()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.stochastic_gradient_descent_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::release_stochastic_gradient_descent() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  if (_internal_has_stochastic_gradient_descent()) {
    clear_has_parameters();
    ::tensorflow::tpu::StochasticGradientDescentParameters* temp = _impl_.parameters_.stochastic_gradient_descent_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.stochastic_gradient_descent_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::StochasticGradientDescentParameters& OptimizationParameters::_internal_stochastic_gradient_descent() const {
  return _internal_has_stochastic_gradient_descent()
      ? *_impl_.parameters_.stochastic_gradient_descent_
      : reinterpret_cast< ::tensorflow::tpu::StochasticGradientDescentParameters&>(::tensorflow::tpu::_StochasticGradientDescentParameters_default_instance_);
}
inline const ::tensorflow::tpu::StochasticGradientDescentParameters& OptimizationParameters::stochastic_gradient_descent() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  return _internal_stochastic_gradient_descent();
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::unsafe_arena_release_stochastic_gradient_descent() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  if (_internal_has_stochastic_gradient_descent()) {
    clear_has_parameters();
    ::tensorflow::tpu::StochasticGradientDescentParameters* temp = _impl_.parameters_.stochastic_gradient_descent_;
    _impl_.parameters_.stochastic_gradient_descent_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_stochastic_gradient_descent(::tensorflow::tpu::StochasticGradientDescentParameters* stochastic_gradient_descent) {
  clear_parameters();
  if (stochastic_gradient_descent) {
    set_has_stochastic_gradient_descent();
    _impl_.parameters_.stochastic_gradient_descent_ = stochastic_gradient_descent;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::_internal_mutable_stochastic_gradient_descent() {
  if (!_internal_has_stochastic_gradient_descent()) {
    clear_parameters();
    set_has_stochastic_gradient_descent();
    _impl_.parameters_.stochastic_gradient_descent_ = CreateMaybeMessage< ::tensorflow::tpu::StochasticGradientDescentParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.stochastic_gradient_descent_;
}
inline ::tensorflow::tpu::StochasticGradientDescentParameters* OptimizationParameters::mutable_stochastic_gradient_descent() {
  ::tensorflow::tpu::StochasticGradientDescentParameters* _msg = _internal_mutable_stochastic_gradient_descent();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.stochastic_gradient_descent)
  return _msg;
}

// .tensorflow.tpu.FtrlParameters ftrl = 5;
inline bool OptimizationParameters::_internal_has_ftrl() const {
  return parameters_case() == kFtrl;
}
inline bool OptimizationParameters::has_ftrl() const {
  return _internal_has_ftrl();
}
inline void OptimizationParameters::set_has_ftrl() {
  _impl_._oneof_case_[0] = kFtrl;
}
inline void OptimizationParameters::clear_ftrl() {
  if (_internal_has_ftrl()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.ftrl_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::release_ftrl() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.ftrl)
  if (_internal_has_ftrl()) {
    clear_has_parameters();
    ::tensorflow::tpu::FtrlParameters* temp = _impl_.parameters_.ftrl_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.ftrl_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::FtrlParameters& OptimizationParameters::_internal_ftrl() const {
  return _internal_has_ftrl()
      ? *_impl_.parameters_.ftrl_
      : reinterpret_cast< ::tensorflow::tpu::FtrlParameters&>(::tensorflow::tpu::_FtrlParameters_default_instance_);
}
inline const ::tensorflow::tpu::FtrlParameters& OptimizationParameters::ftrl() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.ftrl)
  return _internal_ftrl();
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::unsafe_arena_release_ftrl() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.ftrl)
  if (_internal_has_ftrl()) {
    clear_has_parameters();
    ::tensorflow::tpu::FtrlParameters* temp = _impl_.parameters_.ftrl_;
    _impl_.parameters_.ftrl_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_ftrl(::tensorflow::tpu::FtrlParameters* ftrl) {
  clear_parameters();
  if (ftrl) {
    set_has_ftrl();
    _impl_.parameters_.ftrl_ = ftrl;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.ftrl)
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::_internal_mutable_ftrl() {
  if (!_internal_has_ftrl()) {
    clear_parameters();
    set_has_ftrl();
    _impl_.parameters_.ftrl_ = CreateMaybeMessage< ::tensorflow::tpu::FtrlParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.ftrl_;
}
inline ::tensorflow::tpu::FtrlParameters* OptimizationParameters::mutable_ftrl() {
  ::tensorflow::tpu::FtrlParameters* _msg = _internal_mutable_ftrl();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.ftrl)
  return _msg;
}

// .tensorflow.tpu.AdamParameters adam = 6;
inline bool OptimizationParameters::_internal_has_adam() const {
  return parameters_case() == kAdam;
}
inline bool OptimizationParameters::has_adam() const {
  return _internal_has_adam();
}
inline void OptimizationParameters::set_has_adam() {
  _impl_._oneof_case_[0] = kAdam;
}
inline void OptimizationParameters::clear_adam() {
  if (_internal_has_adam()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.adam_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::release_adam() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adam)
  if (_internal_has_adam()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdamParameters* temp = _impl_.parameters_.adam_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.adam_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdamParameters& OptimizationParameters::_internal_adam() const {
  return _internal_has_adam()
      ? *_impl_.parameters_.adam_
      : reinterpret_cast< ::tensorflow::tpu::AdamParameters&>(::tensorflow::tpu::_AdamParameters_default_instance_);
}
inline const ::tensorflow::tpu::AdamParameters& OptimizationParameters::adam() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adam)
  return _internal_adam();
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::unsafe_arena_release_adam() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.adam)
  if (_internal_has_adam()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdamParameters* temp = _impl_.parameters_.adam_;
    _impl_.parameters_.adam_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_adam(::tensorflow::tpu::AdamParameters* adam) {
  clear_parameters();
  if (adam) {
    set_has_adam();
    _impl_.parameters_.adam_ = adam;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.adam)
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::_internal_mutable_adam() {
  if (!_internal_has_adam()) {
    clear_parameters();
    set_has_adam();
    _impl_.parameters_.adam_ = CreateMaybeMessage< ::tensorflow::tpu::AdamParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.adam_;
}
inline ::tensorflow::tpu::AdamParameters* OptimizationParameters::mutable_adam() {
  ::tensorflow::tpu::AdamParameters* _msg = _internal_mutable_adam();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adam)
  return _msg;
}

// .tensorflow.tpu.MomentumParameters momentum = 8;
inline bool OptimizationParameters::_internal_has_momentum() const {
  return parameters_case() == kMomentum;
}
inline bool OptimizationParameters::has_momentum() const {
  return _internal_has_momentum();
}
inline void OptimizationParameters::set_has_momentum() {
  _impl_._oneof_case_[0] = kMomentum;
}
inline void OptimizationParameters::clear_momentum() {
  if (_internal_has_momentum()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.momentum_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::release_momentum() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.momentum)
  if (_internal_has_momentum()) {
    clear_has_parameters();
    ::tensorflow::tpu::MomentumParameters* temp = _impl_.parameters_.momentum_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.momentum_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::MomentumParameters& OptimizationParameters::_internal_momentum() const {
  return _internal_has_momentum()
      ? *_impl_.parameters_.momentum_
      : reinterpret_cast< ::tensorflow::tpu::MomentumParameters&>(::tensorflow::tpu::_MomentumParameters_default_instance_);
}
inline const ::tensorflow::tpu::MomentumParameters& OptimizationParameters::momentum() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.momentum)
  return _internal_momentum();
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::unsafe_arena_release_momentum() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.momentum)
  if (_internal_has_momentum()) {
    clear_has_parameters();
    ::tensorflow::tpu::MomentumParameters* temp = _impl_.parameters_.momentum_;
    _impl_.parameters_.momentum_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_momentum(::tensorflow::tpu::MomentumParameters* momentum) {
  clear_parameters();
  if (momentum) {
    set_has_momentum();
    _impl_.parameters_.momentum_ = momentum;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.momentum)
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::_internal_mutable_momentum() {
  if (!_internal_has_momentum()) {
    clear_parameters();
    set_has_momentum();
    _impl_.parameters_.momentum_ = CreateMaybeMessage< ::tensorflow::tpu::MomentumParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.momentum_;
}
inline ::tensorflow::tpu::MomentumParameters* OptimizationParameters::mutable_momentum() {
  ::tensorflow::tpu::MomentumParameters* _msg = _internal_mutable_momentum();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.momentum)
  return _msg;
}

// .tensorflow.tpu.LionParameters lion = 29;
inline bool OptimizationParameters::_internal_has_lion() const {
  return parameters_case() == kLion;
}
inline bool OptimizationParameters::has_lion() const {
  return _internal_has_lion();
}
inline void OptimizationParameters::set_has_lion() {
  _impl_._oneof_case_[0] = kLion;
}
inline void OptimizationParameters::clear_lion() {
  if (_internal_has_lion()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.lion_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::LionParameters* OptimizationParameters::release_lion() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.lion)
  if (_internal_has_lion()) {
    clear_has_parameters();
    ::tensorflow::tpu::LionParameters* temp = _impl_.parameters_.lion_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.lion_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::LionParameters& OptimizationParameters::_internal_lion() const {
  return _internal_has_lion()
      ? *_impl_.parameters_.lion_
      : reinterpret_cast< ::tensorflow::tpu::LionParameters&>(::tensorflow::tpu::_LionParameters_default_instance_);
}
inline const ::tensorflow::tpu::LionParameters& OptimizationParameters::lion() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.lion)
  return _internal_lion();
}
inline ::tensorflow::tpu::LionParameters* OptimizationParameters::unsafe_arena_release_lion() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.lion)
  if (_internal_has_lion()) {
    clear_has_parameters();
    ::tensorflow::tpu::LionParameters* temp = _impl_.parameters_.lion_;
    _impl_.parameters_.lion_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_lion(::tensorflow::tpu::LionParameters* lion) {
  clear_parameters();
  if (lion) {
    set_has_lion();
    _impl_.parameters_.lion_ = lion;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.lion)
}
inline ::tensorflow::tpu::LionParameters* OptimizationParameters::_internal_mutable_lion() {
  if (!_internal_has_lion()) {
    clear_parameters();
    set_has_lion();
    _impl_.parameters_.lion_ = CreateMaybeMessage< ::tensorflow::tpu::LionParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.lion_;
}
inline ::tensorflow::tpu::LionParameters* OptimizationParameters::mutable_lion() {
  ::tensorflow::tpu::LionParameters* _msg = _internal_mutable_lion();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.lion)
  return _msg;
}

// .tensorflow.tpu.RmsPropParameters rms_prop = 9;
inline bool OptimizationParameters::_internal_has_rms_prop() const {
  return parameters_case() == kRmsProp;
}
inline bool OptimizationParameters::has_rms_prop() const {
  return _internal_has_rms_prop();
}
inline void OptimizationParameters::set_has_rms_prop() {
  _impl_._oneof_case_[0] = kRmsProp;
}
inline void OptimizationParameters::clear_rms_prop() {
  if (_internal_has_rms_prop()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.rms_prop_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::release_rms_prop() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.rms_prop)
  if (_internal_has_rms_prop()) {
    clear_has_parameters();
    ::tensorflow::tpu::RmsPropParameters* temp = _impl_.parameters_.rms_prop_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.rms_prop_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::RmsPropParameters& OptimizationParameters::_internal_rms_prop() const {
  return _internal_has_rms_prop()
      ? *_impl_.parameters_.rms_prop_
      : reinterpret_cast< ::tensorflow::tpu::RmsPropParameters&>(::tensorflow::tpu::_RmsPropParameters_default_instance_);
}
inline const ::tensorflow::tpu::RmsPropParameters& OptimizationParameters::rms_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.rms_prop)
  return _internal_rms_prop();
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::unsafe_arena_release_rms_prop() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.rms_prop)
  if (_internal_has_rms_prop()) {
    clear_has_parameters();
    ::tensorflow::tpu::RmsPropParameters* temp = _impl_.parameters_.rms_prop_;
    _impl_.parameters_.rms_prop_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_rms_prop(::tensorflow::tpu::RmsPropParameters* rms_prop) {
  clear_parameters();
  if (rms_prop) {
    set_has_rms_prop();
    _impl_.parameters_.rms_prop_ = rms_prop;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.rms_prop)
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::_internal_mutable_rms_prop() {
  if (!_internal_has_rms_prop()) {
    clear_parameters();
    set_has_rms_prop();
    _impl_.parameters_.rms_prop_ = CreateMaybeMessage< ::tensorflow::tpu::RmsPropParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.rms_prop_;
}
inline ::tensorflow::tpu::RmsPropParameters* OptimizationParameters::mutable_rms_prop() {
  ::tensorflow::tpu::RmsPropParameters* _msg = _internal_mutable_rms_prop();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.rms_prop)
  return _msg;
}

// .tensorflow.tpu.CenteredRmsPropParameters centered_rms_prop = 10;
inline bool OptimizationParameters::_internal_has_centered_rms_prop() const {
  return parameters_case() == kCenteredRmsProp;
}
inline bool OptimizationParameters::has_centered_rms_prop() const {
  return _internal_has_centered_rms_prop();
}
inline void OptimizationParameters::set_has_centered_rms_prop() {
  _impl_._oneof_case_[0] = kCenteredRmsProp;
}
inline void OptimizationParameters::clear_centered_rms_prop() {
  if (_internal_has_centered_rms_prop()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.centered_rms_prop_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::release_centered_rms_prop() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  if (_internal_has_centered_rms_prop()) {
    clear_has_parameters();
    ::tensorflow::tpu::CenteredRmsPropParameters* temp = _impl_.parameters_.centered_rms_prop_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.centered_rms_prop_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::CenteredRmsPropParameters& OptimizationParameters::_internal_centered_rms_prop() const {
  return _internal_has_centered_rms_prop()
      ? *_impl_.parameters_.centered_rms_prop_
      : reinterpret_cast< ::tensorflow::tpu::CenteredRmsPropParameters&>(::tensorflow::tpu::_CenteredRmsPropParameters_default_instance_);
}
inline const ::tensorflow::tpu::CenteredRmsPropParameters& OptimizationParameters::centered_rms_prop() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  return _internal_centered_rms_prop();
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::unsafe_arena_release_centered_rms_prop() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  if (_internal_has_centered_rms_prop()) {
    clear_has_parameters();
    ::tensorflow::tpu::CenteredRmsPropParameters* temp = _impl_.parameters_.centered_rms_prop_;
    _impl_.parameters_.centered_rms_prop_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_centered_rms_prop(::tensorflow::tpu::CenteredRmsPropParameters* centered_rms_prop) {
  clear_parameters();
  if (centered_rms_prop) {
    set_has_centered_rms_prop();
    _impl_.parameters_.centered_rms_prop_ = centered_rms_prop;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::_internal_mutable_centered_rms_prop() {
  if (!_internal_has_centered_rms_prop()) {
    clear_parameters();
    set_has_centered_rms_prop();
    _impl_.parameters_.centered_rms_prop_ = CreateMaybeMessage< ::tensorflow::tpu::CenteredRmsPropParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.centered_rms_prop_;
}
inline ::tensorflow::tpu::CenteredRmsPropParameters* OptimizationParameters::mutable_centered_rms_prop() {
  ::tensorflow::tpu::CenteredRmsPropParameters* _msg = _internal_mutable_centered_rms_prop();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.centered_rms_prop)
  return _msg;
}

// .tensorflow.tpu.MdlAdagradLightParameters mdl_adagrad_light = 11;
inline bool OptimizationParameters::_internal_has_mdl_adagrad_light() const {
  return parameters_case() == kMdlAdagradLight;
}
inline bool OptimizationParameters::has_mdl_adagrad_light() const {
  return _internal_has_mdl_adagrad_light();
}
inline void OptimizationParameters::set_has_mdl_adagrad_light() {
  _impl_._oneof_case_[0] = kMdlAdagradLight;
}
inline void OptimizationParameters::clear_mdl_adagrad_light() {
  if (_internal_has_mdl_adagrad_light()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.mdl_adagrad_light_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::release_mdl_adagrad_light() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  if (_internal_has_mdl_adagrad_light()) {
    clear_has_parameters();
    ::tensorflow::tpu::MdlAdagradLightParameters* temp = _impl_.parameters_.mdl_adagrad_light_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.mdl_adagrad_light_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::MdlAdagradLightParameters& OptimizationParameters::_internal_mdl_adagrad_light() const {
  return _internal_has_mdl_adagrad_light()
      ? *_impl_.parameters_.mdl_adagrad_light_
      : reinterpret_cast< ::tensorflow::tpu::MdlAdagradLightParameters&>(::tensorflow::tpu::_MdlAdagradLightParameters_default_instance_);
}
inline const ::tensorflow::tpu::MdlAdagradLightParameters& OptimizationParameters::mdl_adagrad_light() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  return _internal_mdl_adagrad_light();
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::unsafe_arena_release_mdl_adagrad_light() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  if (_internal_has_mdl_adagrad_light()) {
    clear_has_parameters();
    ::tensorflow::tpu::MdlAdagradLightParameters* temp = _impl_.parameters_.mdl_adagrad_light_;
    _impl_.parameters_.mdl_adagrad_light_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_mdl_adagrad_light(::tensorflow::tpu::MdlAdagradLightParameters* mdl_adagrad_light) {
  clear_parameters();
  if (mdl_adagrad_light) {
    set_has_mdl_adagrad_light();
    _impl_.parameters_.mdl_adagrad_light_ = mdl_adagrad_light;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::_internal_mutable_mdl_adagrad_light() {
  if (!_internal_has_mdl_adagrad_light()) {
    clear_parameters();
    set_has_mdl_adagrad_light();
    _impl_.parameters_.mdl_adagrad_light_ = CreateMaybeMessage< ::tensorflow::tpu::MdlAdagradLightParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.mdl_adagrad_light_;
}
inline ::tensorflow::tpu::MdlAdagradLightParameters* OptimizationParameters::mutable_mdl_adagrad_light() {
  ::tensorflow::tpu::MdlAdagradLightParameters* _msg = _internal_mutable_mdl_adagrad_light();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.mdl_adagrad_light)
  return _msg;
}

// .tensorflow.tpu.AdadeltaParameters adadelta = 12;
inline bool OptimizationParameters::_internal_has_adadelta() const {
  return parameters_case() == kAdadelta;
}
inline bool OptimizationParameters::has_adadelta() const {
  return _internal_has_adadelta();
}
inline void OptimizationParameters::set_has_adadelta() {
  _impl_._oneof_case_[0] = kAdadelta;
}
inline void OptimizationParameters::clear_adadelta() {
  if (_internal_has_adadelta()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.adadelta_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::release_adadelta() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.adadelta)
  if (_internal_has_adadelta()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdadeltaParameters* temp = _impl_.parameters_.adadelta_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.adadelta_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AdadeltaParameters& OptimizationParameters::_internal_adadelta() const {
  return _internal_has_adadelta()
      ? *_impl_.parameters_.adadelta_
      : reinterpret_cast< ::tensorflow::tpu::AdadeltaParameters&>(::tensorflow::tpu::_AdadeltaParameters_default_instance_);
}
inline const ::tensorflow::tpu::AdadeltaParameters& OptimizationParameters::adadelta() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.adadelta)
  return _internal_adadelta();
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::unsafe_arena_release_adadelta() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.adadelta)
  if (_internal_has_adadelta()) {
    clear_has_parameters();
    ::tensorflow::tpu::AdadeltaParameters* temp = _impl_.parameters_.adadelta_;
    _impl_.parameters_.adadelta_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_adadelta(::tensorflow::tpu::AdadeltaParameters* adadelta) {
  clear_parameters();
  if (adadelta) {
    set_has_adadelta();
    _impl_.parameters_.adadelta_ = adadelta;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.adadelta)
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::_internal_mutable_adadelta() {
  if (!_internal_has_adadelta()) {
    clear_parameters();
    set_has_adadelta();
    _impl_.parameters_.adadelta_ = CreateMaybeMessage< ::tensorflow::tpu::AdadeltaParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.adadelta_;
}
inline ::tensorflow::tpu::AdadeltaParameters* OptimizationParameters::mutable_adadelta() {
  ::tensorflow::tpu::AdadeltaParameters* _msg = _internal_mutable_adadelta();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.adadelta)
  return _msg;
}

// .tensorflow.tpu.ProximalAdagradParameters proximal_adagrad = 14;
inline bool OptimizationParameters::_internal_has_proximal_adagrad() const {
  return parameters_case() == kProximalAdagrad;
}
inline bool OptimizationParameters::has_proximal_adagrad() const {
  return _internal_has_proximal_adagrad();
}
inline void OptimizationParameters::set_has_proximal_adagrad() {
  _impl_._oneof_case_[0] = kProximalAdagrad;
}
inline void OptimizationParameters::clear_proximal_adagrad() {
  if (_internal_has_proximal_adagrad()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.proximal_adagrad_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::release_proximal_adagrad() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  if (_internal_has_proximal_adagrad()) {
    clear_has_parameters();
    ::tensorflow::tpu::ProximalAdagradParameters* temp = _impl_.parameters_.proximal_adagrad_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.proximal_adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::ProximalAdagradParameters& OptimizationParameters::_internal_proximal_adagrad() const {
  return _internal_has_proximal_adagrad()
      ? *_impl_.parameters_.proximal_adagrad_
      : reinterpret_cast< ::tensorflow::tpu::ProximalAdagradParameters&>(::tensorflow::tpu::_ProximalAdagradParameters_default_instance_);
}
inline const ::tensorflow::tpu::ProximalAdagradParameters& OptimizationParameters::proximal_adagrad() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  return _internal_proximal_adagrad();
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::unsafe_arena_release_proximal_adagrad() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  if (_internal_has_proximal_adagrad()) {
    clear_has_parameters();
    ::tensorflow::tpu::ProximalAdagradParameters* temp = _impl_.parameters_.proximal_adagrad_;
    _impl_.parameters_.proximal_adagrad_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_proximal_adagrad(::tensorflow::tpu::ProximalAdagradParameters* proximal_adagrad) {
  clear_parameters();
  if (proximal_adagrad) {
    set_has_proximal_adagrad();
    _impl_.parameters_.proximal_adagrad_ = proximal_adagrad;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::_internal_mutable_proximal_adagrad() {
  if (!_internal_has_proximal_adagrad()) {
    clear_parameters();
    set_has_proximal_adagrad();
    _impl_.parameters_.proximal_adagrad_ = CreateMaybeMessage< ::tensorflow::tpu::ProximalAdagradParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.proximal_adagrad_;
}
inline ::tensorflow::tpu::ProximalAdagradParameters* OptimizationParameters::mutable_proximal_adagrad() {
  ::tensorflow::tpu::ProximalAdagradParameters* _msg = _internal_mutable_proximal_adagrad();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.proximal_adagrad)
  return _msg;
}

// .tensorflow.tpu.OnlineYogiParameters online_yogi = 20;
inline bool OptimizationParameters::_internal_has_online_yogi() const {
  return parameters_case() == kOnlineYogi;
}
inline bool OptimizationParameters::has_online_yogi() const {
  return _internal_has_online_yogi();
}
inline void OptimizationParameters::set_has_online_yogi() {
  _impl_._oneof_case_[0] = kOnlineYogi;
}
inline void OptimizationParameters::clear_online_yogi() {
  if (_internal_has_online_yogi()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.online_yogi_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::OnlineYogiParameters* OptimizationParameters::release_online_yogi() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.online_yogi)
  if (_internal_has_online_yogi()) {
    clear_has_parameters();
    ::tensorflow::tpu::OnlineYogiParameters* temp = _impl_.parameters_.online_yogi_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.online_yogi_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::OnlineYogiParameters& OptimizationParameters::_internal_online_yogi() const {
  return _internal_has_online_yogi()
      ? *_impl_.parameters_.online_yogi_
      : reinterpret_cast< ::tensorflow::tpu::OnlineYogiParameters&>(::tensorflow::tpu::_OnlineYogiParameters_default_instance_);
}
inline const ::tensorflow::tpu::OnlineYogiParameters& OptimizationParameters::online_yogi() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.online_yogi)
  return _internal_online_yogi();
}
inline ::tensorflow::tpu::OnlineYogiParameters* OptimizationParameters::unsafe_arena_release_online_yogi() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.online_yogi)
  if (_internal_has_online_yogi()) {
    clear_has_parameters();
    ::tensorflow::tpu::OnlineYogiParameters* temp = _impl_.parameters_.online_yogi_;
    _impl_.parameters_.online_yogi_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_online_yogi(::tensorflow::tpu::OnlineYogiParameters* online_yogi) {
  clear_parameters();
  if (online_yogi) {
    set_has_online_yogi();
    _impl_.parameters_.online_yogi_ = online_yogi;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.online_yogi)
}
inline ::tensorflow::tpu::OnlineYogiParameters* OptimizationParameters::_internal_mutable_online_yogi() {
  if (!_internal_has_online_yogi()) {
    clear_parameters();
    set_has_online_yogi();
    _impl_.parameters_.online_yogi_ = CreateMaybeMessage< ::tensorflow::tpu::OnlineYogiParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.online_yogi_;
}
inline ::tensorflow::tpu::OnlineYogiParameters* OptimizationParameters::mutable_online_yogi() {
  ::tensorflow::tpu::OnlineYogiParameters* _msg = _internal_mutable_online_yogi();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.online_yogi)
  return _msg;
}

// .tensorflow.tpu.ProximalYogiParameters proximal_yogi = 21;
inline bool OptimizationParameters::_internal_has_proximal_yogi() const {
  return parameters_case() == kProximalYogi;
}
inline bool OptimizationParameters::has_proximal_yogi() const {
  return _internal_has_proximal_yogi();
}
inline void OptimizationParameters::set_has_proximal_yogi() {
  _impl_._oneof_case_[0] = kProximalYogi;
}
inline void OptimizationParameters::clear_proximal_yogi() {
  if (_internal_has_proximal_yogi()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.proximal_yogi_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::ProximalYogiParameters* OptimizationParameters::release_proximal_yogi() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  if (_internal_has_proximal_yogi()) {
    clear_has_parameters();
    ::tensorflow::tpu::ProximalYogiParameters* temp = _impl_.parameters_.proximal_yogi_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.proximal_yogi_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::ProximalYogiParameters& OptimizationParameters::_internal_proximal_yogi() const {
  return _internal_has_proximal_yogi()
      ? *_impl_.parameters_.proximal_yogi_
      : reinterpret_cast< ::tensorflow::tpu::ProximalYogiParameters&>(::tensorflow::tpu::_ProximalYogiParameters_default_instance_);
}
inline const ::tensorflow::tpu::ProximalYogiParameters& OptimizationParameters::proximal_yogi() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  return _internal_proximal_yogi();
}
inline ::tensorflow::tpu::ProximalYogiParameters* OptimizationParameters::unsafe_arena_release_proximal_yogi() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  if (_internal_has_proximal_yogi()) {
    clear_has_parameters();
    ::tensorflow::tpu::ProximalYogiParameters* temp = _impl_.parameters_.proximal_yogi_;
    _impl_.parameters_.proximal_yogi_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_proximal_yogi(::tensorflow::tpu::ProximalYogiParameters* proximal_yogi) {
  clear_parameters();
  if (proximal_yogi) {
    set_has_proximal_yogi();
    _impl_.parameters_.proximal_yogi_ = proximal_yogi;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.proximal_yogi)
}
inline ::tensorflow::tpu::ProximalYogiParameters* OptimizationParameters::_internal_mutable_proximal_yogi() {
  if (!_internal_has_proximal_yogi()) {
    clear_parameters();
    set_has_proximal_yogi();
    _impl_.parameters_.proximal_yogi_ = CreateMaybeMessage< ::tensorflow::tpu::ProximalYogiParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.proximal_yogi_;
}
inline ::tensorflow::tpu::ProximalYogiParameters* OptimizationParameters::mutable_proximal_yogi() {
  ::tensorflow::tpu::ProximalYogiParameters* _msg = _internal_mutable_proximal_yogi();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.proximal_yogi)
  return _msg;
}

// .tensorflow.tpu.FrequencyEstimatorParameters frequency_estimator = 23;
inline bool OptimizationParameters::_internal_has_frequency_estimator() const {
  return parameters_case() == kFrequencyEstimator;
}
inline bool OptimizationParameters::has_frequency_estimator() const {
  return _internal_has_frequency_estimator();
}
inline void OptimizationParameters::set_has_frequency_estimator() {
  _impl_._oneof_case_[0] = kFrequencyEstimator;
}
inline void OptimizationParameters::clear_frequency_estimator() {
  if (_internal_has_frequency_estimator()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.frequency_estimator_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::FrequencyEstimatorParameters* OptimizationParameters::release_frequency_estimator() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  if (_internal_has_frequency_estimator()) {
    clear_has_parameters();
    ::tensorflow::tpu::FrequencyEstimatorParameters* temp = _impl_.parameters_.frequency_estimator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.frequency_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::FrequencyEstimatorParameters& OptimizationParameters::_internal_frequency_estimator() const {
  return _internal_has_frequency_estimator()
      ? *_impl_.parameters_.frequency_estimator_
      : reinterpret_cast< ::tensorflow::tpu::FrequencyEstimatorParameters&>(::tensorflow::tpu::_FrequencyEstimatorParameters_default_instance_);
}
inline const ::tensorflow::tpu::FrequencyEstimatorParameters& OptimizationParameters::frequency_estimator() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  return _internal_frequency_estimator();
}
inline ::tensorflow::tpu::FrequencyEstimatorParameters* OptimizationParameters::unsafe_arena_release_frequency_estimator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  if (_internal_has_frequency_estimator()) {
    clear_has_parameters();
    ::tensorflow::tpu::FrequencyEstimatorParameters* temp = _impl_.parameters_.frequency_estimator_;
    _impl_.parameters_.frequency_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_frequency_estimator(::tensorflow::tpu::FrequencyEstimatorParameters* frequency_estimator) {
  clear_parameters();
  if (frequency_estimator) {
    set_has_frequency_estimator();
    _impl_.parameters_.frequency_estimator_ = frequency_estimator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.frequency_estimator)
}
inline ::tensorflow::tpu::FrequencyEstimatorParameters* OptimizationParameters::_internal_mutable_frequency_estimator() {
  if (!_internal_has_frequency_estimator()) {
    clear_parameters();
    set_has_frequency_estimator();
    _impl_.parameters_.frequency_estimator_ = CreateMaybeMessage< ::tensorflow::tpu::FrequencyEstimatorParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.frequency_estimator_;
}
inline ::tensorflow::tpu::FrequencyEstimatorParameters* OptimizationParameters::mutable_frequency_estimator() {
  ::tensorflow::tpu::FrequencyEstimatorParameters* _msg = _internal_mutable_frequency_estimator();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.frequency_estimator)
  return _msg;
}

// .tensorflow.tpu.UserDefinedProgramParameters user_defined_program = 24;
inline bool OptimizationParameters::_internal_has_user_defined_program() const {
  return parameters_case() == kUserDefinedProgram;
}
inline bool OptimizationParameters::has_user_defined_program() const {
  return _internal_has_user_defined_program();
}
inline void OptimizationParameters::set_has_user_defined_program() {
  _impl_._oneof_case_[0] = kUserDefinedProgram;
}
inline void OptimizationParameters::clear_user_defined_program() {
  if (_internal_has_user_defined_program()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.user_defined_program_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::UserDefinedProgramParameters* OptimizationParameters::release_user_defined_program() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.user_defined_program)
  if (_internal_has_user_defined_program()) {
    clear_has_parameters();
    ::tensorflow::tpu::UserDefinedProgramParameters* temp = _impl_.parameters_.user_defined_program_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.user_defined_program_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::UserDefinedProgramParameters& OptimizationParameters::_internal_user_defined_program() const {
  return _internal_has_user_defined_program()
      ? *_impl_.parameters_.user_defined_program_
      : reinterpret_cast< ::tensorflow::tpu::UserDefinedProgramParameters&>(::tensorflow::tpu::_UserDefinedProgramParameters_default_instance_);
}
inline const ::tensorflow::tpu::UserDefinedProgramParameters& OptimizationParameters::user_defined_program() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.user_defined_program)
  return _internal_user_defined_program();
}
inline ::tensorflow::tpu::UserDefinedProgramParameters* OptimizationParameters::unsafe_arena_release_user_defined_program() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.user_defined_program)
  if (_internal_has_user_defined_program()) {
    clear_has_parameters();
    ::tensorflow::tpu::UserDefinedProgramParameters* temp = _impl_.parameters_.user_defined_program_;
    _impl_.parameters_.user_defined_program_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_user_defined_program(::tensorflow::tpu::UserDefinedProgramParameters* user_defined_program) {
  clear_parameters();
  if (user_defined_program) {
    set_has_user_defined_program();
    _impl_.parameters_.user_defined_program_ = user_defined_program;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.user_defined_program)
}
inline ::tensorflow::tpu::UserDefinedProgramParameters* OptimizationParameters::_internal_mutable_user_defined_program() {
  if (!_internal_has_user_defined_program()) {
    clear_parameters();
    set_has_user_defined_program();
    _impl_.parameters_.user_defined_program_ = CreateMaybeMessage< ::tensorflow::tpu::UserDefinedProgramParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.user_defined_program_;
}
inline ::tensorflow::tpu::UserDefinedProgramParameters* OptimizationParameters::mutable_user_defined_program() {
  ::tensorflow::tpu::UserDefinedProgramParameters* _msg = _internal_mutable_user_defined_program();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.user_defined_program)
  return _msg;
}

// .tensorflow.tpu.AssignParameters assign = 25;
inline bool OptimizationParameters::_internal_has_assign() const {
  return parameters_case() == kAssign;
}
inline bool OptimizationParameters::has_assign() const {
  return _internal_has_assign();
}
inline void OptimizationParameters::set_has_assign() {
  _impl_._oneof_case_[0] = kAssign;
}
inline void OptimizationParameters::clear_assign() {
  if (_internal_has_assign()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.parameters_.assign_;
    }
    clear_has_parameters();
  }
}
inline ::tensorflow::tpu::AssignParameters* OptimizationParameters::release_assign() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.OptimizationParameters.assign)
  if (_internal_has_assign()) {
    clear_has_parameters();
    ::tensorflow::tpu::AssignParameters* temp = _impl_.parameters_.assign_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.parameters_.assign_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::AssignParameters& OptimizationParameters::_internal_assign() const {
  return _internal_has_assign()
      ? *_impl_.parameters_.assign_
      : reinterpret_cast< ::tensorflow::tpu::AssignParameters&>(::tensorflow::tpu::_AssignParameters_default_instance_);
}
inline const ::tensorflow::tpu::AssignParameters& OptimizationParameters::assign() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.OptimizationParameters.assign)
  return _internal_assign();
}
inline ::tensorflow::tpu::AssignParameters* OptimizationParameters::unsafe_arena_release_assign() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.OptimizationParameters.assign)
  if (_internal_has_assign()) {
    clear_has_parameters();
    ::tensorflow::tpu::AssignParameters* temp = _impl_.parameters_.assign_;
    _impl_.parameters_.assign_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void OptimizationParameters::unsafe_arena_set_allocated_assign(::tensorflow::tpu::AssignParameters* assign) {
  clear_parameters();
  if (assign) {
    set_has_assign();
    _impl_.parameters_.assign_ = assign;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.OptimizationParameters.assign)
}
inline ::tensorflow::tpu::AssignParameters* OptimizationParameters::_internal_mutable_assign() {
  if (!_internal_has_assign()) {
    clear_parameters();
    set_has_assign();
    _impl_.parameters_.assign_ = CreateMaybeMessage< ::tensorflow::tpu::AssignParameters >(GetArenaForAllocation());
  }
  return _impl_.parameters_.assign_;
}
inline ::tensorflow::tpu::AssignParameters* OptimizationParameters::mutable_assign() {
  ::tensorflow::tpu::AssignParameters* _msg = _internal_mutable_assign();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.OptimizationParameters.assign)
  return _msg;
}

inline bool OptimizationParameters::has_parameters() const {
  return parameters_case() != PARAMETERS_NOT_SET;
}
inline void OptimizationParameters::clear_has_parameters() {
  _impl_._oneof_case_[0] = PARAMETERS_NOT_SET;
}
inline OptimizationParameters::ParametersCase OptimizationParameters::parameters_case() const {
  return OptimizationParameters::ParametersCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// StateVariableSpecification_UserDefined

// -------------------------------------------------------------------

// StateVariableSpecification_FillWithConstant

// double initial_value = 1;
inline void StateVariableSpecification_FillWithConstant::clear_initial_value() {
  _impl_.initial_value_ = 0;
}
inline double StateVariableSpecification_FillWithConstant::_internal_initial_value() const {
  return _impl_.initial_value_;
}
inline double StateVariableSpecification_FillWithConstant::initial_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value)
  return _internal_initial_value();
}
inline void StateVariableSpecification_FillWithConstant::_internal_set_initial_value(double value) {
  
  _impl_.initial_value_ = value;
}
inline void StateVariableSpecification_FillWithConstant::set_initial_value(double value) {
  _internal_set_initial_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.FillWithConstant.initial_value)
}

// -------------------------------------------------------------------

// StateVariableSpecification

// string name = 1;
inline void StateVariableSpecification::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& StateVariableSpecification::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StateVariableSpecification::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.tpu.StateVariableSpecification.name)
}
inline std::string* StateVariableSpecification::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.name)
  return _s;
}
inline const std::string& StateVariableSpecification::_internal_name() const {
  return _impl_.name_.Get();
}
inline void StateVariableSpecification::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* StateVariableSpecification::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* StateVariableSpecification::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.name)
  return _impl_.name_.Release();
}
inline void StateVariableSpecification::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.tpu.StateVariableSpecification.name)
}

// .tensorflow.tpu.StateVariableSpecification.UserDefined user_defined = 2;
inline bool StateVariableSpecification::_internal_has_user_defined() const {
  return usage_case() == kUserDefined;
}
inline bool StateVariableSpecification::has_user_defined() const {
  return _internal_has_user_defined();
}
inline void StateVariableSpecification::set_has_user_defined() {
  _impl_._oneof_case_[0] = kUserDefined;
}
inline void StateVariableSpecification::clear_user_defined() {
  if (_internal_has_user_defined()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.usage_.user_defined_;
    }
    clear_has_usage();
  }
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::release_user_defined() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.user_defined)
  if (_internal_has_user_defined()) {
    clear_has_usage();
    ::tensorflow::tpu::StateVariableSpecification_UserDefined* temp = _impl_.usage_.user_defined_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.usage_.user_defined_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_UserDefined& StateVariableSpecification::_internal_user_defined() const {
  return _internal_has_user_defined()
      ? *_impl_.usage_.user_defined_
      : reinterpret_cast< ::tensorflow::tpu::StateVariableSpecification_UserDefined&>(::tensorflow::tpu::_StateVariableSpecification_UserDefined_default_instance_);
}
inline const ::tensorflow::tpu::StateVariableSpecification_UserDefined& StateVariableSpecification::user_defined() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.user_defined)
  return _internal_user_defined();
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::unsafe_arena_release_user_defined() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.StateVariableSpecification.user_defined)
  if (_internal_has_user_defined()) {
    clear_has_usage();
    ::tensorflow::tpu::StateVariableSpecification_UserDefined* temp = _impl_.usage_.user_defined_;
    _impl_.usage_.user_defined_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StateVariableSpecification::unsafe_arena_set_allocated_user_defined(::tensorflow::tpu::StateVariableSpecification_UserDefined* user_defined) {
  clear_usage();
  if (user_defined) {
    set_has_user_defined();
    _impl_.usage_.user_defined_ = user_defined;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.StateVariableSpecification.user_defined)
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::_internal_mutable_user_defined() {
  if (!_internal_has_user_defined()) {
    clear_usage();
    set_has_user_defined();
    _impl_.usage_.user_defined_ = CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_UserDefined >(GetArenaForAllocation());
  }
  return _impl_.usage_.user_defined_;
}
inline ::tensorflow::tpu::StateVariableSpecification_UserDefined* StateVariableSpecification::mutable_user_defined() {
  ::tensorflow::tpu::StateVariableSpecification_UserDefined* _msg = _internal_mutable_user_defined();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.user_defined)
  return _msg;
}

// .tensorflow.tpu.StateVariableSpecification.FillWithConstant fill_with_constant = 3;
inline bool StateVariableSpecification::_internal_has_fill_with_constant() const {
  return usage_case() == kFillWithConstant;
}
inline bool StateVariableSpecification::has_fill_with_constant() const {
  return _internal_has_fill_with_constant();
}
inline void StateVariableSpecification::set_has_fill_with_constant() {
  _impl_._oneof_case_[0] = kFillWithConstant;
}
inline void StateVariableSpecification::clear_fill_with_constant() {
  if (_internal_has_fill_with_constant()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.usage_.fill_with_constant_;
    }
    clear_has_usage();
  }
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::release_fill_with_constant() {
  // @@protoc_insertion_point(field_release:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  if (_internal_has_fill_with_constant()) {
    clear_has_usage();
    ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* temp = _impl_.usage_.fill_with_constant_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.usage_.fill_with_constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& StateVariableSpecification::_internal_fill_with_constant() const {
  return _internal_has_fill_with_constant()
      ? *_impl_.usage_.fill_with_constant_
      : reinterpret_cast< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant&>(::tensorflow::tpu::_StateVariableSpecification_FillWithConstant_default_instance_);
}
inline const ::tensorflow::tpu::StateVariableSpecification_FillWithConstant& StateVariableSpecification::fill_with_constant() const {
  // @@protoc_insertion_point(field_get:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  return _internal_fill_with_constant();
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::unsafe_arena_release_fill_with_constant() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  if (_internal_has_fill_with_constant()) {
    clear_has_usage();
    ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* temp = _impl_.usage_.fill_with_constant_;
    _impl_.usage_.fill_with_constant_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StateVariableSpecification::unsafe_arena_set_allocated_fill_with_constant(::tensorflow::tpu::StateVariableSpecification_FillWithConstant* fill_with_constant) {
  clear_usage();
  if (fill_with_constant) {
    set_has_fill_with_constant();
    _impl_.usage_.fill_with_constant_ = fill_with_constant;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::_internal_mutable_fill_with_constant() {
  if (!_internal_has_fill_with_constant()) {
    clear_usage();
    set_has_fill_with_constant();
    _impl_.usage_.fill_with_constant_ = CreateMaybeMessage< ::tensorflow::tpu::StateVariableSpecification_FillWithConstant >(GetArenaForAllocation());
  }
  return _impl_.usage_.fill_with_constant_;
}
inline ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* StateVariableSpecification::mutable_fill_with_constant() {
  ::tensorflow::tpu::StateVariableSpecification_FillWithConstant* _msg = _internal_mutable_fill_with_constant();
  // @@protoc_insertion_point(field_mutable:tensorflow.tpu.StateVariableSpecification.fill_with_constant)
  return _msg;
}

inline bool StateVariableSpecification::has_usage() const {
  return usage_case() != USAGE_NOT_SET;
}
inline void StateVariableSpecification::clear_has_usage() {
  _impl_._oneof_case_[0] = USAGE_NOT_SET;
}
inline StateVariableSpecification::UsageCase StateVariableSpecification::usage_case() const {
  return StateVariableSpecification::UsageCase(_impl_._oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tpu
}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::tpu::GradientAccumulationStatus_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::GradientAccumulationStatus_Status>() {
  return ::tensorflow::tpu::GradientAccumulationStatus_Status_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::LowDimensionalPackingStatus_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::LowDimensionalPackingStatus_Status>() {
  return ::tensorflow::tpu::LowDimensionalPackingStatus_Status_descriptor();
}
template <> struct is_proto_enum< ::tensorflow::tpu::HotIdReplicationConfiguration_Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::tpu::HotIdReplicationConfiguration_Status>() {
  return ::tensorflow::tpu::HotIdReplicationConfiguration_Status_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2ftpu_2foptimization_5fparameters_2eproto
