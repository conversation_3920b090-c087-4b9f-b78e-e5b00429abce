Metadata-Version: 2.4
Name: fire
Version: 0.7.0
Summary: A library for automatically generating command line interfaces.
Home-page: https://github.com/google/python-fire
Author: <PERSON>
Author-email: <EMAIL>
License: Apache Software License
Keywords: command line interface cli python fire interactive bash tool
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Topic :: Software Development :: Libraries :: Python Modules
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Operating System :: OS Independent
Classifier: Operating System :: POSIX
Classifier: Operating System :: MacOS
Classifier: Operating System :: Unix
License-File: LICENSE
Requires-Dist: termcolor
Dynamic: author
Dynamic: author-email
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: keywords
Dynamic: license
Dynamic: license-file
Dynamic: requires-dist
Dynamic: summary

Python Fire is a library for automatically generating command line interfaces
(CLIs) with a single line of code.

It will turn any Python module, class, object, function, etc. (any Python
component will work!) into a CLI. It's called Fire because when you call Fire(),
it fires off your command.
