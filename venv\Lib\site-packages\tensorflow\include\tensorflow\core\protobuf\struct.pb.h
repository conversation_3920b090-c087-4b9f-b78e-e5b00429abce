// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/struct.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "tensorflow/core/framework/tensor.pb.h"
#include "tensorflow/core/framework/tensor_shape.pb.h"
#include "tensorflow/core/framework/types.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
namespace tensorflow {
class BoundedTensorSpecProto;
struct BoundedTensorSpecProtoDefaultTypeInternal;
extern BoundedTensorSpecProtoDefaultTypeInternal _BoundedTensorSpecProto_default_instance_;
class DictValue;
struct DictValueDefaultTypeInternal;
extern DictValueDefaultTypeInternal _DictValue_default_instance_;
class DictValue_FieldsEntry_DoNotUse;
struct DictValue_FieldsEntry_DoNotUseDefaultTypeInternal;
extern DictValue_FieldsEntry_DoNotUseDefaultTypeInternal _DictValue_FieldsEntry_DoNotUse_default_instance_;
class ListValue;
struct ListValueDefaultTypeInternal;
extern ListValueDefaultTypeInternal _ListValue_default_instance_;
class NamedTupleValue;
struct NamedTupleValueDefaultTypeInternal;
extern NamedTupleValueDefaultTypeInternal _NamedTupleValue_default_instance_;
class NoneValue;
struct NoneValueDefaultTypeInternal;
extern NoneValueDefaultTypeInternal _NoneValue_default_instance_;
class PairValue;
struct PairValueDefaultTypeInternal;
extern PairValueDefaultTypeInternal _PairValue_default_instance_;
class StructuredValue;
struct StructuredValueDefaultTypeInternal;
extern StructuredValueDefaultTypeInternal _StructuredValue_default_instance_;
class TensorSpecProto;
struct TensorSpecProtoDefaultTypeInternal;
extern TensorSpecProtoDefaultTypeInternal _TensorSpecProto_default_instance_;
class TupleValue;
struct TupleValueDefaultTypeInternal;
extern TupleValueDefaultTypeInternal _TupleValue_default_instance_;
class TypeSpecProto;
struct TypeSpecProtoDefaultTypeInternal;
extern TypeSpecProtoDefaultTypeInternal _TypeSpecProto_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::BoundedTensorSpecProto* Arena::CreateMaybeMessage<::tensorflow::BoundedTensorSpecProto>(Arena*);
template<> ::tensorflow::DictValue* Arena::CreateMaybeMessage<::tensorflow::DictValue>(Arena*);
template<> ::tensorflow::DictValue_FieldsEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::DictValue_FieldsEntry_DoNotUse>(Arena*);
template<> ::tensorflow::ListValue* Arena::CreateMaybeMessage<::tensorflow::ListValue>(Arena*);
template<> ::tensorflow::NamedTupleValue* Arena::CreateMaybeMessage<::tensorflow::NamedTupleValue>(Arena*);
template<> ::tensorflow::NoneValue* Arena::CreateMaybeMessage<::tensorflow::NoneValue>(Arena*);
template<> ::tensorflow::PairValue* Arena::CreateMaybeMessage<::tensorflow::PairValue>(Arena*);
template<> ::tensorflow::StructuredValue* Arena::CreateMaybeMessage<::tensorflow::StructuredValue>(Arena*);
template<> ::tensorflow::TensorSpecProto* Arena::CreateMaybeMessage<::tensorflow::TensorSpecProto>(Arena*);
template<> ::tensorflow::TupleValue* Arena::CreateMaybeMessage<::tensorflow::TupleValue>(Arena*);
template<> ::tensorflow::TypeSpecProto* Arena::CreateMaybeMessage<::tensorflow::TypeSpecProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

enum TypeSpecProto_TypeSpecClass : int {
  TypeSpecProto_TypeSpecClass_UNKNOWN = 0,
  TypeSpecProto_TypeSpecClass_SPARSE_TENSOR_SPEC = 1,
  TypeSpecProto_TypeSpecClass_INDEXED_SLICES_SPEC = 2,
  TypeSpecProto_TypeSpecClass_RAGGED_TENSOR_SPEC = 3,
  TypeSpecProto_TypeSpecClass_TENSOR_ARRAY_SPEC = 4,
  TypeSpecProto_TypeSpecClass_DATA_DATASET_SPEC = 5,
  TypeSpecProto_TypeSpecClass_DATA_ITERATOR_SPEC = 6,
  TypeSpecProto_TypeSpecClass_OPTIONAL_SPEC = 7,
  TypeSpecProto_TypeSpecClass_PER_REPLICA_SPEC = 8,
  TypeSpecProto_TypeSpecClass_VARIABLE_SPEC = 9,
  TypeSpecProto_TypeSpecClass_ROW_PARTITION_SPEC = 10,
  TypeSpecProto_TypeSpecClass_REGISTERED_TYPE_SPEC = 12,
  TypeSpecProto_TypeSpecClass_EXTENSION_TYPE_SPEC = 13,
  TypeSpecProto_TypeSpecClass_TypeSpecProto_TypeSpecClass_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  TypeSpecProto_TypeSpecClass_TypeSpecProto_TypeSpecClass_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool TypeSpecProto_TypeSpecClass_IsValid(int value);
constexpr TypeSpecProto_TypeSpecClass TypeSpecProto_TypeSpecClass_TypeSpecClass_MIN = TypeSpecProto_TypeSpecClass_UNKNOWN;
constexpr TypeSpecProto_TypeSpecClass TypeSpecProto_TypeSpecClass_TypeSpecClass_MAX = TypeSpecProto_TypeSpecClass_EXTENSION_TYPE_SPEC;
constexpr int TypeSpecProto_TypeSpecClass_TypeSpecClass_ARRAYSIZE = TypeSpecProto_TypeSpecClass_TypeSpecClass_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* TypeSpecProto_TypeSpecClass_descriptor();
template<typename T>
inline const std::string& TypeSpecProto_TypeSpecClass_Name(T enum_t_value) {
  static_assert(::std::is_same<T, TypeSpecProto_TypeSpecClass>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function TypeSpecProto_TypeSpecClass_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    TypeSpecProto_TypeSpecClass_descriptor(), enum_t_value);
}
inline bool TypeSpecProto_TypeSpecClass_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, TypeSpecProto_TypeSpecClass* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<TypeSpecProto_TypeSpecClass>(
    TypeSpecProto_TypeSpecClass_descriptor(), name, value);
}
// ===================================================================

class StructuredValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.StructuredValue) */ {
 public:
  inline StructuredValue() : StructuredValue(nullptr) {}
  ~StructuredValue() override;
  explicit PROTOBUF_CONSTEXPR StructuredValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StructuredValue(const StructuredValue& from);
  StructuredValue(StructuredValue&& from) noexcept
    : StructuredValue() {
    *this = ::std::move(from);
  }

  inline StructuredValue& operator=(const StructuredValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline StructuredValue& operator=(StructuredValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StructuredValue& default_instance() {
    return *internal_default_instance();
  }
  enum KindCase {
    kNoneValue = 1,
    kFloat64Value = 11,
    kInt64Value = 12,
    kStringValue = 13,
    kBoolValue = 14,
    kTensorShapeValue = 31,
    kTensorDtypeValue = 32,
    kTensorSpecValue = 33,
    kTypeSpecValue = 34,
    kBoundedTensorSpecValue = 35,
    kListValue = 51,
    kTupleValue = 52,
    kDictValue = 53,
    kNamedTupleValue = 54,
    kTensorValue = 55,
    kNumpyValue = 56,
    KIND_NOT_SET = 0,
  };

  static inline const StructuredValue* internal_default_instance() {
    return reinterpret_cast<const StructuredValue*>(
               &_StructuredValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(StructuredValue& a, StructuredValue& b) {
    a.Swap(&b);
  }
  inline void Swap(StructuredValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StructuredValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StructuredValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StructuredValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StructuredValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const StructuredValue& from) {
    StructuredValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StructuredValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.StructuredValue";
  }
  protected:
  explicit StructuredValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNoneValueFieldNumber = 1,
    kFloat64ValueFieldNumber = 11,
    kInt64ValueFieldNumber = 12,
    kStringValueFieldNumber = 13,
    kBoolValueFieldNumber = 14,
    kTensorShapeValueFieldNumber = 31,
    kTensorDtypeValueFieldNumber = 32,
    kTensorSpecValueFieldNumber = 33,
    kTypeSpecValueFieldNumber = 34,
    kBoundedTensorSpecValueFieldNumber = 35,
    kListValueFieldNumber = 51,
    kTupleValueFieldNumber = 52,
    kDictValueFieldNumber = 53,
    kNamedTupleValueFieldNumber = 54,
    kTensorValueFieldNumber = 55,
    kNumpyValueFieldNumber = 56,
  };
  // .tensorflow.NoneValue none_value = 1;
  bool has_none_value() const;
  private:
  bool _internal_has_none_value() const;
  public:
  void clear_none_value();
  const ::tensorflow::NoneValue& none_value() const;
  PROTOBUF_NODISCARD ::tensorflow::NoneValue* release_none_value();
  ::tensorflow::NoneValue* mutable_none_value();
  void set_allocated_none_value(::tensorflow::NoneValue* none_value);
  private:
  const ::tensorflow::NoneValue& _internal_none_value() const;
  ::tensorflow::NoneValue* _internal_mutable_none_value();
  public:
  void unsafe_arena_set_allocated_none_value(
      ::tensorflow::NoneValue* none_value);
  ::tensorflow::NoneValue* unsafe_arena_release_none_value();

  // double float64_value = 11;
  bool has_float64_value() const;
  private:
  bool _internal_has_float64_value() const;
  public:
  void clear_float64_value();
  double float64_value() const;
  void set_float64_value(double value);
  private:
  double _internal_float64_value() const;
  void _internal_set_float64_value(double value);
  public:

  // sint64 int64_value = 12;
  bool has_int64_value() const;
  private:
  bool _internal_has_int64_value() const;
  public:
  void clear_int64_value();
  int64_t int64_value() const;
  void set_int64_value(int64_t value);
  private:
  int64_t _internal_int64_value() const;
  void _internal_set_int64_value(int64_t value);
  public:

  // string string_value = 13;
  bool has_string_value() const;
  private:
  bool _internal_has_string_value() const;
  public:
  void clear_string_value();
  const std::string& string_value() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_value(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_value();
  PROTOBUF_NODISCARD std::string* release_string_value();
  void set_allocated_string_value(std::string* string_value);
  private:
  const std::string& _internal_string_value() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_value(const std::string& value);
  std::string* _internal_mutable_string_value();
  public:

  // bool bool_value = 14;
  bool has_bool_value() const;
  private:
  bool _internal_has_bool_value() const;
  public:
  void clear_bool_value();
  bool bool_value() const;
  void set_bool_value(bool value);
  private:
  bool _internal_bool_value() const;
  void _internal_set_bool_value(bool value);
  public:

  // .tensorflow.TensorShapeProto tensor_shape_value = 31;
  bool has_tensor_shape_value() const;
  private:
  bool _internal_has_tensor_shape_value() const;
  public:
  void clear_tensor_shape_value();
  const ::tensorflow::TensorShapeProto& tensor_shape_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_tensor_shape_value();
  ::tensorflow::TensorShapeProto* mutable_tensor_shape_value();
  void set_allocated_tensor_shape_value(::tensorflow::TensorShapeProto* tensor_shape_value);
  private:
  const ::tensorflow::TensorShapeProto& _internal_tensor_shape_value() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_tensor_shape_value();
  public:
  void unsafe_arena_set_allocated_tensor_shape_value(
      ::tensorflow::TensorShapeProto* tensor_shape_value);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_tensor_shape_value();

  // .tensorflow.DataType tensor_dtype_value = 32;
  bool has_tensor_dtype_value() const;
  private:
  bool _internal_has_tensor_dtype_value() const;
  public:
  void clear_tensor_dtype_value();
  ::tensorflow::DataType tensor_dtype_value() const;
  void set_tensor_dtype_value(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_tensor_dtype_value() const;
  void _internal_set_tensor_dtype_value(::tensorflow::DataType value);
  public:

  // .tensorflow.TensorSpecProto tensor_spec_value = 33;
  bool has_tensor_spec_value() const;
  private:
  bool _internal_has_tensor_spec_value() const;
  public:
  void clear_tensor_spec_value();
  const ::tensorflow::TensorSpecProto& tensor_spec_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorSpecProto* release_tensor_spec_value();
  ::tensorflow::TensorSpecProto* mutable_tensor_spec_value();
  void set_allocated_tensor_spec_value(::tensorflow::TensorSpecProto* tensor_spec_value);
  private:
  const ::tensorflow::TensorSpecProto& _internal_tensor_spec_value() const;
  ::tensorflow::TensorSpecProto* _internal_mutable_tensor_spec_value();
  public:
  void unsafe_arena_set_allocated_tensor_spec_value(
      ::tensorflow::TensorSpecProto* tensor_spec_value);
  ::tensorflow::TensorSpecProto* unsafe_arena_release_tensor_spec_value();

  // .tensorflow.TypeSpecProto type_spec_value = 34;
  bool has_type_spec_value() const;
  private:
  bool _internal_has_type_spec_value() const;
  public:
  void clear_type_spec_value();
  const ::tensorflow::TypeSpecProto& type_spec_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TypeSpecProto* release_type_spec_value();
  ::tensorflow::TypeSpecProto* mutable_type_spec_value();
  void set_allocated_type_spec_value(::tensorflow::TypeSpecProto* type_spec_value);
  private:
  const ::tensorflow::TypeSpecProto& _internal_type_spec_value() const;
  ::tensorflow::TypeSpecProto* _internal_mutable_type_spec_value();
  public:
  void unsafe_arena_set_allocated_type_spec_value(
      ::tensorflow::TypeSpecProto* type_spec_value);
  ::tensorflow::TypeSpecProto* unsafe_arena_release_type_spec_value();

  // .tensorflow.BoundedTensorSpecProto bounded_tensor_spec_value = 35;
  bool has_bounded_tensor_spec_value() const;
  private:
  bool _internal_has_bounded_tensor_spec_value() const;
  public:
  void clear_bounded_tensor_spec_value();
  const ::tensorflow::BoundedTensorSpecProto& bounded_tensor_spec_value() const;
  PROTOBUF_NODISCARD ::tensorflow::BoundedTensorSpecProto* release_bounded_tensor_spec_value();
  ::tensorflow::BoundedTensorSpecProto* mutable_bounded_tensor_spec_value();
  void set_allocated_bounded_tensor_spec_value(::tensorflow::BoundedTensorSpecProto* bounded_tensor_spec_value);
  private:
  const ::tensorflow::BoundedTensorSpecProto& _internal_bounded_tensor_spec_value() const;
  ::tensorflow::BoundedTensorSpecProto* _internal_mutable_bounded_tensor_spec_value();
  public:
  void unsafe_arena_set_allocated_bounded_tensor_spec_value(
      ::tensorflow::BoundedTensorSpecProto* bounded_tensor_spec_value);
  ::tensorflow::BoundedTensorSpecProto* unsafe_arena_release_bounded_tensor_spec_value();

  // .tensorflow.ListValue list_value = 51;
  bool has_list_value() const;
  private:
  bool _internal_has_list_value() const;
  public:
  void clear_list_value();
  const ::tensorflow::ListValue& list_value() const;
  PROTOBUF_NODISCARD ::tensorflow::ListValue* release_list_value();
  ::tensorflow::ListValue* mutable_list_value();
  void set_allocated_list_value(::tensorflow::ListValue* list_value);
  private:
  const ::tensorflow::ListValue& _internal_list_value() const;
  ::tensorflow::ListValue* _internal_mutable_list_value();
  public:
  void unsafe_arena_set_allocated_list_value(
      ::tensorflow::ListValue* list_value);
  ::tensorflow::ListValue* unsafe_arena_release_list_value();

  // .tensorflow.TupleValue tuple_value = 52;
  bool has_tuple_value() const;
  private:
  bool _internal_has_tuple_value() const;
  public:
  void clear_tuple_value();
  const ::tensorflow::TupleValue& tuple_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TupleValue* release_tuple_value();
  ::tensorflow::TupleValue* mutable_tuple_value();
  void set_allocated_tuple_value(::tensorflow::TupleValue* tuple_value);
  private:
  const ::tensorflow::TupleValue& _internal_tuple_value() const;
  ::tensorflow::TupleValue* _internal_mutable_tuple_value();
  public:
  void unsafe_arena_set_allocated_tuple_value(
      ::tensorflow::TupleValue* tuple_value);
  ::tensorflow::TupleValue* unsafe_arena_release_tuple_value();

  // .tensorflow.DictValue dict_value = 53;
  bool has_dict_value() const;
  private:
  bool _internal_has_dict_value() const;
  public:
  void clear_dict_value();
  const ::tensorflow::DictValue& dict_value() const;
  PROTOBUF_NODISCARD ::tensorflow::DictValue* release_dict_value();
  ::tensorflow::DictValue* mutable_dict_value();
  void set_allocated_dict_value(::tensorflow::DictValue* dict_value);
  private:
  const ::tensorflow::DictValue& _internal_dict_value() const;
  ::tensorflow::DictValue* _internal_mutable_dict_value();
  public:
  void unsafe_arena_set_allocated_dict_value(
      ::tensorflow::DictValue* dict_value);
  ::tensorflow::DictValue* unsafe_arena_release_dict_value();

  // .tensorflow.NamedTupleValue named_tuple_value = 54;
  bool has_named_tuple_value() const;
  private:
  bool _internal_has_named_tuple_value() const;
  public:
  void clear_named_tuple_value();
  const ::tensorflow::NamedTupleValue& named_tuple_value() const;
  PROTOBUF_NODISCARD ::tensorflow::NamedTupleValue* release_named_tuple_value();
  ::tensorflow::NamedTupleValue* mutable_named_tuple_value();
  void set_allocated_named_tuple_value(::tensorflow::NamedTupleValue* named_tuple_value);
  private:
  const ::tensorflow::NamedTupleValue& _internal_named_tuple_value() const;
  ::tensorflow::NamedTupleValue* _internal_mutable_named_tuple_value();
  public:
  void unsafe_arena_set_allocated_named_tuple_value(
      ::tensorflow::NamedTupleValue* named_tuple_value);
  ::tensorflow::NamedTupleValue* unsafe_arena_release_named_tuple_value();

  // .tensorflow.TensorProto tensor_value = 55;
  bool has_tensor_value() const;
  private:
  bool _internal_has_tensor_value() const;
  public:
  void clear_tensor_value();
  const ::tensorflow::TensorProto& tensor_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_tensor_value();
  ::tensorflow::TensorProto* mutable_tensor_value();
  void set_allocated_tensor_value(::tensorflow::TensorProto* tensor_value);
  private:
  const ::tensorflow::TensorProto& _internal_tensor_value() const;
  ::tensorflow::TensorProto* _internal_mutable_tensor_value();
  public:
  void unsafe_arena_set_allocated_tensor_value(
      ::tensorflow::TensorProto* tensor_value);
  ::tensorflow::TensorProto* unsafe_arena_release_tensor_value();

  // .tensorflow.TensorProto numpy_value = 56;
  bool has_numpy_value() const;
  private:
  bool _internal_has_numpy_value() const;
  public:
  void clear_numpy_value();
  const ::tensorflow::TensorProto& numpy_value() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_numpy_value();
  ::tensorflow::TensorProto* mutable_numpy_value();
  void set_allocated_numpy_value(::tensorflow::TensorProto* numpy_value);
  private:
  const ::tensorflow::TensorProto& _internal_numpy_value() const;
  ::tensorflow::TensorProto* _internal_mutable_numpy_value();
  public:
  void unsafe_arena_set_allocated_numpy_value(
      ::tensorflow::TensorProto* numpy_value);
  ::tensorflow::TensorProto* unsafe_arena_release_numpy_value();

  void clear_kind();
  KindCase kind_case() const;
  // @@protoc_insertion_point(class_scope:tensorflow.StructuredValue)
 private:
  class _Internal;
  void set_has_none_value();
  void set_has_float64_value();
  void set_has_int64_value();
  void set_has_string_value();
  void set_has_bool_value();
  void set_has_tensor_shape_value();
  void set_has_tensor_dtype_value();
  void set_has_tensor_spec_value();
  void set_has_type_spec_value();
  void set_has_bounded_tensor_spec_value();
  void set_has_list_value();
  void set_has_tuple_value();
  void set_has_dict_value();
  void set_has_named_tuple_value();
  void set_has_tensor_value();
  void set_has_numpy_value();

  inline bool has_kind() const;
  inline void clear_has_kind();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    union KindUnion {
      constexpr KindUnion() : _constinit_{} {}
        ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
      ::tensorflow::NoneValue* none_value_;
      double float64_value_;
      int64_t int64_value_;
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_value_;
      bool bool_value_;
      ::tensorflow::TensorShapeProto* tensor_shape_value_;
      int tensor_dtype_value_;
      ::tensorflow::TensorSpecProto* tensor_spec_value_;
      ::tensorflow::TypeSpecProto* type_spec_value_;
      ::tensorflow::BoundedTensorSpecProto* bounded_tensor_spec_value_;
      ::tensorflow::ListValue* list_value_;
      ::tensorflow::TupleValue* tuple_value_;
      ::tensorflow::DictValue* dict_value_;
      ::tensorflow::NamedTupleValue* named_tuple_value_;
      ::tensorflow::TensorProto* tensor_value_;
      ::tensorflow::TensorProto* numpy_value_;
    } kind_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
    uint32_t _oneof_case_[1];

  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class NoneValue final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:tensorflow.NoneValue) */ {
 public:
  inline NoneValue() : NoneValue(nullptr) {}
  explicit PROTOBUF_CONSTEXPR NoneValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NoneValue(const NoneValue& from);
  NoneValue(NoneValue&& from) noexcept
    : NoneValue() {
    *this = ::std::move(from);
  }

  inline NoneValue& operator=(const NoneValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline NoneValue& operator=(NoneValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NoneValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const NoneValue* internal_default_instance() {
    return reinterpret_cast<const NoneValue*>(
               &_NoneValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(NoneValue& a, NoneValue& b) {
    a.Swap(&b);
  }
  inline void Swap(NoneValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NoneValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NoneValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NoneValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const NoneValue& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(*this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const NoneValue& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(*this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NoneValue";
  }
  protected:
  explicit NoneValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:tensorflow.NoneValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
  };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class ListValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ListValue) */ {
 public:
  inline ListValue() : ListValue(nullptr) {}
  ~ListValue() override;
  explicit PROTOBUF_CONSTEXPR ListValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ListValue(const ListValue& from);
  ListValue(ListValue&& from) noexcept
    : ListValue() {
    *this = ::std::move(from);
  }

  inline ListValue& operator=(const ListValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline ListValue& operator=(ListValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ListValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const ListValue* internal_default_instance() {
    return reinterpret_cast<const ListValue*>(
               &_ListValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ListValue& a, ListValue& b) {
    a.Swap(&b);
  }
  inline void Swap(ListValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ListValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ListValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ListValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ListValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ListValue& from) {
    ListValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ListValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ListValue";
  }
  protected:
  explicit ListValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 1,
  };
  // repeated .tensorflow.StructuredValue values = 1;
  int values_size() const;
  private:
  int _internal_values_size() const;
  public:
  void clear_values();
  ::tensorflow::StructuredValue* mutable_values(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
      mutable_values();
  private:
  const ::tensorflow::StructuredValue& _internal_values(int index) const;
  ::tensorflow::StructuredValue* _internal_add_values();
  public:
  const ::tensorflow::StructuredValue& values(int index) const;
  ::tensorflow::StructuredValue* add_values();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
      values() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ListValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue > values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class TupleValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TupleValue) */ {
 public:
  inline TupleValue() : TupleValue(nullptr) {}
  ~TupleValue() override;
  explicit PROTOBUF_CONSTEXPR TupleValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TupleValue(const TupleValue& from);
  TupleValue(TupleValue&& from) noexcept
    : TupleValue() {
    *this = ::std::move(from);
  }

  inline TupleValue& operator=(const TupleValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline TupleValue& operator=(TupleValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TupleValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const TupleValue* internal_default_instance() {
    return reinterpret_cast<const TupleValue*>(
               &_TupleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TupleValue& a, TupleValue& b) {
    a.Swap(&b);
  }
  inline void Swap(TupleValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TupleValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TupleValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TupleValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TupleValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TupleValue& from) {
    TupleValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TupleValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TupleValue";
  }
  protected:
  explicit TupleValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 1,
  };
  // repeated .tensorflow.StructuredValue values = 1;
  int values_size() const;
  private:
  int _internal_values_size() const;
  public:
  void clear_values();
  ::tensorflow::StructuredValue* mutable_values(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
      mutable_values();
  private:
  const ::tensorflow::StructuredValue& _internal_values(int index) const;
  ::tensorflow::StructuredValue* _internal_add_values();
  public:
  const ::tensorflow::StructuredValue& values(int index) const;
  ::tensorflow::StructuredValue* add_values();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
      values() const;

  // @@protoc_insertion_point(class_scope:tensorflow.TupleValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue > values_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class DictValue_FieldsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DictValue_FieldsEntry_DoNotUse, 
    std::string, ::tensorflow::StructuredValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DictValue_FieldsEntry_DoNotUse, 
    std::string, ::tensorflow::StructuredValue,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  DictValue_FieldsEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR DictValue_FieldsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DictValue_FieldsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DictValue_FieldsEntry_DoNotUse& other);
  static const DictValue_FieldsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DictValue_FieldsEntry_DoNotUse*>(&_DictValue_FieldsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "tensorflow.DictValue.FieldsEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};

// -------------------------------------------------------------------

class DictValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.DictValue) */ {
 public:
  inline DictValue() : DictValue(nullptr) {}
  ~DictValue() override;
  explicit PROTOBUF_CONSTEXPR DictValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DictValue(const DictValue& from);
  DictValue(DictValue&& from) noexcept
    : DictValue() {
    *this = ::std::move(from);
  }

  inline DictValue& operator=(const DictValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline DictValue& operator=(DictValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DictValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const DictValue* internal_default_instance() {
    return reinterpret_cast<const DictValue*>(
               &_DictValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(DictValue& a, DictValue& b) {
    a.Swap(&b);
  }
  inline void Swap(DictValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DictValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DictValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DictValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DictValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DictValue& from) {
    DictValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DictValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.DictValue";
  }
  protected:
  explicit DictValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kFieldsFieldNumber = 1,
  };
  // map<string, .tensorflow.StructuredValue> fields = 1;
  int fields_size() const;
  private:
  int _internal_fields_size() const;
  public:
  void clear_fields();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >&
      _internal_fields() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >*
      _internal_mutable_fields();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >&
      fields() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >*
      mutable_fields();

  // @@protoc_insertion_point(class_scope:tensorflow.DictValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        DictValue_FieldsEntry_DoNotUse,
        std::string, ::tensorflow::StructuredValue,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> fields_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class PairValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.PairValue) */ {
 public:
  inline PairValue() : PairValue(nullptr) {}
  ~PairValue() override;
  explicit PROTOBUF_CONSTEXPR PairValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PairValue(const PairValue& from);
  PairValue(PairValue&& from) noexcept
    : PairValue() {
    *this = ::std::move(from);
  }

  inline PairValue& operator=(const PairValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline PairValue& operator=(PairValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PairValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const PairValue* internal_default_instance() {
    return reinterpret_cast<const PairValue*>(
               &_PairValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PairValue& a, PairValue& b) {
    a.Swap(&b);
  }
  inline void Swap(PairValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PairValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PairValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PairValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PairValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const PairValue& from) {
    PairValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PairValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.PairValue";
  }
  protected:
  explicit PairValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // .tensorflow.StructuredValue value = 2;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::tensorflow::StructuredValue& value() const;
  PROTOBUF_NODISCARD ::tensorflow::StructuredValue* release_value();
  ::tensorflow::StructuredValue* mutable_value();
  void set_allocated_value(::tensorflow::StructuredValue* value);
  private:
  const ::tensorflow::StructuredValue& _internal_value() const;
  ::tensorflow::StructuredValue* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::tensorflow::StructuredValue* value);
  ::tensorflow::StructuredValue* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:tensorflow.PairValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
    ::tensorflow::StructuredValue* value_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class NamedTupleValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.NamedTupleValue) */ {
 public:
  inline NamedTupleValue() : NamedTupleValue(nullptr) {}
  ~NamedTupleValue() override;
  explicit PROTOBUF_CONSTEXPR NamedTupleValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NamedTupleValue(const NamedTupleValue& from);
  NamedTupleValue(NamedTupleValue&& from) noexcept
    : NamedTupleValue() {
    *this = ::std::move(from);
  }

  inline NamedTupleValue& operator=(const NamedTupleValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline NamedTupleValue& operator=(NamedTupleValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NamedTupleValue& default_instance() {
    return *internal_default_instance();
  }
  static inline const NamedTupleValue* internal_default_instance() {
    return reinterpret_cast<const NamedTupleValue*>(
               &_NamedTupleValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(NamedTupleValue& a, NamedTupleValue& b) {
    a.Swap(&b);
  }
  inline void Swap(NamedTupleValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NamedTupleValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NamedTupleValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NamedTupleValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NamedTupleValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const NamedTupleValue& from) {
    NamedTupleValue::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NamedTupleValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.NamedTupleValue";
  }
  protected:
  explicit NamedTupleValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kValuesFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // repeated .tensorflow.PairValue values = 2;
  int values_size() const;
  private:
  int _internal_values_size() const;
  public:
  void clear_values();
  ::tensorflow::PairValue* mutable_values(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >*
      mutable_values();
  private:
  const ::tensorflow::PairValue& _internal_values(int index) const;
  ::tensorflow::PairValue* _internal_add_values();
  public:
  const ::tensorflow::PairValue& values(int index) const;
  ::tensorflow::PairValue* add_values();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >&
      values() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.NamedTupleValue)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue > values_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class TensorSpecProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TensorSpecProto) */ {
 public:
  inline TensorSpecProto() : TensorSpecProto(nullptr) {}
  ~TensorSpecProto() override;
  explicit PROTOBUF_CONSTEXPR TensorSpecProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TensorSpecProto(const TensorSpecProto& from);
  TensorSpecProto(TensorSpecProto&& from) noexcept
    : TensorSpecProto() {
    *this = ::std::move(from);
  }

  inline TensorSpecProto& operator=(const TensorSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TensorSpecProto& operator=(TensorSpecProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TensorSpecProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TensorSpecProto* internal_default_instance() {
    return reinterpret_cast<const TensorSpecProto*>(
               &_TensorSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(TensorSpecProto& a, TensorSpecProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TensorSpecProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TensorSpecProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TensorSpecProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TensorSpecProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TensorSpecProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TensorSpecProto& from) {
    TensorSpecProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TensorSpecProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TensorSpecProto";
  }
  protected:
  explicit TensorSpecProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kShapeFieldNumber = 2,
    kDtypeFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TensorSpecProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::TensorShapeProto* shape_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class BoundedTensorSpecProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.BoundedTensorSpecProto) */ {
 public:
  inline BoundedTensorSpecProto() : BoundedTensorSpecProto(nullptr) {}
  ~BoundedTensorSpecProto() override;
  explicit PROTOBUF_CONSTEXPR BoundedTensorSpecProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BoundedTensorSpecProto(const BoundedTensorSpecProto& from);
  BoundedTensorSpecProto(BoundedTensorSpecProto&& from) noexcept
    : BoundedTensorSpecProto() {
    *this = ::std::move(from);
  }

  inline BoundedTensorSpecProto& operator=(const BoundedTensorSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline BoundedTensorSpecProto& operator=(BoundedTensorSpecProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BoundedTensorSpecProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const BoundedTensorSpecProto* internal_default_instance() {
    return reinterpret_cast<const BoundedTensorSpecProto*>(
               &_BoundedTensorSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(BoundedTensorSpecProto& a, BoundedTensorSpecProto& b) {
    a.Swap(&b);
  }
  inline void Swap(BoundedTensorSpecProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BoundedTensorSpecProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BoundedTensorSpecProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BoundedTensorSpecProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BoundedTensorSpecProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const BoundedTensorSpecProto& from) {
    BoundedTensorSpecProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BoundedTensorSpecProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.BoundedTensorSpecProto";
  }
  protected:
  explicit BoundedTensorSpecProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kShapeFieldNumber = 2,
    kMinimumFieldNumber = 4,
    kMaximumFieldNumber = 5,
    kDtypeFieldNumber = 3,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .tensorflow.TensorShapeProto shape = 2;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::tensorflow::TensorShapeProto& shape() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorShapeProto* release_shape();
  ::tensorflow::TensorShapeProto* mutable_shape();
  void set_allocated_shape(::tensorflow::TensorShapeProto* shape);
  private:
  const ::tensorflow::TensorShapeProto& _internal_shape() const;
  ::tensorflow::TensorShapeProto* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::tensorflow::TensorShapeProto* shape);
  ::tensorflow::TensorShapeProto* unsafe_arena_release_shape();

  // .tensorflow.TensorProto minimum = 4;
  bool has_minimum() const;
  private:
  bool _internal_has_minimum() const;
  public:
  void clear_minimum();
  const ::tensorflow::TensorProto& minimum() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_minimum();
  ::tensorflow::TensorProto* mutable_minimum();
  void set_allocated_minimum(::tensorflow::TensorProto* minimum);
  private:
  const ::tensorflow::TensorProto& _internal_minimum() const;
  ::tensorflow::TensorProto* _internal_mutable_minimum();
  public:
  void unsafe_arena_set_allocated_minimum(
      ::tensorflow::TensorProto* minimum);
  ::tensorflow::TensorProto* unsafe_arena_release_minimum();

  // .tensorflow.TensorProto maximum = 5;
  bool has_maximum() const;
  private:
  bool _internal_has_maximum() const;
  public:
  void clear_maximum();
  const ::tensorflow::TensorProto& maximum() const;
  PROTOBUF_NODISCARD ::tensorflow::TensorProto* release_maximum();
  ::tensorflow::TensorProto* mutable_maximum();
  void set_allocated_maximum(::tensorflow::TensorProto* maximum);
  private:
  const ::tensorflow::TensorProto& _internal_maximum() const;
  ::tensorflow::TensorProto* _internal_mutable_maximum();
  public:
  void unsafe_arena_set_allocated_maximum(
      ::tensorflow::TensorProto* maximum);
  ::tensorflow::TensorProto* unsafe_arena_release_maximum();

  // .tensorflow.DataType dtype = 3;
  void clear_dtype();
  ::tensorflow::DataType dtype() const;
  void set_dtype(::tensorflow::DataType value);
  private:
  ::tensorflow::DataType _internal_dtype() const;
  void _internal_set_dtype(::tensorflow::DataType value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.BoundedTensorSpecProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::tensorflow::TensorShapeProto* shape_;
    ::tensorflow::TensorProto* minimum_;
    ::tensorflow::TensorProto* maximum_;
    int dtype_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// -------------------------------------------------------------------

class TypeSpecProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TypeSpecProto) */ {
 public:
  inline TypeSpecProto() : TypeSpecProto(nullptr) {}
  ~TypeSpecProto() override;
  explicit PROTOBUF_CONSTEXPR TypeSpecProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TypeSpecProto(const TypeSpecProto& from);
  TypeSpecProto(TypeSpecProto&& from) noexcept
    : TypeSpecProto() {
    *this = ::std::move(from);
  }

  inline TypeSpecProto& operator=(const TypeSpecProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TypeSpecProto& operator=(TypeSpecProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TypeSpecProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TypeSpecProto* internal_default_instance() {
    return reinterpret_cast<const TypeSpecProto*>(
               &_TypeSpecProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(TypeSpecProto& a, TypeSpecProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TypeSpecProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TypeSpecProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TypeSpecProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TypeSpecProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TypeSpecProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TypeSpecProto& from) {
    TypeSpecProto::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TypeSpecProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TypeSpecProto";
  }
  protected:
  explicit TypeSpecProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef TypeSpecProto_TypeSpecClass TypeSpecClass;
  static constexpr TypeSpecClass UNKNOWN =
    TypeSpecProto_TypeSpecClass_UNKNOWN;
  static constexpr TypeSpecClass SPARSE_TENSOR_SPEC =
    TypeSpecProto_TypeSpecClass_SPARSE_TENSOR_SPEC;
  static constexpr TypeSpecClass INDEXED_SLICES_SPEC =
    TypeSpecProto_TypeSpecClass_INDEXED_SLICES_SPEC;
  static constexpr TypeSpecClass RAGGED_TENSOR_SPEC =
    TypeSpecProto_TypeSpecClass_RAGGED_TENSOR_SPEC;
  static constexpr TypeSpecClass TENSOR_ARRAY_SPEC =
    TypeSpecProto_TypeSpecClass_TENSOR_ARRAY_SPEC;
  static constexpr TypeSpecClass DATA_DATASET_SPEC =
    TypeSpecProto_TypeSpecClass_DATA_DATASET_SPEC;
  static constexpr TypeSpecClass DATA_ITERATOR_SPEC =
    TypeSpecProto_TypeSpecClass_DATA_ITERATOR_SPEC;
  static constexpr TypeSpecClass OPTIONAL_SPEC =
    TypeSpecProto_TypeSpecClass_OPTIONAL_SPEC;
  static constexpr TypeSpecClass PER_REPLICA_SPEC =
    TypeSpecProto_TypeSpecClass_PER_REPLICA_SPEC;
  static constexpr TypeSpecClass VARIABLE_SPEC =
    TypeSpecProto_TypeSpecClass_VARIABLE_SPEC;
  static constexpr TypeSpecClass ROW_PARTITION_SPEC =
    TypeSpecProto_TypeSpecClass_ROW_PARTITION_SPEC;
  static constexpr TypeSpecClass REGISTERED_TYPE_SPEC =
    TypeSpecProto_TypeSpecClass_REGISTERED_TYPE_SPEC;
  static constexpr TypeSpecClass EXTENSION_TYPE_SPEC =
    TypeSpecProto_TypeSpecClass_EXTENSION_TYPE_SPEC;
  static inline bool TypeSpecClass_IsValid(int value) {
    return TypeSpecProto_TypeSpecClass_IsValid(value);
  }
  static constexpr TypeSpecClass TypeSpecClass_MIN =
    TypeSpecProto_TypeSpecClass_TypeSpecClass_MIN;
  static constexpr TypeSpecClass TypeSpecClass_MAX =
    TypeSpecProto_TypeSpecClass_TypeSpecClass_MAX;
  static constexpr int TypeSpecClass_ARRAYSIZE =
    TypeSpecProto_TypeSpecClass_TypeSpecClass_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  TypeSpecClass_descriptor() {
    return TypeSpecProto_TypeSpecClass_descriptor();
  }
  template<typename T>
  static inline const std::string& TypeSpecClass_Name(T enum_t_value) {
    static_assert(::std::is_same<T, TypeSpecClass>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function TypeSpecClass_Name.");
    return TypeSpecProto_TypeSpecClass_Name(enum_t_value);
  }
  static inline bool TypeSpecClass_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      TypeSpecClass* value) {
    return TypeSpecProto_TypeSpecClass_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTypeSpecClassNameFieldNumber = 3,
    kTypeStateFieldNumber = 2,
    kTypeSpecClassFieldNumber = 1,
    kNumFlatComponentsFieldNumber = 4,
  };
  // string type_spec_class_name = 3;
  void clear_type_spec_class_name();
  const std::string& type_spec_class_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_type_spec_class_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_type_spec_class_name();
  PROTOBUF_NODISCARD std::string* release_type_spec_class_name();
  void set_allocated_type_spec_class_name(std::string* type_spec_class_name);
  private:
  const std::string& _internal_type_spec_class_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_type_spec_class_name(const std::string& value);
  std::string* _internal_mutable_type_spec_class_name();
  public:

  // .tensorflow.StructuredValue type_state = 2;
  bool has_type_state() const;
  private:
  bool _internal_has_type_state() const;
  public:
  void clear_type_state();
  const ::tensorflow::StructuredValue& type_state() const;
  PROTOBUF_NODISCARD ::tensorflow::StructuredValue* release_type_state();
  ::tensorflow::StructuredValue* mutable_type_state();
  void set_allocated_type_state(::tensorflow::StructuredValue* type_state);
  private:
  const ::tensorflow::StructuredValue& _internal_type_state() const;
  ::tensorflow::StructuredValue* _internal_mutable_type_state();
  public:
  void unsafe_arena_set_allocated_type_state(
      ::tensorflow::StructuredValue* type_state);
  ::tensorflow::StructuredValue* unsafe_arena_release_type_state();

  // .tensorflow.TypeSpecProto.TypeSpecClass type_spec_class = 1;
  void clear_type_spec_class();
  ::tensorflow::TypeSpecProto_TypeSpecClass type_spec_class() const;
  void set_type_spec_class(::tensorflow::TypeSpecProto_TypeSpecClass value);
  private:
  ::tensorflow::TypeSpecProto_TypeSpecClass _internal_type_spec_class() const;
  void _internal_set_type_spec_class(::tensorflow::TypeSpecProto_TypeSpecClass value);
  public:

  // int32 num_flat_components = 4;
  void clear_num_flat_components();
  int32_t num_flat_components() const;
  void set_num_flat_components(int32_t value);
  private:
  int32_t _internal_num_flat_components() const;
  void _internal_set_num_flat_components(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TypeSpecProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr type_spec_class_name_;
    ::tensorflow::StructuredValue* type_state_;
    int type_spec_class_;
    int32_t num_flat_components_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// StructuredValue

// .tensorflow.NoneValue none_value = 1;
inline bool StructuredValue::_internal_has_none_value() const {
  return kind_case() == kNoneValue;
}
inline bool StructuredValue::has_none_value() const {
  return _internal_has_none_value();
}
inline void StructuredValue::set_has_none_value() {
  _impl_._oneof_case_[0] = kNoneValue;
}
inline void StructuredValue::clear_none_value() {
  if (_internal_has_none_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.none_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::NoneValue* StructuredValue::release_none_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.none_value)
  if (_internal_has_none_value()) {
    clear_has_kind();
    ::tensorflow::NoneValue* temp = _impl_.kind_.none_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.none_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NoneValue& StructuredValue::_internal_none_value() const {
  return _internal_has_none_value()
      ? *_impl_.kind_.none_value_
      : reinterpret_cast< ::tensorflow::NoneValue&>(::tensorflow::_NoneValue_default_instance_);
}
inline const ::tensorflow::NoneValue& StructuredValue::none_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.none_value)
  return _internal_none_value();
}
inline ::tensorflow::NoneValue* StructuredValue::unsafe_arena_release_none_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.none_value)
  if (_internal_has_none_value()) {
    clear_has_kind();
    ::tensorflow::NoneValue* temp = _impl_.kind_.none_value_;
    _impl_.kind_.none_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_none_value(::tensorflow::NoneValue* none_value) {
  clear_kind();
  if (none_value) {
    set_has_none_value();
    _impl_.kind_.none_value_ = none_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.none_value)
}
inline ::tensorflow::NoneValue* StructuredValue::_internal_mutable_none_value() {
  if (!_internal_has_none_value()) {
    clear_kind();
    set_has_none_value();
    _impl_.kind_.none_value_ = CreateMaybeMessage< ::tensorflow::NoneValue >(GetArenaForAllocation());
  }
  return _impl_.kind_.none_value_;
}
inline ::tensorflow::NoneValue* StructuredValue::mutable_none_value() {
  ::tensorflow::NoneValue* _msg = _internal_mutable_none_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.none_value)
  return _msg;
}

// double float64_value = 11;
inline bool StructuredValue::_internal_has_float64_value() const {
  return kind_case() == kFloat64Value;
}
inline bool StructuredValue::has_float64_value() const {
  return _internal_has_float64_value();
}
inline void StructuredValue::set_has_float64_value() {
  _impl_._oneof_case_[0] = kFloat64Value;
}
inline void StructuredValue::clear_float64_value() {
  if (_internal_has_float64_value()) {
    _impl_.kind_.float64_value_ = 0;
    clear_has_kind();
  }
}
inline double StructuredValue::_internal_float64_value() const {
  if (_internal_has_float64_value()) {
    return _impl_.kind_.float64_value_;
  }
  return 0;
}
inline void StructuredValue::_internal_set_float64_value(double value) {
  if (!_internal_has_float64_value()) {
    clear_kind();
    set_has_float64_value();
  }
  _impl_.kind_.float64_value_ = value;
}
inline double StructuredValue::float64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.float64_value)
  return _internal_float64_value();
}
inline void StructuredValue::set_float64_value(double value) {
  _internal_set_float64_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.float64_value)
}

// sint64 int64_value = 12;
inline bool StructuredValue::_internal_has_int64_value() const {
  return kind_case() == kInt64Value;
}
inline bool StructuredValue::has_int64_value() const {
  return _internal_has_int64_value();
}
inline void StructuredValue::set_has_int64_value() {
  _impl_._oneof_case_[0] = kInt64Value;
}
inline void StructuredValue::clear_int64_value() {
  if (_internal_has_int64_value()) {
    _impl_.kind_.int64_value_ = int64_t{0};
    clear_has_kind();
  }
}
inline int64_t StructuredValue::_internal_int64_value() const {
  if (_internal_has_int64_value()) {
    return _impl_.kind_.int64_value_;
  }
  return int64_t{0};
}
inline void StructuredValue::_internal_set_int64_value(int64_t value) {
  if (!_internal_has_int64_value()) {
    clear_kind();
    set_has_int64_value();
  }
  _impl_.kind_.int64_value_ = value;
}
inline int64_t StructuredValue::int64_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.int64_value)
  return _internal_int64_value();
}
inline void StructuredValue::set_int64_value(int64_t value) {
  _internal_set_int64_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.int64_value)
}

// string string_value = 13;
inline bool StructuredValue::_internal_has_string_value() const {
  return kind_case() == kStringValue;
}
inline bool StructuredValue::has_string_value() const {
  return _internal_has_string_value();
}
inline void StructuredValue::set_has_string_value() {
  _impl_._oneof_case_[0] = kStringValue;
}
inline void StructuredValue::clear_string_value() {
  if (_internal_has_string_value()) {
    _impl_.kind_.string_value_.Destroy();
    clear_has_kind();
  }
}
inline const std::string& StructuredValue::string_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.string_value)
  return _internal_string_value();
}
template <typename ArgT0, typename... ArgT>
inline void StructuredValue::set_string_value(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_string_value()) {
    clear_kind();
    set_has_string_value();
    _impl_.kind_.string_value_.InitDefault();
  }
  _impl_.kind_.string_value_.Set( static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.string_value)
}
inline std::string* StructuredValue::mutable_string_value() {
  std::string* _s = _internal_mutable_string_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.string_value)
  return _s;
}
inline const std::string& StructuredValue::_internal_string_value() const {
  if (_internal_has_string_value()) {
    return _impl_.kind_.string_value_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void StructuredValue::_internal_set_string_value(const std::string& value) {
  if (!_internal_has_string_value()) {
    clear_kind();
    set_has_string_value();
    _impl_.kind_.string_value_.InitDefault();
  }
  _impl_.kind_.string_value_.Set(value, GetArenaForAllocation());
}
inline std::string* StructuredValue::_internal_mutable_string_value() {
  if (!_internal_has_string_value()) {
    clear_kind();
    set_has_string_value();
    _impl_.kind_.string_value_.InitDefault();
  }
  return _impl_.kind_.string_value_.Mutable(      GetArenaForAllocation());
}
inline std::string* StructuredValue::release_string_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.string_value)
  if (_internal_has_string_value()) {
    clear_has_kind();
    return _impl_.kind_.string_value_.Release();
  } else {
    return nullptr;
  }
}
inline void StructuredValue::set_allocated_string_value(std::string* string_value) {
  if (has_kind()) {
    clear_kind();
  }
  if (string_value != nullptr) {
    set_has_string_value();
    _impl_.kind_.string_value_.InitAllocated(string_value, GetArenaForAllocation());
  }
  // @@protoc_insertion_point(field_set_allocated:tensorflow.StructuredValue.string_value)
}

// bool bool_value = 14;
inline bool StructuredValue::_internal_has_bool_value() const {
  return kind_case() == kBoolValue;
}
inline bool StructuredValue::has_bool_value() const {
  return _internal_has_bool_value();
}
inline void StructuredValue::set_has_bool_value() {
  _impl_._oneof_case_[0] = kBoolValue;
}
inline void StructuredValue::clear_bool_value() {
  if (_internal_has_bool_value()) {
    _impl_.kind_.bool_value_ = false;
    clear_has_kind();
  }
}
inline bool StructuredValue::_internal_bool_value() const {
  if (_internal_has_bool_value()) {
    return _impl_.kind_.bool_value_;
  }
  return false;
}
inline void StructuredValue::_internal_set_bool_value(bool value) {
  if (!_internal_has_bool_value()) {
    clear_kind();
    set_has_bool_value();
  }
  _impl_.kind_.bool_value_ = value;
}
inline bool StructuredValue::bool_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.bool_value)
  return _internal_bool_value();
}
inline void StructuredValue::set_bool_value(bool value) {
  _internal_set_bool_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.bool_value)
}

// .tensorflow.TensorShapeProto tensor_shape_value = 31;
inline bool StructuredValue::_internal_has_tensor_shape_value() const {
  return kind_case() == kTensorShapeValue;
}
inline bool StructuredValue::has_tensor_shape_value() const {
  return _internal_has_tensor_shape_value();
}
inline void StructuredValue::set_has_tensor_shape_value() {
  _impl_._oneof_case_[0] = kTensorShapeValue;
}
inline ::tensorflow::TensorShapeProto* StructuredValue::release_tensor_shape_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_shape_value)
  if (_internal_has_tensor_shape_value()) {
    clear_has_kind();
    ::tensorflow::TensorShapeProto* temp = _impl_.kind_.tensor_shape_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.tensor_shape_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorShapeProto& StructuredValue::_internal_tensor_shape_value() const {
  return _internal_has_tensor_shape_value()
      ? *_impl_.kind_.tensor_shape_value_
      : reinterpret_cast< ::tensorflow::TensorShapeProto&>(::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& StructuredValue::tensor_shape_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_shape_value)
  return _internal_tensor_shape_value();
}
inline ::tensorflow::TensorShapeProto* StructuredValue::unsafe_arena_release_tensor_shape_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.tensor_shape_value)
  if (_internal_has_tensor_shape_value()) {
    clear_has_kind();
    ::tensorflow::TensorShapeProto* temp = _impl_.kind_.tensor_shape_value_;
    _impl_.kind_.tensor_shape_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_tensor_shape_value(::tensorflow::TensorShapeProto* tensor_shape_value) {
  clear_kind();
  if (tensor_shape_value) {
    set_has_tensor_shape_value();
    _impl_.kind_.tensor_shape_value_ = tensor_shape_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.tensor_shape_value)
}
inline ::tensorflow::TensorShapeProto* StructuredValue::_internal_mutable_tensor_shape_value() {
  if (!_internal_has_tensor_shape_value()) {
    clear_kind();
    set_has_tensor_shape_value();
    _impl_.kind_.tensor_shape_value_ = CreateMaybeMessage< ::tensorflow::TensorShapeProto >(GetArenaForAllocation());
  }
  return _impl_.kind_.tensor_shape_value_;
}
inline ::tensorflow::TensorShapeProto* StructuredValue::mutable_tensor_shape_value() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_tensor_shape_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_shape_value)
  return _msg;
}

// .tensorflow.DataType tensor_dtype_value = 32;
inline bool StructuredValue::_internal_has_tensor_dtype_value() const {
  return kind_case() == kTensorDtypeValue;
}
inline bool StructuredValue::has_tensor_dtype_value() const {
  return _internal_has_tensor_dtype_value();
}
inline void StructuredValue::set_has_tensor_dtype_value() {
  _impl_._oneof_case_[0] = kTensorDtypeValue;
}
inline void StructuredValue::clear_tensor_dtype_value() {
  if (_internal_has_tensor_dtype_value()) {
    _impl_.kind_.tensor_dtype_value_ = 0;
    clear_has_kind();
  }
}
inline ::tensorflow::DataType StructuredValue::_internal_tensor_dtype_value() const {
  if (_internal_has_tensor_dtype_value()) {
    return static_cast< ::tensorflow::DataType >(_impl_.kind_.tensor_dtype_value_);
  }
  return static_cast< ::tensorflow::DataType >(0);
}
inline ::tensorflow::DataType StructuredValue::tensor_dtype_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_dtype_value)
  return _internal_tensor_dtype_value();
}
inline void StructuredValue::_internal_set_tensor_dtype_value(::tensorflow::DataType value) {
  if (!_internal_has_tensor_dtype_value()) {
    clear_kind();
    set_has_tensor_dtype_value();
  }
  _impl_.kind_.tensor_dtype_value_ = value;
}
inline void StructuredValue::set_tensor_dtype_value(::tensorflow::DataType value) {
  _internal_set_tensor_dtype_value(value);
  // @@protoc_insertion_point(field_set:tensorflow.StructuredValue.tensor_dtype_value)
}

// .tensorflow.TensorSpecProto tensor_spec_value = 33;
inline bool StructuredValue::_internal_has_tensor_spec_value() const {
  return kind_case() == kTensorSpecValue;
}
inline bool StructuredValue::has_tensor_spec_value() const {
  return _internal_has_tensor_spec_value();
}
inline void StructuredValue::set_has_tensor_spec_value() {
  _impl_._oneof_case_[0] = kTensorSpecValue;
}
inline void StructuredValue::clear_tensor_spec_value() {
  if (_internal_has_tensor_spec_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.tensor_spec_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::TensorSpecProto* StructuredValue::release_tensor_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_spec_value)
  if (_internal_has_tensor_spec_value()) {
    clear_has_kind();
    ::tensorflow::TensorSpecProto* temp = _impl_.kind_.tensor_spec_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.tensor_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorSpecProto& StructuredValue::_internal_tensor_spec_value() const {
  return _internal_has_tensor_spec_value()
      ? *_impl_.kind_.tensor_spec_value_
      : reinterpret_cast< ::tensorflow::TensorSpecProto&>(::tensorflow::_TensorSpecProto_default_instance_);
}
inline const ::tensorflow::TensorSpecProto& StructuredValue::tensor_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_spec_value)
  return _internal_tensor_spec_value();
}
inline ::tensorflow::TensorSpecProto* StructuredValue::unsafe_arena_release_tensor_spec_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.tensor_spec_value)
  if (_internal_has_tensor_spec_value()) {
    clear_has_kind();
    ::tensorflow::TensorSpecProto* temp = _impl_.kind_.tensor_spec_value_;
    _impl_.kind_.tensor_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_tensor_spec_value(::tensorflow::TensorSpecProto* tensor_spec_value) {
  clear_kind();
  if (tensor_spec_value) {
    set_has_tensor_spec_value();
    _impl_.kind_.tensor_spec_value_ = tensor_spec_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.tensor_spec_value)
}
inline ::tensorflow::TensorSpecProto* StructuredValue::_internal_mutable_tensor_spec_value() {
  if (!_internal_has_tensor_spec_value()) {
    clear_kind();
    set_has_tensor_spec_value();
    _impl_.kind_.tensor_spec_value_ = CreateMaybeMessage< ::tensorflow::TensorSpecProto >(GetArenaForAllocation());
  }
  return _impl_.kind_.tensor_spec_value_;
}
inline ::tensorflow::TensorSpecProto* StructuredValue::mutable_tensor_spec_value() {
  ::tensorflow::TensorSpecProto* _msg = _internal_mutable_tensor_spec_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_spec_value)
  return _msg;
}

// .tensorflow.TypeSpecProto type_spec_value = 34;
inline bool StructuredValue::_internal_has_type_spec_value() const {
  return kind_case() == kTypeSpecValue;
}
inline bool StructuredValue::has_type_spec_value() const {
  return _internal_has_type_spec_value();
}
inline void StructuredValue::set_has_type_spec_value() {
  _impl_._oneof_case_[0] = kTypeSpecValue;
}
inline void StructuredValue::clear_type_spec_value() {
  if (_internal_has_type_spec_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.type_spec_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::TypeSpecProto* StructuredValue::release_type_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.type_spec_value)
  if (_internal_has_type_spec_value()) {
    clear_has_kind();
    ::tensorflow::TypeSpecProto* temp = _impl_.kind_.type_spec_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.type_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TypeSpecProto& StructuredValue::_internal_type_spec_value() const {
  return _internal_has_type_spec_value()
      ? *_impl_.kind_.type_spec_value_
      : reinterpret_cast< ::tensorflow::TypeSpecProto&>(::tensorflow::_TypeSpecProto_default_instance_);
}
inline const ::tensorflow::TypeSpecProto& StructuredValue::type_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.type_spec_value)
  return _internal_type_spec_value();
}
inline ::tensorflow::TypeSpecProto* StructuredValue::unsafe_arena_release_type_spec_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.type_spec_value)
  if (_internal_has_type_spec_value()) {
    clear_has_kind();
    ::tensorflow::TypeSpecProto* temp = _impl_.kind_.type_spec_value_;
    _impl_.kind_.type_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_type_spec_value(::tensorflow::TypeSpecProto* type_spec_value) {
  clear_kind();
  if (type_spec_value) {
    set_has_type_spec_value();
    _impl_.kind_.type_spec_value_ = type_spec_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.type_spec_value)
}
inline ::tensorflow::TypeSpecProto* StructuredValue::_internal_mutable_type_spec_value() {
  if (!_internal_has_type_spec_value()) {
    clear_kind();
    set_has_type_spec_value();
    _impl_.kind_.type_spec_value_ = CreateMaybeMessage< ::tensorflow::TypeSpecProto >(GetArenaForAllocation());
  }
  return _impl_.kind_.type_spec_value_;
}
inline ::tensorflow::TypeSpecProto* StructuredValue::mutable_type_spec_value() {
  ::tensorflow::TypeSpecProto* _msg = _internal_mutable_type_spec_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.type_spec_value)
  return _msg;
}

// .tensorflow.BoundedTensorSpecProto bounded_tensor_spec_value = 35;
inline bool StructuredValue::_internal_has_bounded_tensor_spec_value() const {
  return kind_case() == kBoundedTensorSpecValue;
}
inline bool StructuredValue::has_bounded_tensor_spec_value() const {
  return _internal_has_bounded_tensor_spec_value();
}
inline void StructuredValue::set_has_bounded_tensor_spec_value() {
  _impl_._oneof_case_[0] = kBoundedTensorSpecValue;
}
inline void StructuredValue::clear_bounded_tensor_spec_value() {
  if (_internal_has_bounded_tensor_spec_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.bounded_tensor_spec_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::BoundedTensorSpecProto* StructuredValue::release_bounded_tensor_spec_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.bounded_tensor_spec_value)
  if (_internal_has_bounded_tensor_spec_value()) {
    clear_has_kind();
    ::tensorflow::BoundedTensorSpecProto* temp = _impl_.kind_.bounded_tensor_spec_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.bounded_tensor_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::BoundedTensorSpecProto& StructuredValue::_internal_bounded_tensor_spec_value() const {
  return _internal_has_bounded_tensor_spec_value()
      ? *_impl_.kind_.bounded_tensor_spec_value_
      : reinterpret_cast< ::tensorflow::BoundedTensorSpecProto&>(::tensorflow::_BoundedTensorSpecProto_default_instance_);
}
inline const ::tensorflow::BoundedTensorSpecProto& StructuredValue::bounded_tensor_spec_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.bounded_tensor_spec_value)
  return _internal_bounded_tensor_spec_value();
}
inline ::tensorflow::BoundedTensorSpecProto* StructuredValue::unsafe_arena_release_bounded_tensor_spec_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.bounded_tensor_spec_value)
  if (_internal_has_bounded_tensor_spec_value()) {
    clear_has_kind();
    ::tensorflow::BoundedTensorSpecProto* temp = _impl_.kind_.bounded_tensor_spec_value_;
    _impl_.kind_.bounded_tensor_spec_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_bounded_tensor_spec_value(::tensorflow::BoundedTensorSpecProto* bounded_tensor_spec_value) {
  clear_kind();
  if (bounded_tensor_spec_value) {
    set_has_bounded_tensor_spec_value();
    _impl_.kind_.bounded_tensor_spec_value_ = bounded_tensor_spec_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.bounded_tensor_spec_value)
}
inline ::tensorflow::BoundedTensorSpecProto* StructuredValue::_internal_mutable_bounded_tensor_spec_value() {
  if (!_internal_has_bounded_tensor_spec_value()) {
    clear_kind();
    set_has_bounded_tensor_spec_value();
    _impl_.kind_.bounded_tensor_spec_value_ = CreateMaybeMessage< ::tensorflow::BoundedTensorSpecProto >(GetArenaForAllocation());
  }
  return _impl_.kind_.bounded_tensor_spec_value_;
}
inline ::tensorflow::BoundedTensorSpecProto* StructuredValue::mutable_bounded_tensor_spec_value() {
  ::tensorflow::BoundedTensorSpecProto* _msg = _internal_mutable_bounded_tensor_spec_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.bounded_tensor_spec_value)
  return _msg;
}

// .tensorflow.ListValue list_value = 51;
inline bool StructuredValue::_internal_has_list_value() const {
  return kind_case() == kListValue;
}
inline bool StructuredValue::has_list_value() const {
  return _internal_has_list_value();
}
inline void StructuredValue::set_has_list_value() {
  _impl_._oneof_case_[0] = kListValue;
}
inline void StructuredValue::clear_list_value() {
  if (_internal_has_list_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.list_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::ListValue* StructuredValue::release_list_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.list_value)
  if (_internal_has_list_value()) {
    clear_has_kind();
    ::tensorflow::ListValue* temp = _impl_.kind_.list_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.list_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::ListValue& StructuredValue::_internal_list_value() const {
  return _internal_has_list_value()
      ? *_impl_.kind_.list_value_
      : reinterpret_cast< ::tensorflow::ListValue&>(::tensorflow::_ListValue_default_instance_);
}
inline const ::tensorflow::ListValue& StructuredValue::list_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.list_value)
  return _internal_list_value();
}
inline ::tensorflow::ListValue* StructuredValue::unsafe_arena_release_list_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.list_value)
  if (_internal_has_list_value()) {
    clear_has_kind();
    ::tensorflow::ListValue* temp = _impl_.kind_.list_value_;
    _impl_.kind_.list_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_list_value(::tensorflow::ListValue* list_value) {
  clear_kind();
  if (list_value) {
    set_has_list_value();
    _impl_.kind_.list_value_ = list_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.list_value)
}
inline ::tensorflow::ListValue* StructuredValue::_internal_mutable_list_value() {
  if (!_internal_has_list_value()) {
    clear_kind();
    set_has_list_value();
    _impl_.kind_.list_value_ = CreateMaybeMessage< ::tensorflow::ListValue >(GetArenaForAllocation());
  }
  return _impl_.kind_.list_value_;
}
inline ::tensorflow::ListValue* StructuredValue::mutable_list_value() {
  ::tensorflow::ListValue* _msg = _internal_mutable_list_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.list_value)
  return _msg;
}

// .tensorflow.TupleValue tuple_value = 52;
inline bool StructuredValue::_internal_has_tuple_value() const {
  return kind_case() == kTupleValue;
}
inline bool StructuredValue::has_tuple_value() const {
  return _internal_has_tuple_value();
}
inline void StructuredValue::set_has_tuple_value() {
  _impl_._oneof_case_[0] = kTupleValue;
}
inline void StructuredValue::clear_tuple_value() {
  if (_internal_has_tuple_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.tuple_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::TupleValue* StructuredValue::release_tuple_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tuple_value)
  if (_internal_has_tuple_value()) {
    clear_has_kind();
    ::tensorflow::TupleValue* temp = _impl_.kind_.tuple_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.tuple_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TupleValue& StructuredValue::_internal_tuple_value() const {
  return _internal_has_tuple_value()
      ? *_impl_.kind_.tuple_value_
      : reinterpret_cast< ::tensorflow::TupleValue&>(::tensorflow::_TupleValue_default_instance_);
}
inline const ::tensorflow::TupleValue& StructuredValue::tuple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tuple_value)
  return _internal_tuple_value();
}
inline ::tensorflow::TupleValue* StructuredValue::unsafe_arena_release_tuple_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.tuple_value)
  if (_internal_has_tuple_value()) {
    clear_has_kind();
    ::tensorflow::TupleValue* temp = _impl_.kind_.tuple_value_;
    _impl_.kind_.tuple_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_tuple_value(::tensorflow::TupleValue* tuple_value) {
  clear_kind();
  if (tuple_value) {
    set_has_tuple_value();
    _impl_.kind_.tuple_value_ = tuple_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.tuple_value)
}
inline ::tensorflow::TupleValue* StructuredValue::_internal_mutable_tuple_value() {
  if (!_internal_has_tuple_value()) {
    clear_kind();
    set_has_tuple_value();
    _impl_.kind_.tuple_value_ = CreateMaybeMessage< ::tensorflow::TupleValue >(GetArenaForAllocation());
  }
  return _impl_.kind_.tuple_value_;
}
inline ::tensorflow::TupleValue* StructuredValue::mutable_tuple_value() {
  ::tensorflow::TupleValue* _msg = _internal_mutable_tuple_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tuple_value)
  return _msg;
}

// .tensorflow.DictValue dict_value = 53;
inline bool StructuredValue::_internal_has_dict_value() const {
  return kind_case() == kDictValue;
}
inline bool StructuredValue::has_dict_value() const {
  return _internal_has_dict_value();
}
inline void StructuredValue::set_has_dict_value() {
  _impl_._oneof_case_[0] = kDictValue;
}
inline void StructuredValue::clear_dict_value() {
  if (_internal_has_dict_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.dict_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::DictValue* StructuredValue::release_dict_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.dict_value)
  if (_internal_has_dict_value()) {
    clear_has_kind();
    ::tensorflow::DictValue* temp = _impl_.kind_.dict_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.dict_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::DictValue& StructuredValue::_internal_dict_value() const {
  return _internal_has_dict_value()
      ? *_impl_.kind_.dict_value_
      : reinterpret_cast< ::tensorflow::DictValue&>(::tensorflow::_DictValue_default_instance_);
}
inline const ::tensorflow::DictValue& StructuredValue::dict_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.dict_value)
  return _internal_dict_value();
}
inline ::tensorflow::DictValue* StructuredValue::unsafe_arena_release_dict_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.dict_value)
  if (_internal_has_dict_value()) {
    clear_has_kind();
    ::tensorflow::DictValue* temp = _impl_.kind_.dict_value_;
    _impl_.kind_.dict_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_dict_value(::tensorflow::DictValue* dict_value) {
  clear_kind();
  if (dict_value) {
    set_has_dict_value();
    _impl_.kind_.dict_value_ = dict_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.dict_value)
}
inline ::tensorflow::DictValue* StructuredValue::_internal_mutable_dict_value() {
  if (!_internal_has_dict_value()) {
    clear_kind();
    set_has_dict_value();
    _impl_.kind_.dict_value_ = CreateMaybeMessage< ::tensorflow::DictValue >(GetArenaForAllocation());
  }
  return _impl_.kind_.dict_value_;
}
inline ::tensorflow::DictValue* StructuredValue::mutable_dict_value() {
  ::tensorflow::DictValue* _msg = _internal_mutable_dict_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.dict_value)
  return _msg;
}

// .tensorflow.NamedTupleValue named_tuple_value = 54;
inline bool StructuredValue::_internal_has_named_tuple_value() const {
  return kind_case() == kNamedTupleValue;
}
inline bool StructuredValue::has_named_tuple_value() const {
  return _internal_has_named_tuple_value();
}
inline void StructuredValue::set_has_named_tuple_value() {
  _impl_._oneof_case_[0] = kNamedTupleValue;
}
inline void StructuredValue::clear_named_tuple_value() {
  if (_internal_has_named_tuple_value()) {
    if (GetArenaForAllocation() == nullptr) {
      delete _impl_.kind_.named_tuple_value_;
    }
    clear_has_kind();
  }
}
inline ::tensorflow::NamedTupleValue* StructuredValue::release_named_tuple_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.named_tuple_value)
  if (_internal_has_named_tuple_value()) {
    clear_has_kind();
    ::tensorflow::NamedTupleValue* temp = _impl_.kind_.named_tuple_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.named_tuple_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::NamedTupleValue& StructuredValue::_internal_named_tuple_value() const {
  return _internal_has_named_tuple_value()
      ? *_impl_.kind_.named_tuple_value_
      : reinterpret_cast< ::tensorflow::NamedTupleValue&>(::tensorflow::_NamedTupleValue_default_instance_);
}
inline const ::tensorflow::NamedTupleValue& StructuredValue::named_tuple_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.named_tuple_value)
  return _internal_named_tuple_value();
}
inline ::tensorflow::NamedTupleValue* StructuredValue::unsafe_arena_release_named_tuple_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.named_tuple_value)
  if (_internal_has_named_tuple_value()) {
    clear_has_kind();
    ::tensorflow::NamedTupleValue* temp = _impl_.kind_.named_tuple_value_;
    _impl_.kind_.named_tuple_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_named_tuple_value(::tensorflow::NamedTupleValue* named_tuple_value) {
  clear_kind();
  if (named_tuple_value) {
    set_has_named_tuple_value();
    _impl_.kind_.named_tuple_value_ = named_tuple_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.named_tuple_value)
}
inline ::tensorflow::NamedTupleValue* StructuredValue::_internal_mutable_named_tuple_value() {
  if (!_internal_has_named_tuple_value()) {
    clear_kind();
    set_has_named_tuple_value();
    _impl_.kind_.named_tuple_value_ = CreateMaybeMessage< ::tensorflow::NamedTupleValue >(GetArenaForAllocation());
  }
  return _impl_.kind_.named_tuple_value_;
}
inline ::tensorflow::NamedTupleValue* StructuredValue::mutable_named_tuple_value() {
  ::tensorflow::NamedTupleValue* _msg = _internal_mutable_named_tuple_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.named_tuple_value)
  return _msg;
}

// .tensorflow.TensorProto tensor_value = 55;
inline bool StructuredValue::_internal_has_tensor_value() const {
  return kind_case() == kTensorValue;
}
inline bool StructuredValue::has_tensor_value() const {
  return _internal_has_tensor_value();
}
inline void StructuredValue::set_has_tensor_value() {
  _impl_._oneof_case_[0] = kTensorValue;
}
inline ::tensorflow::TensorProto* StructuredValue::release_tensor_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.tensor_value)
  if (_internal_has_tensor_value()) {
    clear_has_kind();
    ::tensorflow::TensorProto* temp = _impl_.kind_.tensor_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.tensor_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorProto& StructuredValue::_internal_tensor_value() const {
  return _internal_has_tensor_value()
      ? *_impl_.kind_.tensor_value_
      : reinterpret_cast< ::tensorflow::TensorProto&>(::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& StructuredValue::tensor_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.tensor_value)
  return _internal_tensor_value();
}
inline ::tensorflow::TensorProto* StructuredValue::unsafe_arena_release_tensor_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.tensor_value)
  if (_internal_has_tensor_value()) {
    clear_has_kind();
    ::tensorflow::TensorProto* temp = _impl_.kind_.tensor_value_;
    _impl_.kind_.tensor_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_tensor_value(::tensorflow::TensorProto* tensor_value) {
  clear_kind();
  if (tensor_value) {
    set_has_tensor_value();
    _impl_.kind_.tensor_value_ = tensor_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.tensor_value)
}
inline ::tensorflow::TensorProto* StructuredValue::_internal_mutable_tensor_value() {
  if (!_internal_has_tensor_value()) {
    clear_kind();
    set_has_tensor_value();
    _impl_.kind_.tensor_value_ = CreateMaybeMessage< ::tensorflow::TensorProto >(GetArenaForAllocation());
  }
  return _impl_.kind_.tensor_value_;
}
inline ::tensorflow::TensorProto* StructuredValue::mutable_tensor_value() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_tensor_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.tensor_value)
  return _msg;
}

// .tensorflow.TensorProto numpy_value = 56;
inline bool StructuredValue::_internal_has_numpy_value() const {
  return kind_case() == kNumpyValue;
}
inline bool StructuredValue::has_numpy_value() const {
  return _internal_has_numpy_value();
}
inline void StructuredValue::set_has_numpy_value() {
  _impl_._oneof_case_[0] = kNumpyValue;
}
inline ::tensorflow::TensorProto* StructuredValue::release_numpy_value() {
  // @@protoc_insertion_point(field_release:tensorflow.StructuredValue.numpy_value)
  if (_internal_has_numpy_value()) {
    clear_has_kind();
    ::tensorflow::TensorProto* temp = _impl_.kind_.numpy_value_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    _impl_.kind_.numpy_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::tensorflow::TensorProto& StructuredValue::_internal_numpy_value() const {
  return _internal_has_numpy_value()
      ? *_impl_.kind_.numpy_value_
      : reinterpret_cast< ::tensorflow::TensorProto&>(::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& StructuredValue::numpy_value() const {
  // @@protoc_insertion_point(field_get:tensorflow.StructuredValue.numpy_value)
  return _internal_numpy_value();
}
inline ::tensorflow::TensorProto* StructuredValue::unsafe_arena_release_numpy_value() {
  // @@protoc_insertion_point(field_unsafe_arena_release:tensorflow.StructuredValue.numpy_value)
  if (_internal_has_numpy_value()) {
    clear_has_kind();
    ::tensorflow::TensorProto* temp = _impl_.kind_.numpy_value_;
    _impl_.kind_.numpy_value_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void StructuredValue::unsafe_arena_set_allocated_numpy_value(::tensorflow::TensorProto* numpy_value) {
  clear_kind();
  if (numpy_value) {
    set_has_numpy_value();
    _impl_.kind_.numpy_value_ = numpy_value;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.StructuredValue.numpy_value)
}
inline ::tensorflow::TensorProto* StructuredValue::_internal_mutable_numpy_value() {
  if (!_internal_has_numpy_value()) {
    clear_kind();
    set_has_numpy_value();
    _impl_.kind_.numpy_value_ = CreateMaybeMessage< ::tensorflow::TensorProto >(GetArenaForAllocation());
  }
  return _impl_.kind_.numpy_value_;
}
inline ::tensorflow::TensorProto* StructuredValue::mutable_numpy_value() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_numpy_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.StructuredValue.numpy_value)
  return _msg;
}

inline bool StructuredValue::has_kind() const {
  return kind_case() != KIND_NOT_SET;
}
inline void StructuredValue::clear_has_kind() {
  _impl_._oneof_case_[0] = KIND_NOT_SET;
}
inline StructuredValue::KindCase StructuredValue::kind_case() const {
  return StructuredValue::KindCase(_impl_._oneof_case_[0]);
}
// -------------------------------------------------------------------

// NoneValue

// -------------------------------------------------------------------

// ListValue

// repeated .tensorflow.StructuredValue values = 1;
inline int ListValue::_internal_values_size() const {
  return _impl_.values_.size();
}
inline int ListValue::values_size() const {
  return _internal_values_size();
}
inline void ListValue::clear_values() {
  _impl_.values_.Clear();
}
inline ::tensorflow::StructuredValue* ListValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ListValue.values)
  return _impl_.values_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
ListValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ListValue.values)
  return &_impl_.values_;
}
inline const ::tensorflow::StructuredValue& ListValue::_internal_values(int index) const {
  return _impl_.values_.Get(index);
}
inline const ::tensorflow::StructuredValue& ListValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ListValue.values)
  return _internal_values(index);
}
inline ::tensorflow::StructuredValue* ListValue::_internal_add_values() {
  return _impl_.values_.Add();
}
inline ::tensorflow::StructuredValue* ListValue::add_values() {
  ::tensorflow::StructuredValue* _add = _internal_add_values();
  // @@protoc_insertion_point(field_add:tensorflow.ListValue.values)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
ListValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.ListValue.values)
  return _impl_.values_;
}

// -------------------------------------------------------------------

// TupleValue

// repeated .tensorflow.StructuredValue values = 1;
inline int TupleValue::_internal_values_size() const {
  return _impl_.values_.size();
}
inline int TupleValue::values_size() const {
  return _internal_values_size();
}
inline void TupleValue::clear_values() {
  _impl_.values_.Clear();
}
inline ::tensorflow::StructuredValue* TupleValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TupleValue.values)
  return _impl_.values_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >*
TupleValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TupleValue.values)
  return &_impl_.values_;
}
inline const ::tensorflow::StructuredValue& TupleValue::_internal_values(int index) const {
  return _impl_.values_.Get(index);
}
inline const ::tensorflow::StructuredValue& TupleValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TupleValue.values)
  return _internal_values(index);
}
inline ::tensorflow::StructuredValue* TupleValue::_internal_add_values() {
  return _impl_.values_.Add();
}
inline ::tensorflow::StructuredValue* TupleValue::add_values() {
  ::tensorflow::StructuredValue* _add = _internal_add_values();
  // @@protoc_insertion_point(field_add:tensorflow.TupleValue.values)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::StructuredValue >&
TupleValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.TupleValue.values)
  return _impl_.values_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DictValue

// map<string, .tensorflow.StructuredValue> fields = 1;
inline int DictValue::_internal_fields_size() const {
  return _impl_.fields_.size();
}
inline int DictValue::fields_size() const {
  return _internal_fields_size();
}
inline void DictValue::clear_fields() {
  _impl_.fields_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >&
DictValue::_internal_fields() const {
  return _impl_.fields_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >&
DictValue::fields() const {
  // @@protoc_insertion_point(field_map:tensorflow.DictValue.fields)
  return _internal_fields();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >*
DictValue::_internal_mutable_fields() {
  return _impl_.fields_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::tensorflow::StructuredValue >*
DictValue::mutable_fields() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.DictValue.fields)
  return _internal_mutable_fields();
}

// -------------------------------------------------------------------

// PairValue

// string key = 1;
inline void PairValue::clear_key() {
  _impl_.key_.ClearToEmpty();
}
inline const std::string& PairValue::key() const {
  // @@protoc_insertion_point(field_get:tensorflow.PairValue.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PairValue::set_key(ArgT0&& arg0, ArgT... args) {
 
 _impl_.key_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.PairValue.key)
}
inline std::string* PairValue::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:tensorflow.PairValue.key)
  return _s;
}
inline const std::string& PairValue::_internal_key() const {
  return _impl_.key_.Get();
}
inline void PairValue::_internal_set_key(const std::string& value) {
  
  _impl_.key_.Set(value, GetArenaForAllocation());
}
inline std::string* PairValue::_internal_mutable_key() {
  
  return _impl_.key_.Mutable(GetArenaForAllocation());
}
inline std::string* PairValue::release_key() {
  // @@protoc_insertion_point(field_release:tensorflow.PairValue.key)
  return _impl_.key_.Release();
}
inline void PairValue::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  _impl_.key_.SetAllocated(key, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.key_.IsDefault()) {
    _impl_.key_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PairValue.key)
}

// .tensorflow.StructuredValue value = 2;
inline bool PairValue::_internal_has_value() const {
  return this != internal_default_instance() && _impl_.value_ != nullptr;
}
inline bool PairValue::has_value() const {
  return _internal_has_value();
}
inline void PairValue::clear_value() {
  if (GetArenaForAllocation() == nullptr && _impl_.value_ != nullptr) {
    delete _impl_.value_;
  }
  _impl_.value_ = nullptr;
}
inline const ::tensorflow::StructuredValue& PairValue::_internal_value() const {
  const ::tensorflow::StructuredValue* p = _impl_.value_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StructuredValue&>(
      ::tensorflow::_StructuredValue_default_instance_);
}
inline const ::tensorflow::StructuredValue& PairValue::value() const {
  // @@protoc_insertion_point(field_get:tensorflow.PairValue.value)
  return _internal_value();
}
inline void PairValue::unsafe_arena_set_allocated_value(
    ::tensorflow::StructuredValue* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.value_);
  }
  _impl_.value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.PairValue.value)
}
inline ::tensorflow::StructuredValue* PairValue::release_value() {
  
  ::tensorflow::StructuredValue* temp = _impl_.value_;
  _impl_.value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StructuredValue* PairValue::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:tensorflow.PairValue.value)
  
  ::tensorflow::StructuredValue* temp = _impl_.value_;
  _impl_.value_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* PairValue::_internal_mutable_value() {
  
  if (_impl_.value_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaForAllocation());
    _impl_.value_ = p;
  }
  return _impl_.value_;
}
inline ::tensorflow::StructuredValue* PairValue::mutable_value() {
  ::tensorflow::StructuredValue* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:tensorflow.PairValue.value)
  return _msg;
}
inline void PairValue::set_allocated_value(::tensorflow::StructuredValue* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.value_;
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(value);
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.value_ = value;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.PairValue.value)
}

// -------------------------------------------------------------------

// NamedTupleValue

// string name = 1;
inline void NamedTupleValue::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& NamedTupleValue::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedTupleValue.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void NamedTupleValue::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.NamedTupleValue.name)
}
inline std::string* NamedTupleValue::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedTupleValue.name)
  return _s;
}
inline const std::string& NamedTupleValue::_internal_name() const {
  return _impl_.name_.Get();
}
inline void NamedTupleValue::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* NamedTupleValue::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* NamedTupleValue::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.NamedTupleValue.name)
  return _impl_.name_.Release();
}
inline void NamedTupleValue::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.NamedTupleValue.name)
}

// repeated .tensorflow.PairValue values = 2;
inline int NamedTupleValue::_internal_values_size() const {
  return _impl_.values_.size();
}
inline int NamedTupleValue::values_size() const {
  return _internal_values_size();
}
inline void NamedTupleValue::clear_values() {
  _impl_.values_.Clear();
}
inline ::tensorflow::PairValue* NamedTupleValue::mutable_values(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.NamedTupleValue.values)
  return _impl_.values_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >*
NamedTupleValue::mutable_values() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.NamedTupleValue.values)
  return &_impl_.values_;
}
inline const ::tensorflow::PairValue& NamedTupleValue::_internal_values(int index) const {
  return _impl_.values_.Get(index);
}
inline const ::tensorflow::PairValue& NamedTupleValue::values(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.NamedTupleValue.values)
  return _internal_values(index);
}
inline ::tensorflow::PairValue* NamedTupleValue::_internal_add_values() {
  return _impl_.values_.Add();
}
inline ::tensorflow::PairValue* NamedTupleValue::add_values() {
  ::tensorflow::PairValue* _add = _internal_add_values();
  // @@protoc_insertion_point(field_add:tensorflow.NamedTupleValue.values)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::PairValue >&
NamedTupleValue::values() const {
  // @@protoc_insertion_point(field_list:tensorflow.NamedTupleValue.values)
  return _impl_.values_;
}

// -------------------------------------------------------------------

// TensorSpecProto

// string name = 1;
inline void TensorSpecProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& TensorSpecProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TensorSpecProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TensorSpecProto.name)
}
inline std::string* TensorSpecProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSpecProto.name)
  return _s;
}
inline const std::string& TensorSpecProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void TensorSpecProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* TensorSpecProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* TensorSpecProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorSpecProto.name)
  return _impl_.name_.Release();
}
inline void TensorSpecProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorSpecProto.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool TensorSpecProto::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool TensorSpecProto::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& TensorSpecProto::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& TensorSpecProto::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.shape)
  return _internal_shape();
}
inline void TensorSpecProto::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TensorSpecProto.shape)
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.TensorSpecProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* TensorSpecProto::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.TensorSpecProto.shape)
  return _msg;
}
inline void TensorSpecProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TensorSpecProto.shape)
}

// .tensorflow.DataType dtype = 3;
inline void TensorSpecProto::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType TensorSpecProto::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType TensorSpecProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.TensorSpecProto.dtype)
  return _internal_dtype();
}
inline void TensorSpecProto::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void TensorSpecProto::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.TensorSpecProto.dtype)
}

// -------------------------------------------------------------------

// BoundedTensorSpecProto

// string name = 1;
inline void BoundedTensorSpecProto::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& BoundedTensorSpecProto::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BoundedTensorSpecProto::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.BoundedTensorSpecProto.name)
}
inline std::string* BoundedTensorSpecProto::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.name)
  return _s;
}
inline const std::string& BoundedTensorSpecProto::_internal_name() const {
  return _impl_.name_.Get();
}
inline void BoundedTensorSpecProto::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* BoundedTensorSpecProto::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* BoundedTensorSpecProto::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.name)
  return _impl_.name_.Release();
}
inline void BoundedTensorSpecProto::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.name)
}

// .tensorflow.TensorShapeProto shape = 2;
inline bool BoundedTensorSpecProto::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool BoundedTensorSpecProto::has_shape() const {
  return _internal_has_shape();
}
inline const ::tensorflow::TensorShapeProto& BoundedTensorSpecProto::_internal_shape() const {
  const ::tensorflow::TensorShapeProto* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorShapeProto&>(
      ::tensorflow::_TensorShapeProto_default_instance_);
}
inline const ::tensorflow::TensorShapeProto& BoundedTensorSpecProto::shape() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.shape)
  return _internal_shape();
}
inline void BoundedTensorSpecProto::unsafe_arena_set_allocated_shape(
    ::tensorflow::TensorShapeProto* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BoundedTensorSpecProto.shape)
}
inline ::tensorflow::TensorShapeProto* BoundedTensorSpecProto::release_shape() {
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorShapeProto* BoundedTensorSpecProto::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.shape)
  
  ::tensorflow::TensorShapeProto* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorShapeProto* BoundedTensorSpecProto::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorShapeProto>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::tensorflow::TensorShapeProto* BoundedTensorSpecProto::mutable_shape() {
  ::tensorflow::TensorShapeProto* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.shape)
  return _msg;
}
inline void BoundedTensorSpecProto::set_allocated_shape(::tensorflow::TensorShapeProto* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.shape)
}

// .tensorflow.DataType dtype = 3;
inline void BoundedTensorSpecProto::clear_dtype() {
  _impl_.dtype_ = 0;
}
inline ::tensorflow::DataType BoundedTensorSpecProto::_internal_dtype() const {
  return static_cast< ::tensorflow::DataType >(_impl_.dtype_);
}
inline ::tensorflow::DataType BoundedTensorSpecProto::dtype() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.dtype)
  return _internal_dtype();
}
inline void BoundedTensorSpecProto::_internal_set_dtype(::tensorflow::DataType value) {
  
  _impl_.dtype_ = value;
}
inline void BoundedTensorSpecProto::set_dtype(::tensorflow::DataType value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:tensorflow.BoundedTensorSpecProto.dtype)
}

// .tensorflow.TensorProto minimum = 4;
inline bool BoundedTensorSpecProto::_internal_has_minimum() const {
  return this != internal_default_instance() && _impl_.minimum_ != nullptr;
}
inline bool BoundedTensorSpecProto::has_minimum() const {
  return _internal_has_minimum();
}
inline const ::tensorflow::TensorProto& BoundedTensorSpecProto::_internal_minimum() const {
  const ::tensorflow::TensorProto* p = _impl_.minimum_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& BoundedTensorSpecProto::minimum() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.minimum)
  return _internal_minimum();
}
inline void BoundedTensorSpecProto::unsafe_arena_set_allocated_minimum(
    ::tensorflow::TensorProto* minimum) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.minimum_);
  }
  _impl_.minimum_ = minimum;
  if (minimum) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BoundedTensorSpecProto.minimum)
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::release_minimum() {
  
  ::tensorflow::TensorProto* temp = _impl_.minimum_;
  _impl_.minimum_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::unsafe_arena_release_minimum() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.minimum)
  
  ::tensorflow::TensorProto* temp = _impl_.minimum_;
  _impl_.minimum_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::_internal_mutable_minimum() {
  
  if (_impl_.minimum_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.minimum_ = p;
  }
  return _impl_.minimum_;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::mutable_minimum() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_minimum();
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.minimum)
  return _msg;
}
inline void BoundedTensorSpecProto::set_allocated_minimum(::tensorflow::TensorProto* minimum) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.minimum_);
  }
  if (minimum) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(minimum));
    if (message_arena != submessage_arena) {
      minimum = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, minimum, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.minimum_ = minimum;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.minimum)
}

// .tensorflow.TensorProto maximum = 5;
inline bool BoundedTensorSpecProto::_internal_has_maximum() const {
  return this != internal_default_instance() && _impl_.maximum_ != nullptr;
}
inline bool BoundedTensorSpecProto::has_maximum() const {
  return _internal_has_maximum();
}
inline const ::tensorflow::TensorProto& BoundedTensorSpecProto::_internal_maximum() const {
  const ::tensorflow::TensorProto* p = _impl_.maximum_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::TensorProto&>(
      ::tensorflow::_TensorProto_default_instance_);
}
inline const ::tensorflow::TensorProto& BoundedTensorSpecProto::maximum() const {
  // @@protoc_insertion_point(field_get:tensorflow.BoundedTensorSpecProto.maximum)
  return _internal_maximum();
}
inline void BoundedTensorSpecProto::unsafe_arena_set_allocated_maximum(
    ::tensorflow::TensorProto* maximum) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.maximum_);
  }
  _impl_.maximum_ = maximum;
  if (maximum) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.BoundedTensorSpecProto.maximum)
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::release_maximum() {
  
  ::tensorflow::TensorProto* temp = _impl_.maximum_;
  _impl_.maximum_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::unsafe_arena_release_maximum() {
  // @@protoc_insertion_point(field_release:tensorflow.BoundedTensorSpecProto.maximum)
  
  ::tensorflow::TensorProto* temp = _impl_.maximum_;
  _impl_.maximum_ = nullptr;
  return temp;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::_internal_mutable_maximum() {
  
  if (_impl_.maximum_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::TensorProto>(GetArenaForAllocation());
    _impl_.maximum_ = p;
  }
  return _impl_.maximum_;
}
inline ::tensorflow::TensorProto* BoundedTensorSpecProto::mutable_maximum() {
  ::tensorflow::TensorProto* _msg = _internal_mutable_maximum();
  // @@protoc_insertion_point(field_mutable:tensorflow.BoundedTensorSpecProto.maximum)
  return _msg;
}
inline void BoundedTensorSpecProto::set_allocated_maximum(::tensorflow::TensorProto* maximum) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.maximum_);
  }
  if (maximum) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(maximum));
    if (message_arena != submessage_arena) {
      maximum = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, maximum, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.maximum_ = maximum;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.BoundedTensorSpecProto.maximum)
}

// -------------------------------------------------------------------

// TypeSpecProto

// .tensorflow.TypeSpecProto.TypeSpecClass type_spec_class = 1;
inline void TypeSpecProto::clear_type_spec_class() {
  _impl_.type_spec_class_ = 0;
}
inline ::tensorflow::TypeSpecProto_TypeSpecClass TypeSpecProto::_internal_type_spec_class() const {
  return static_cast< ::tensorflow::TypeSpecProto_TypeSpecClass >(_impl_.type_spec_class_);
}
inline ::tensorflow::TypeSpecProto_TypeSpecClass TypeSpecProto::type_spec_class() const {
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.type_spec_class)
  return _internal_type_spec_class();
}
inline void TypeSpecProto::_internal_set_type_spec_class(::tensorflow::TypeSpecProto_TypeSpecClass value) {
  
  _impl_.type_spec_class_ = value;
}
inline void TypeSpecProto::set_type_spec_class(::tensorflow::TypeSpecProto_TypeSpecClass value) {
  _internal_set_type_spec_class(value);
  // @@protoc_insertion_point(field_set:tensorflow.TypeSpecProto.type_spec_class)
}

// .tensorflow.StructuredValue type_state = 2;
inline bool TypeSpecProto::_internal_has_type_state() const {
  return this != internal_default_instance() && _impl_.type_state_ != nullptr;
}
inline bool TypeSpecProto::has_type_state() const {
  return _internal_has_type_state();
}
inline void TypeSpecProto::clear_type_state() {
  if (GetArenaForAllocation() == nullptr && _impl_.type_state_ != nullptr) {
    delete _impl_.type_state_;
  }
  _impl_.type_state_ = nullptr;
}
inline const ::tensorflow::StructuredValue& TypeSpecProto::_internal_type_state() const {
  const ::tensorflow::StructuredValue* p = _impl_.type_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::tensorflow::StructuredValue&>(
      ::tensorflow::_StructuredValue_default_instance_);
}
inline const ::tensorflow::StructuredValue& TypeSpecProto::type_state() const {
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.type_state)
  return _internal_type_state();
}
inline void TypeSpecProto::unsafe_arena_set_allocated_type_state(
    ::tensorflow::StructuredValue* type_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.type_state_);
  }
  _impl_.type_state_ = type_state;
  if (type_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:tensorflow.TypeSpecProto.type_state)
}
inline ::tensorflow::StructuredValue* TypeSpecProto::release_type_state() {
  
  ::tensorflow::StructuredValue* temp = _impl_.type_state_;
  _impl_.type_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::tensorflow::StructuredValue* TypeSpecProto::unsafe_arena_release_type_state() {
  // @@protoc_insertion_point(field_release:tensorflow.TypeSpecProto.type_state)
  
  ::tensorflow::StructuredValue* temp = _impl_.type_state_;
  _impl_.type_state_ = nullptr;
  return temp;
}
inline ::tensorflow::StructuredValue* TypeSpecProto::_internal_mutable_type_state() {
  
  if (_impl_.type_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::tensorflow::StructuredValue>(GetArenaForAllocation());
    _impl_.type_state_ = p;
  }
  return _impl_.type_state_;
}
inline ::tensorflow::StructuredValue* TypeSpecProto::mutable_type_state() {
  ::tensorflow::StructuredValue* _msg = _internal_mutable_type_state();
  // @@protoc_insertion_point(field_mutable:tensorflow.TypeSpecProto.type_state)
  return _msg;
}
inline void TypeSpecProto::set_allocated_type_state(::tensorflow::StructuredValue* type_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.type_state_;
  }
  if (type_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(type_state);
    if (message_arena != submessage_arena) {
      type_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, type_state, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.type_state_ = type_state;
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TypeSpecProto.type_state)
}

// string type_spec_class_name = 3;
inline void TypeSpecProto::clear_type_spec_class_name() {
  _impl_.type_spec_class_name_.ClearToEmpty();
}
inline const std::string& TypeSpecProto::type_spec_class_name() const {
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.type_spec_class_name)
  return _internal_type_spec_class_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TypeSpecProto::set_type_spec_class_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.type_spec_class_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.TypeSpecProto.type_spec_class_name)
}
inline std::string* TypeSpecProto::mutable_type_spec_class_name() {
  std::string* _s = _internal_mutable_type_spec_class_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.TypeSpecProto.type_spec_class_name)
  return _s;
}
inline const std::string& TypeSpecProto::_internal_type_spec_class_name() const {
  return _impl_.type_spec_class_name_.Get();
}
inline void TypeSpecProto::_internal_set_type_spec_class_name(const std::string& value) {
  
  _impl_.type_spec_class_name_.Set(value, GetArenaForAllocation());
}
inline std::string* TypeSpecProto::_internal_mutable_type_spec_class_name() {
  
  return _impl_.type_spec_class_name_.Mutable(GetArenaForAllocation());
}
inline std::string* TypeSpecProto::release_type_spec_class_name() {
  // @@protoc_insertion_point(field_release:tensorflow.TypeSpecProto.type_spec_class_name)
  return _impl_.type_spec_class_name_.Release();
}
inline void TypeSpecProto::set_allocated_type_spec_class_name(std::string* type_spec_class_name) {
  if (type_spec_class_name != nullptr) {
    
  } else {
    
  }
  _impl_.type_spec_class_name_.SetAllocated(type_spec_class_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.type_spec_class_name_.IsDefault()) {
    _impl_.type_spec_class_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.TypeSpecProto.type_spec_class_name)
}

// int32 num_flat_components = 4;
inline void TypeSpecProto::clear_num_flat_components() {
  _impl_.num_flat_components_ = 0;
}
inline int32_t TypeSpecProto::_internal_num_flat_components() const {
  return _impl_.num_flat_components_;
}
inline int32_t TypeSpecProto::num_flat_components() const {
  // @@protoc_insertion_point(field_get:tensorflow.TypeSpecProto.num_flat_components)
  return _internal_num_flat_components();
}
inline void TypeSpecProto::_internal_set_num_flat_components(int32_t value) {
  
  _impl_.num_flat_components_ = value;
}
inline void TypeSpecProto::set_num_flat_components(int32_t value) {
  _internal_set_num_flat_components(value);
  // @@protoc_insertion_point(field_set:tensorflow.TypeSpecProto.num_flat_components)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::tensorflow::TypeSpecProto_TypeSpecClass> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::tensorflow::TypeSpecProto_TypeSpecClass>() {
  return ::tensorflow::TypeSpecProto_TypeSpecClass_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fstruct_2eproto
