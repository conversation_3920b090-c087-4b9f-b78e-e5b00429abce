// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: tsl/profiler/protobuf/profiler_service.proto

#include "tsl/profiler/protobuf/profiler_service.pb.h"
#include "tsl/profiler/protobuf/profiler_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace tensorflow {


namespace grpc {

class MockProfilerServiceStub : public ProfilerService::StubInterface {
 public:
  MOCK_METHOD3(Profile, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::tensorflow::ProfileResponse* response));
  MOCK_METHOD3(AsyncProfileRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>*(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncProfileRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::ProfileResponse>*(::grpc::ClientContext* context, const ::tensorflow::ProfileRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Terminate, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::tensorflow::TerminateResponse* response));
  MOCK_METHOD3(AsyncTerminateRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>*(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncTerminateRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::TerminateResponse>*(::grpc::ClientContext* context, const ::tensorflow::TerminateRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Monitor, ::grpc::Status(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::tensorflow::MonitorResponse* response));
  MOCK_METHOD3(AsyncMonitorRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>*(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncMonitorRaw, ::grpc::ClientAsyncResponseReaderInterface< ::tensorflow::MonitorResponse>*(::grpc::ClientContext* context, const ::tensorflow::MonitorRequest& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc

} // namespace tensorflow

