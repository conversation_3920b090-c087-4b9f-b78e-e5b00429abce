// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: tensorflow/core/protobuf/device_filters.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021009 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
namespace tensorflow {
class ClusterDeviceFilters;
struct ClusterDeviceFiltersDefaultTypeInternal;
extern ClusterDeviceFiltersDefaultTypeInternal _ClusterDeviceFilters_default_instance_;
class JobDeviceFilters;
struct JobDeviceFiltersDefaultTypeInternal;
extern JobDeviceFiltersDefaultTypeInternal _JobDeviceFilters_default_instance_;
class JobDeviceFilters_TasksEntry_DoNotUse;
struct JobDeviceFilters_TasksEntry_DoNotUseDefaultTypeInternal;
extern JobDeviceFilters_TasksEntry_DoNotUseDefaultTypeInternal _JobDeviceFilters_TasksEntry_DoNotUse_default_instance_;
class TaskDeviceFilters;
struct TaskDeviceFiltersDefaultTypeInternal;
extern TaskDeviceFiltersDefaultTypeInternal _TaskDeviceFilters_default_instance_;
}  // namespace tensorflow
PROTOBUF_NAMESPACE_OPEN
template<> ::tensorflow::ClusterDeviceFilters* Arena::CreateMaybeMessage<::tensorflow::ClusterDeviceFilters>(Arena*);
template<> ::tensorflow::JobDeviceFilters* Arena::CreateMaybeMessage<::tensorflow::JobDeviceFilters>(Arena*);
template<> ::tensorflow::JobDeviceFilters_TasksEntry_DoNotUse* Arena::CreateMaybeMessage<::tensorflow::JobDeviceFilters_TasksEntry_DoNotUse>(Arena*);
template<> ::tensorflow::TaskDeviceFilters* Arena::CreateMaybeMessage<::tensorflow::TaskDeviceFilters>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace tensorflow {

// ===================================================================

class TaskDeviceFilters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.TaskDeviceFilters) */ {
 public:
  inline TaskDeviceFilters() : TaskDeviceFilters(nullptr) {}
  ~TaskDeviceFilters() override;
  explicit PROTOBUF_CONSTEXPR TaskDeviceFilters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TaskDeviceFilters(const TaskDeviceFilters& from);
  TaskDeviceFilters(TaskDeviceFilters&& from) noexcept
    : TaskDeviceFilters() {
    *this = ::std::move(from);
  }

  inline TaskDeviceFilters& operator=(const TaskDeviceFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline TaskDeviceFilters& operator=(TaskDeviceFilters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TaskDeviceFilters& default_instance() {
    return *internal_default_instance();
  }
  static inline const TaskDeviceFilters* internal_default_instance() {
    return reinterpret_cast<const TaskDeviceFilters*>(
               &_TaskDeviceFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TaskDeviceFilters& a, TaskDeviceFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(TaskDeviceFilters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TaskDeviceFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TaskDeviceFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TaskDeviceFilters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TaskDeviceFilters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TaskDeviceFilters& from) {
    TaskDeviceFilters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TaskDeviceFilters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.TaskDeviceFilters";
  }
  protected:
  explicit TaskDeviceFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDeviceFiltersFieldNumber = 1,
  };
  // repeated string device_filters = 1;
  int device_filters_size() const;
  private:
  int _internal_device_filters_size() const;
  public:
  void clear_device_filters();
  const std::string& device_filters(int index) const;
  std::string* mutable_device_filters(int index);
  void set_device_filters(int index, const std::string& value);
  void set_device_filters(int index, std::string&& value);
  void set_device_filters(int index, const char* value);
  void set_device_filters(int index, const char* value, size_t size);
  std::string* add_device_filters();
  void add_device_filters(const std::string& value);
  void add_device_filters(std::string&& value);
  void add_device_filters(const char* value);
  void add_device_filters(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& device_filters() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_device_filters();
  private:
  const std::string& _internal_device_filters(int index) const;
  std::string* _internal_add_device_filters();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.TaskDeviceFilters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> device_filters_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};
// -------------------------------------------------------------------

class JobDeviceFilters_TasksEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<JobDeviceFilters_TasksEntry_DoNotUse, 
    int32_t, ::tensorflow::TaskDeviceFilters,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<JobDeviceFilters_TasksEntry_DoNotUse, 
    int32_t, ::tensorflow::TaskDeviceFilters,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  JobDeviceFilters_TasksEntry_DoNotUse();
  explicit PROTOBUF_CONSTEXPR JobDeviceFilters_TasksEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit JobDeviceFilters_TasksEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const JobDeviceFilters_TasksEntry_DoNotUse& other);
  static const JobDeviceFilters_TasksEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const JobDeviceFilters_TasksEntry_DoNotUse*>(&_JobDeviceFilters_TasksEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};

// -------------------------------------------------------------------

class JobDeviceFilters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.JobDeviceFilters) */ {
 public:
  inline JobDeviceFilters() : JobDeviceFilters(nullptr) {}
  ~JobDeviceFilters() override;
  explicit PROTOBUF_CONSTEXPR JobDeviceFilters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  JobDeviceFilters(const JobDeviceFilters& from);
  JobDeviceFilters(JobDeviceFilters&& from) noexcept
    : JobDeviceFilters() {
    *this = ::std::move(from);
  }

  inline JobDeviceFilters& operator=(const JobDeviceFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline JobDeviceFilters& operator=(JobDeviceFilters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const JobDeviceFilters& default_instance() {
    return *internal_default_instance();
  }
  static inline const JobDeviceFilters* internal_default_instance() {
    return reinterpret_cast<const JobDeviceFilters*>(
               &_JobDeviceFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(JobDeviceFilters& a, JobDeviceFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(JobDeviceFilters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(JobDeviceFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  JobDeviceFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<JobDeviceFilters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const JobDeviceFilters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const JobDeviceFilters& from) {
    JobDeviceFilters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(JobDeviceFilters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.JobDeviceFilters";
  }
  protected:
  explicit JobDeviceFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kTasksFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // map<int32, .tensorflow.TaskDeviceFilters> tasks = 2;
  int tasks_size() const;
  private:
  int _internal_tasks_size() const;
  public:
  void clear_tasks();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >&
      _internal_tasks() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >*
      _internal_mutable_tasks();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >&
      tasks() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >*
      mutable_tasks();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:tensorflow.JobDeviceFilters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::MapField<
        JobDeviceFilters_TasksEntry_DoNotUse,
        int32_t, ::tensorflow::TaskDeviceFilters,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> tasks_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};
// -------------------------------------------------------------------

class ClusterDeviceFilters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:tensorflow.ClusterDeviceFilters) */ {
 public:
  inline ClusterDeviceFilters() : ClusterDeviceFilters(nullptr) {}
  ~ClusterDeviceFilters() override;
  explicit PROTOBUF_CONSTEXPR ClusterDeviceFilters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ClusterDeviceFilters(const ClusterDeviceFilters& from);
  ClusterDeviceFilters(ClusterDeviceFilters&& from) noexcept
    : ClusterDeviceFilters() {
    *this = ::std::move(from);
  }

  inline ClusterDeviceFilters& operator=(const ClusterDeviceFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClusterDeviceFilters& operator=(ClusterDeviceFilters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ClusterDeviceFilters& default_instance() {
    return *internal_default_instance();
  }
  static inline const ClusterDeviceFilters* internal_default_instance() {
    return reinterpret_cast<const ClusterDeviceFilters*>(
               &_ClusterDeviceFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(ClusterDeviceFilters& a, ClusterDeviceFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(ClusterDeviceFilters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClusterDeviceFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ClusterDeviceFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ClusterDeviceFilters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ClusterDeviceFilters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ClusterDeviceFilters& from) {
    ClusterDeviceFilters::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClusterDeviceFilters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "tensorflow.ClusterDeviceFilters";
  }
  protected:
  explicit ClusterDeviceFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kJobsFieldNumber = 1,
  };
  // repeated .tensorflow.JobDeviceFilters jobs = 1;
  int jobs_size() const;
  private:
  int _internal_jobs_size() const;
  public:
  void clear_jobs();
  ::tensorflow::JobDeviceFilters* mutable_jobs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >*
      mutable_jobs();
  private:
  const ::tensorflow::JobDeviceFilters& _internal_jobs(int index) const;
  ::tensorflow::JobDeviceFilters* _internal_add_jobs();
  public:
  const ::tensorflow::JobDeviceFilters& jobs(int index) const;
  ::tensorflow::JobDeviceFilters* add_jobs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >&
      jobs() const;

  // @@protoc_insertion_point(class_scope:tensorflow.ClusterDeviceFilters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters > jobs_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TaskDeviceFilters

// repeated string device_filters = 1;
inline int TaskDeviceFilters::_internal_device_filters_size() const {
  return _impl_.device_filters_.size();
}
inline int TaskDeviceFilters::device_filters_size() const {
  return _internal_device_filters_size();
}
inline void TaskDeviceFilters::clear_device_filters() {
  _impl_.device_filters_.Clear();
}
inline std::string* TaskDeviceFilters::add_device_filters() {
  std::string* _s = _internal_add_device_filters();
  // @@protoc_insertion_point(field_add_mutable:tensorflow.TaskDeviceFilters.device_filters)
  return _s;
}
inline const std::string& TaskDeviceFilters::_internal_device_filters(int index) const {
  return _impl_.device_filters_.Get(index);
}
inline const std::string& TaskDeviceFilters::device_filters(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.TaskDeviceFilters.device_filters)
  return _internal_device_filters(index);
}
inline std::string* TaskDeviceFilters::mutable_device_filters(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.TaskDeviceFilters.device_filters)
  return _impl_.device_filters_.Mutable(index);
}
inline void TaskDeviceFilters::set_device_filters(int index, const std::string& value) {
  _impl_.device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::set_device_filters(int index, std::string&& value) {
  _impl_.device_filters_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::set_device_filters(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_filters_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::set_device_filters(int index, const char* value, size_t size) {
  _impl_.device_filters_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:tensorflow.TaskDeviceFilters.device_filters)
}
inline std::string* TaskDeviceFilters::_internal_add_device_filters() {
  return _impl_.device_filters_.Add();
}
inline void TaskDeviceFilters::add_device_filters(const std::string& value) {
  _impl_.device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::add_device_filters(std::string&& value) {
  _impl_.device_filters_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::add_device_filters(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.device_filters_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:tensorflow.TaskDeviceFilters.device_filters)
}
inline void TaskDeviceFilters::add_device_filters(const char* value, size_t size) {
  _impl_.device_filters_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:tensorflow.TaskDeviceFilters.device_filters)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TaskDeviceFilters::device_filters() const {
  // @@protoc_insertion_point(field_list:tensorflow.TaskDeviceFilters.device_filters)
  return _impl_.device_filters_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TaskDeviceFilters::mutable_device_filters() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.TaskDeviceFilters.device_filters)
  return &_impl_.device_filters_;
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// JobDeviceFilters

// string name = 1;
inline void JobDeviceFilters::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& JobDeviceFilters::name() const {
  // @@protoc_insertion_point(field_get:tensorflow.JobDeviceFilters.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void JobDeviceFilters::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:tensorflow.JobDeviceFilters.name)
}
inline std::string* JobDeviceFilters::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:tensorflow.JobDeviceFilters.name)
  return _s;
}
inline const std::string& JobDeviceFilters::_internal_name() const {
  return _impl_.name_.Get();
}
inline void JobDeviceFilters::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* JobDeviceFilters::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* JobDeviceFilters::release_name() {
  // @@protoc_insertion_point(field_release:tensorflow.JobDeviceFilters.name)
  return _impl_.name_.Release();
}
inline void JobDeviceFilters::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:tensorflow.JobDeviceFilters.name)
}

// map<int32, .tensorflow.TaskDeviceFilters> tasks = 2;
inline int JobDeviceFilters::_internal_tasks_size() const {
  return _impl_.tasks_.size();
}
inline int JobDeviceFilters::tasks_size() const {
  return _internal_tasks_size();
}
inline void JobDeviceFilters::clear_tasks() {
  _impl_.tasks_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >&
JobDeviceFilters::_internal_tasks() const {
  return _impl_.tasks_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >&
JobDeviceFilters::tasks() const {
  // @@protoc_insertion_point(field_map:tensorflow.JobDeviceFilters.tasks)
  return _internal_tasks();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >*
JobDeviceFilters::_internal_mutable_tasks() {
  return _impl_.tasks_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::tensorflow::TaskDeviceFilters >*
JobDeviceFilters::mutable_tasks() {
  // @@protoc_insertion_point(field_mutable_map:tensorflow.JobDeviceFilters.tasks)
  return _internal_mutable_tasks();
}

// -------------------------------------------------------------------

// ClusterDeviceFilters

// repeated .tensorflow.JobDeviceFilters jobs = 1;
inline int ClusterDeviceFilters::_internal_jobs_size() const {
  return _impl_.jobs_.size();
}
inline int ClusterDeviceFilters::jobs_size() const {
  return _internal_jobs_size();
}
inline void ClusterDeviceFilters::clear_jobs() {
  _impl_.jobs_.Clear();
}
inline ::tensorflow::JobDeviceFilters* ClusterDeviceFilters::mutable_jobs(int index) {
  // @@protoc_insertion_point(field_mutable:tensorflow.ClusterDeviceFilters.jobs)
  return _impl_.jobs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >*
ClusterDeviceFilters::mutable_jobs() {
  // @@protoc_insertion_point(field_mutable_list:tensorflow.ClusterDeviceFilters.jobs)
  return &_impl_.jobs_;
}
inline const ::tensorflow::JobDeviceFilters& ClusterDeviceFilters::_internal_jobs(int index) const {
  return _impl_.jobs_.Get(index);
}
inline const ::tensorflow::JobDeviceFilters& ClusterDeviceFilters::jobs(int index) const {
  // @@protoc_insertion_point(field_get:tensorflow.ClusterDeviceFilters.jobs)
  return _internal_jobs(index);
}
inline ::tensorflow::JobDeviceFilters* ClusterDeviceFilters::_internal_add_jobs() {
  return _impl_.jobs_.Add();
}
inline ::tensorflow::JobDeviceFilters* ClusterDeviceFilters::add_jobs() {
  ::tensorflow::JobDeviceFilters* _add = _internal_add_jobs();
  // @@protoc_insertion_point(field_add:tensorflow.ClusterDeviceFilters.jobs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::tensorflow::JobDeviceFilters >&
ClusterDeviceFilters::jobs() const {
  // @@protoc_insertion_point(field_list:tensorflow.ClusterDeviceFilters.jobs)
  return _impl_.jobs_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace tensorflow

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_tensorflow_2fcore_2fprotobuf_2fdevice_5ffilters_2eproto
